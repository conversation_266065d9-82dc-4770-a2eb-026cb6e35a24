import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'

export class Maintenance {
  @ApiProperty({ type: Number, required: true })
  id: number

  @ApiProperty({ type: String, required: true, nullable: false })
  @IsString()
  @IsNotEmpty()
  datECode: string

  @ApiProperty({ type: Number, required: true, nullable: true })
  @IsNumber()
  @IsOptional()
  km: number | null

  @ApiProperty({ type: String, required: true, nullable: true })
  @IsString()
  @IsNotEmpty()
  description: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsString()
  partNumber?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsString()
  amount?: string | null

  @ApiProperty({ type: Number, format: 'double',  required: false, nullable: true })
  @IsOptional()
  @IsNumber()
  price?: number | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsString()
  workTime?: string | null
}

export class UploadFileResponse {
  @ApiProperty({ type: String })
  message: string
}

export class FindByDatECodeResponse extends Maintenance {}

export class GetAllMaintenancesResponse extends Maintenance {}
