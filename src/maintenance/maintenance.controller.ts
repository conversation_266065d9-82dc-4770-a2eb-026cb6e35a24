import { FileInterceptor } from '@nestjs/platform-express'
import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  Get,
  Param,
  BadRequestException,
  UseGuards
} from '@nestjs/common'
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger'
import { Readable } from 'stream'
import multer from 'multer'

import { MaintenanceService } from './maintenance.service'

import { AuthGuard } from '../common/guards/auth.guard'

import { SwaggerDoc } from 'src/common/decorators/swagger-doc.decorator'

import { maintenanceTag } from 'src/common/constants/swagger'
import { FindByDatECodeResponse, GetAllMaintenancesResponse, UploadFileResponse } from './types'

@ApiTags(maintenanceTag)
@Controller(maintenanceTag)
@UseGuards(AuthGuard)
export class MaintenanceController {
  constructor(private maintenanceService: MaintenanceService) {}

  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Maintenance data file',
    type: 'multipart/form-data',
    required: true,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The file to upload'
        }
      },
      required: ['file']
    }
  })
  @SwaggerDoc({
    isAuth: false,
    summary: 'Upload File',
    typeResponse: UploadFileResponse
  })
  @Post()
  @UseInterceptors(
    FileInterceptor('file', {
      storage: multer.memoryStorage(),
      limits: {
        fileSize: 50 * 1024 * 1024 // 50 MB
      }
    })
  )
  async uploadFile(@UploadedFile() file: Express.Multer.File): Promise<UploadFileResponse> {
    if (!file) {
      throw new BadRequestException('No file provided')
    }

    const bufferStream = Readable.from(file.buffer)
    return this.maintenanceService.processMaintenanceData(bufferStream)
  }

  @SwaggerDoc({
    isAuth: false,
    summary: 'Search by ECode',
    typeResponse: [FindByDatECodeResponse]
  })
  @Get('/:datECode')
  async findByDatECode(@Param('datECode') datECode: string): Promise<FindByDatECodeResponse[]> {
    return this.maintenanceService.findByDatECode(datECode)
  }

  @SwaggerDoc({
    isAuth: false,
    summary: 'Get all maintenances',
    typeResponse: [GetAllMaintenancesResponse]
  })
  @Get('/')
  async getAllMaintenances(): Promise<GetAllMaintenancesResponse[]> {
    return this.maintenanceService.getAllMaintenances()
  }
}
