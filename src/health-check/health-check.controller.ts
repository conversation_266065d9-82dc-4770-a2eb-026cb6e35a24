import { HealthCheck, HealthCheckService } from '@nestjs/terminus'
import { Controller, Get } from '@nestjs/common'
import { DatabaseHealthIndicator } from 'src/health-check/indicators/db-health.indicator'
import { DatApiHealthIndicator } from 'src/health-check/indicators/dat-api-health.indicator'
import { MyClaimProxyApiHealthIndicator } from 'src/health-check/indicators/my-claim-proxy-api-health.indicator'
import { ProxyApiHealthIndicator } from 'src/health-check/indicators/proxy-api-health.decorator'
import { WedatApiHealthIndicator } from 'src/health-check/indicators/wedat-api-health.indicator'

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private readonly dbConnectHealthIndicator: DatabaseHealthIndicator,
    private readonly datApiHealthIndicator: DatApiHealthIndicator,
    private readonly myClaimProxyApiHealthIndicator: MyClaimProxyApiHealthIndicator,
    private readonly proxyApiHealthIndicator: ProxyApiHealthIndicator,
    private readonly wedatApiHealthIndicator: WedatApiHealthIndicator
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.dbConnectHealthIndicator.isHealthy('database'),
      () => this.datApiHealthIndicator.isHealthy('dat'),
      () => this.myClaimProxyApiHealthIndicator.isHealthy('myClaimProxy'),
      // () => this.proxyApiHealthIndicator.isHealthy('proxy'),
      () => this.wedatApiHealthIndicator.isHealthy('wedat')
    ])
  }
}
