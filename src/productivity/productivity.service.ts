import { Injectable, UnauthorizedException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { ClaimEventEntity } from 'src/productivity/claim-event.entity'
import {
  ClaimEventUpsertDto,
  ComparisonBasedOnKindQueryDto,
  GetChartQueryDto,
  GetOwnChartQueryDto,
  SingleYearComparisonDataDto
} from 'src/productivity/productivity.dto'
import { AuthService } from 'src/auth/auth.service'

@Injectable()
export class ProductivityService {
  constructor(
    @InjectRepository(ClaimEventEntity)
    private claimEventRepository: Repository<ClaimEventEntity>,
    private authService: AuthService
  ) {}

  async upsertClaimEvent(data: ClaimEventUpsertDto, token: string): Promise<ClaimEventEntity | null> {
    const customerNumber = this.getUserFromToken(token)
    const { contractId, customerNumber: potentialCustomerNumber, ...rest } = data
    const update = { ...rest, customerNumber: customerNumber || potentialCustomerNumber }
    const potentialAlreadyStoredClaimEvent = await this.claimEventRepository.findOne({ where: { contractId } })
    if (potentialAlreadyStoredClaimEvent) {
      await this.claimEventRepository.update({ contractId }, { ...update })
    } else {
      const claimEvent = this.claimEventRepository.create(data)
      await this.claimEventRepository.save(claimEvent)
    }
    const upsertedClaimEvent = await this.claimEventRepository.findOne({ where: { contractId } })
    return upsertedClaimEvent
  }

  async getSingleYearComparisonBasedOnKindWithDifferences(year: number, data: ComparisonBasedOnKindQueryDto) {
    const mappedExpertData: SingleYearComparisonDataDto[] = []
    const mappedCollaboratorData: SingleYearComparisonDataDto[] = []
    const differenceMap: Record<string, SingleYearComparisonDataDto> = {}

    const expertDataBasedOnAssignmentKind = await this.getSingleYearComparisonBasedOnAssignmentKind(year)
    const expertDataBasedOnAssignmentKindAndInsuranceType =
      await this.getSingleYearComparisonBasedOnAssignmentKindAndInsuranceType(year)

    expertDataBasedOnAssignmentKind.map(expert => {
      const expertData = {
        group: expert.assignmentKind,
        nrAssignedClaims: +expert.nrAssignedClaims,
        percentNotClosed: +expert.percentNotClosed,
        percentReturned: +expert.percentReturned,
        percentPl: +expert.percentPl,
        avgTotalPl: +expert.avgTotalPl,
        avgTimeToReturnHrs: +expert.avgTimeToReturnHrs,
        percentAgreed: +expert.percentAgreed,
        avgTotalAgreed: +expert.avgTotalAgreed,
        avgTotalNetCorrected: +expert.avgTotalNetCorrected
      }
      mappedExpertData.push(expertData)

      differenceMap[expertData.group] = {
        group: expert.assignmentKind,
        nrAssignedClaims: -expert.nrAssignedClaims,
        percentNotClosed: -expert.percentNotClosed,
        percentReturned: -expert.percentReturned,
        percentPl: -expert.percentPl,
        avgTotalPl: -expert.avgTotalPl,
        avgTimeToReturnHrs: -expert.avgTimeToReturnHrs,
        percentAgreed: -expert.percentAgreed,
        avgTotalAgreed: -expert.avgTotalAgreed,
        avgTotalNetCorrected: -expert.avgTotalNetCorrected
      }
    })

    expertDataBasedOnAssignmentKindAndInsuranceType.map(expert => {
      const expertData = {
        group: `${expert.assignmentKind}/${expert.insuranceType}`,
        nrAssignedClaims: +expert.nrAssignedClaims,
        percentNotClosed: +expert.percentNotClosed,
        percentReturned: +expert.percentReturned,
        percentPl: +expert.percentPl,
        avgTotalPl: +expert.avgTotalPl,
        avgTimeToReturnHrs: +expert.avgTimeToReturnHrs,
        percentAgreed: +expert.percentAgreed,
        avgTotalAgreed: +expert.avgTotalAgreed,
        avgTotalNetCorrected: +expert.avgTotalNetCorrected
      }
      mappedExpertData.push(expertData)

      differenceMap[expertData.group] = {
        group: `${expert.assignmentKind}/${expert.insuranceType}`,
        nrAssignedClaims: -expert.nrAssignedClaims,
        percentNotClosed: -expert.percentNotClosed,
        percentReturned: -expert.percentReturned,
        percentPl: -expert.percentPl,
        avgTotalPl: -expert.avgTotalPl,
        avgTimeToReturnHrs: -expert.avgTimeToReturnHrs,
        percentAgreed: -expert.percentAgreed,
        avgTotalAgreed: -expert.avgTotalAgreed,
        avgTotalNetCorrected: -expert.avgTotalNetCorrected
      }
    })

    const handleCollaboratorDifferenceCalculationAndPush = (
      collaboratorData: SingleYearComparisonDataDto,
      collab: any
    ) => {
      mappedCollaboratorData.push(collaboratorData)
      differenceMap[collaboratorData.group] = {
        group: collab.assignmentKind,
        nrAssignedClaims: +collab.nrAssignedClaims + differenceMap[collaboratorData.group].nrAssignedClaims,
        percentNotClosed: +collab.percentNotClosed + differenceMap[collaboratorData.group].percentNotClosed,
        percentReturned: +collab.percentReturned + differenceMap[collaboratorData.group].percentReturned,
        percentPl: +collab.percentPl + differenceMap[collaboratorData.group].percentPl,
        avgTotalPl: +collab.avgTotalPl + differenceMap[collaboratorData.group].avgTotalPl,
        avgTimeToReturnHrs: +collab.avgTimeToReturnHrs + differenceMap[collaboratorData.group].avgTimeToReturnHrs,
        percentAgreed: +collab.percentAgreed + differenceMap[collaboratorData.group].percentAgreed,
        avgTotalAgreed: +collab.avgTotalAgreed + differenceMap[collaboratorData.group].avgTotalAgreed,
        avgTotalNetCorrected: +collab.avgTotalNetCorrected + differenceMap[collaboratorData.group].avgTotalNetCorrected
      }
    }

    if (data.collaboratorIds) {
      const collaboratorDataBasedOnAssignmentKind = await this.getSingleYearComparisonBasedOnAssignmentKind(year, data)
      const collaboratorDataBasedOnAssignmentKindAndInsuranceType =
        await this.getSingleYearComparisonBasedOnAssignmentKindAndInsuranceType(year, data)
      collaboratorDataBasedOnAssignmentKind.map(collab => {
        const collaboratorData = {
          group: collab.assignmentKind,
          collaboratorId: collab.collaboratorId,
          nrAssignedClaims: +collab.nrAssignedClaims,
          percentNotClosed: +collab.percentNotClosed,
          percentReturned: +collab.percentReturned,
          percentPl: +collab.percentPl,
          avgTotalPl: +collab.avgTotalPl,
          avgTimeToReturnHrs: +collab.avgTimeToReturnHrs,
          percentAgreed: +collab.percentAgreed,
          avgTotalAgreed: +collab.avgTotalAgreed,
          avgTotalNetCorrected: +collab.avgTotalNetCorrected
        }
        handleCollaboratorDifferenceCalculationAndPush(collaboratorData, collab)
      })

      collaboratorDataBasedOnAssignmentKindAndInsuranceType.map(collab => {
        const collaboratorData = {
          group: `${collab.assignmentKind}/${collab.insuranceType}`,
          collaboratorId: collab.collaboratorId,
          nrAssignedClaims: +collab.nrAssignedClaims,
          percentNotClosed: +collab.percentNotClosed,
          percentReturned: +collab.percentReturned,
          percentPl: +collab.percentPl,
          avgTotalPl: +collab.avgTotalPl,
          avgTimeToReturnHrs: +collab.avgTimeToReturnHrs,
          percentAgreed: +collab.percentAgreed,
          avgTotalAgreed: +collab.avgTotalAgreed,
          avgTotalNetCorrected: +collab.avgTotalNetCorrected
        }
        handleCollaboratorDifferenceCalculationAndPush(collaboratorData, collab)
      })
    }

    return {
      expert: mappedExpertData,
      collaborator: mappedCollaboratorData,
      difference: Object.values(differenceMap)
    }
  }

  async getSingleYearComparisonBasedOnAssignmentKindAndInsuranceType(
    year: number,
    data: ComparisonBasedOnKindQueryDto = {}
  ) {
    let selectQuery = this.claimEventRepository
      .createQueryBuilder('claim')
      .select([
        'claim."assignmentKind" AS "assignmentKind"',
        'claim."insuranceType" AS "insuranceType"',
        ...(data.collaboratorIds ? ['claim."collaboratorId" AS "collaboratorId"'] : []),
        'COUNT(*)::int AS "nrAssignedClaims"',
        'ROUND(100.0 * SUM(CASE WHEN claim."closedClaim" = false THEN 1 ELSE 0 END) / COUNT(*), 2) AS "percentNotClosed"',
        'ROUND(100.0 * SUM(CASE WHEN claim."returned" = true THEN 1 ELSE 0 END) / COUNT(*), 2) AS "percentReturned"',
        'ROUND(100.0 * SUM(CASE WHEN claim."isPlClaim" = true THEN 1 ELSE 0 END) / COUNT(*), 2) AS "percentPl"',
        'ROUND(AVG(CASE WHEN claim."isPlClaim" = true THEN claim."totalNetCorrected"::numeric END), 2) AS "avgTotalPl"',
        'ROUND((AVG(EXTRACT(EPOCH FROM (claim."returnedDate"::timestamp - claim."createdDate"::timestamp)) / 3600))::numeric, 2) AS "avgTimeToReturnHrs"',
        'ROUND(100.0 * SUM(CASE WHEN claim."isAgreed" = true THEN 1 ELSE 0 END) / COUNT(*), 2) AS "percentAgreed"',
        'ROUND(AVG(claim."totalAgreed"::numeric), 2) AS "avgTotalAgreed"',
        'ROUND(AVG(claim."totalNetCorrected"::numeric), 2) AS "avgTotalNetCorrected"'
      ])
      .where('EXTRACT(YEAR FROM claim."createdDate") = :year', { year })

    if (data.collaboratorIds) {
      selectQuery = selectQuery.andWhere('claim."collaboratorId" IN (:...collaboratorIds)', {
        collaboratorIds: Array.isArray(data.collaboratorIds) ? data.collaboratorIds : [data.collaboratorIds]
      })
    }

    selectQuery = selectQuery.groupBy('claim."assignmentKind"').addGroupBy('claim."insuranceType"')

    if (data.collaboratorIds) {
      selectQuery = selectQuery.addGroupBy('claim."collaboratorId"')
    }

    selectQuery = selectQuery.orderBy('claim."assignmentKind"')

    if (data.collaboratorIds) {
      selectQuery = selectQuery.addOrderBy('claim."collaboratorId"')
    }

    return selectQuery.getRawMany()
  }

  async getSingleYearComparisonBasedOnAssignmentKind(year: number, data: ComparisonBasedOnKindQueryDto = {}) {
    let selectQuery = this.claimEventRepository
      .createQueryBuilder('claim')
      .select([
        'claim."assignmentKind" AS "assignmentKind"',
        ...(data.collaboratorIds ? ['claim."collaboratorId" AS "collaboratorId"'] : []),
        'COUNT(*)::int AS "nrAssignedClaims"',
        'ROUND(100.0 * SUM(CASE WHEN claim."closedClaim" = false THEN 1 ELSE 0 END) / COUNT(*), 2) AS "percentNotClosed"',
        'ROUND(100.0 * SUM(CASE WHEN claim."returned" = true THEN 1 ELSE 0 END) / COUNT(*), 2) AS "percentReturned"',
        'ROUND(100.0 * SUM(CASE WHEN claim."isPlClaim" = true THEN 1 ELSE 0 END) / COUNT(*), 2) AS "percentPl"',
        'ROUND(AVG(CASE WHEN claim."isPlClaim" = true THEN claim."totalNetCorrected"::numeric END), 2) AS "avgTotalPl"',
        'ROUND((AVG(EXTRACT(EPOCH FROM (claim."returnedDate"::timestamp - claim."createdDate"::timestamp)) / 3600))::numeric, 2) AS "avgTimeToReturnHrs"',
        'ROUND(100.0 * SUM(CASE WHEN claim."isAgreed" = true THEN 1 ELSE 0 END) / COUNT(*), 2) AS "percentAgreed"',
        'ROUND(AVG(claim."totalAgreed")::numeric, 2) AS "avgTotalAgreed"',
        'ROUND(AVG(claim."totalNetCorrected")::numeric, 2) AS "avgTotalNetCorrected"'
      ])
      .where(`TO_CHAR(claim."createdDate", 'YYYY') = :year`, { year: String(year) })

    if (data.collaboratorIds) {
      selectQuery = selectQuery.andWhere('claim."collaboratorId" IN (:...collaboratorIds)', {
        collaboratorIds: Array.isArray(data.collaboratorIds) ? data.collaboratorIds : [data.collaboratorIds]
      })
    }

    selectQuery = selectQuery.groupBy('claim."assignmentKind"')

    if (data.collaboratorIds) {
      selectQuery = selectQuery.addGroupBy('claim."collaboratorId"')
    }

    selectQuery = selectQuery.orderBy('claim."assignmentKind"')

    if (data.collaboratorIds) {
      selectQuery = selectQuery.addOrderBy('claim."collaboratorId"')
    }

    return selectQuery.getRawMany()
  }

  async getChartData(data: GetChartQueryDto) {
    const { year, collaboratorIds } = data
    const yearly = await this.getCountOfClaimsPerMonth(year)
    const withCollaborators = await this.getCountOfClaimsPerMonth(year, { collaboratorIds })
    return {
      yearly,
      withCollaborators
    }
  }

  async getOwnChartData(data: GetOwnChartQueryDto, token: string) {
    const customerNumber = this.getUserFromToken(token)
    const { year } = data
    const yearly = await this.getCountOfClaimsPerMonth(year, { customerNumber })
    return {
      yearly
    }
  }

  async getCountOfClaimsPerMonth(
    years: number[] | number = [new Date().getFullYear() - 1, new Date().getFullYear()],
    params: { collaboratorIds?: string[]; customerNumber?: number } = {}
  ) {
    const { collaboratorIds, customerNumber } = params
    let selectQuery = this.claimEventRepository
      .createQueryBuilder('claim')
      .select([
        'EXTRACT(MONTH FROM claim."createdDate") AS "month"',
        'EXTRACT(YEAR FROM claim."createdDate") AS "year"',
        ...(collaboratorIds && collaboratorIds.length > 0 ? ['claim."collaboratorId" AS "collaboratorId"'] : []),
        'COUNT(*)::int AS "claimCount"'
      ])
      .where('EXTRACT(YEAR FROM claim."createdDate") IN (:...years)', { years: Array.isArray(years) ? years : [years] })

    if (customerNumber) {
      selectQuery = selectQuery.andWhere('claim."customerNumber" = :customerNumber', { customerNumber })
    }

    if (collaboratorIds && collaboratorIds.length > 0) {
      selectQuery = selectQuery.andWhere('claim."collaboratorId" IN (:...collaboratorIds)', { collaboratorIds })
    }

    selectQuery = selectQuery.groupBy('month').addGroupBy('year')

    if (collaboratorIds && collaboratorIds.length > 0) {
      selectQuery = selectQuery.addGroupBy('claim."collaboratorId"')
    }
    return selectQuery.orderBy('month').addOrderBy('year').getRawMany()
  }

  async deleteProductivityBasedOnContractId(contractId: string): Promise<void> {
    await this.claimEventRepository.delete({ contractId: +contractId })
  }

  private getUserFromToken(token: string) {
    if (!token) {
      throw new UnauthorizedException('User not authorized')
    }
    const decodedData = this.authService.decodeToken(token)

    if (!decodedData.customerNumber) {
      throw new UnauthorizedException('User not authorized')
    }

    return +decodedData.customerNumber
  }
}
