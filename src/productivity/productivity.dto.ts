import { <PERSON><PERSON><PERSON><PERSON>, IsBoolean, <PERSON>Date, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'

import { BaseModel } from 'src/common/types/common'

export class ClaimEventUpsertDto extends BaseModel {
  @IsNumber()
  @ApiProperty({ type: Number, required: true })
  contractId: number

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty({ type: String, required: false })
  collaboratorId?: string

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty({ type: String, required: false })
  expertId?: string

  @IsNumber()
  @IsOptional()
  @ApiProperty({ type: Number, required: false })
  customerNumber?: number

  @IsNumber()
  @IsOptional()
  @ApiProperty({ type: Number, required: false })
  totalNetCorrected?: number

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @<PERSON>piProperty({ type: String, required: true })
  insuranceType: string

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty({ type: String, required: false })
  expertiseType?: string

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty({ type: String, required: false })
  assignmentKind?: string

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ type: Boolean, required: false })
  assignedClaim?: boolean

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ type: Boolean, required: false })
  closedClaim?: boolean

  @IsNumber()
  @IsOptional()
  @ApiProperty({ type: Number, required: false })
  totalWorktime?: number

  @IsNumber()
  @IsOptional()
  @ApiProperty({ type: Number, required: false })
  totalPartsPrice?: number

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ type: Boolean, required: false })
  returned?: boolean

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ type: Boolean, required: false })
  isNegative?: boolean

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ type: Boolean, required: false })
  isPlClaim?: boolean

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ type: Boolean, required: false })
  isAgreed?: boolean

  @IsNumber()
  @IsOptional()
  @ApiProperty({ type: Number, required: false })
  totalAgreed?: number

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @ApiProperty({ type: String, format: 'date-time', nullable: true, required: false })
  createdDate?: Date

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @ApiProperty({ type: String, format: 'date-time', nullable: true, required: false })
  closedDate?: Date

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @ApiProperty({ type: String, format: 'date-time', nullable: true, required: false })
  returnedDate?: Date

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @ApiProperty({ type: String, format: 'date-time', nullable: true, required: false })
  inspectionDate?: Date

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @ApiProperty({ type: String, format: 'date-time', nullable: true, required: false })
  appointmentDate?: Date
}

export class ComparisonBasedOnKindQueryDto {
  @IsOptional()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  @ApiProperty({ required: false })
  collaboratorIds?: string[]
}

export class SingleYearComparisonDataDto {
  @IsString()
  @ApiProperty({ type: String, required: true })
  group: string

  @IsNumber()
  @ApiProperty({ type: Number, required: true })
  nrAssignedClaims: number

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty({ type: String, required: false })
  collaboratorId?: string

  @IsNumber()
  @ApiProperty({ type: Number, required: true })
  percentNotClosed: number

  @IsNumber()
  @ApiProperty({ type: Number, required: true })
  percentReturned: number

  @IsNumber()
  @ApiProperty({ type: Number, required: true })
  percentPl: number

  @IsNumber()
  @ApiProperty({ type: Number, required: true })
  avgTotalPl: number

  @IsNumber()
  @ApiProperty({ type: Number, required: true })
  avgTimeToReturnHrs: number

  @IsNumber()
  @ApiProperty({ type: Number, required: true })
  percentAgreed: number

  @IsNumber()
  @ApiProperty({ type: Number, required: true })
  avgTotalAgreed: number

  @IsNumber()
  @ApiProperty({ type: Number, required: true })
  avgTotalNetCorrected: number
}

export class GetChartQueryDto {
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  @ApiProperty({ required: false })
  @IsOptional()
  year?: number[]

  @IsOptional()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  @ApiProperty({ required: false })
  collaboratorIds?: string[]
}

export class GetOwnChartQueryDto {
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  @ApiProperty({ required: false })
  @IsOptional()
  year?: number[]
}

export class UpsertClaimEventResponse extends ClaimEventUpsertDto {}

export class GetSingleYearComparisonBasedOnKindResponse {
  @ApiProperty({ type: [SingleYearComparisonDataDto] })
  expert: SingleYearComparisonDataDto[]

  @ApiProperty({ type: [SingleYearComparisonDataDto] })
  collaborator: SingleYearComparisonDataDto[]

  @ApiProperty({ type: [SingleYearComparisonDataDto] })
  difference: SingleYearComparisonDataDto[]
}
