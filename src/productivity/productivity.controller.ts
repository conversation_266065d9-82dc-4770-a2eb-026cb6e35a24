import { Body, Controller, Delete, Get, Headers, Param, Post, Query, UseGuards } from '@nestjs/common'
import { ProductivityService } from 'src/productivity/productivity.service'
import {
  ClaimEventUpsertDto,
  ComparisonBasedOnKindQueryDto,
  GetChartQueryDto,
  GetOwnChartQueryDto,
  GetSingleYearComparisonBasedOnKindResponse,
  UpsertClaimEventResponse
} from 'src/productivity/productivity.dto'
import { SwaggerDoc } from 'src/common/decorators/swagger-doc.decorator'
import { AuthGuard } from 'src/common/guards/auth.guard'
import { HeadersNames } from 'src/common/constants/headers-names'
import { ApiTags } from '@nestjs/swagger'
import { productivityTag } from 'src/common/constants/swagger'

@ApiTags(productivityTag)
@Controller(productivityTag)
export class ProductivityController {
  constructor(private readonly productivityService: ProductivityService) {}

  @Post('/claim/event')
  @SwaggerDoc({
    isAuth: true,
    summary: 'Create claim event',
    typeResponse: UpsertClaimEventResponse
  })
  @UseGuards(AuthGuard)
  upsertClaimEvent(
    @Body() data: ClaimEventUpsertDto,
    @Headers() headers: HeadersNames
  ): Promise<UpsertClaimEventResponse | null> {
    return this.productivityService.upsertClaimEvent(data, headers[HeadersNames.DatAuthToken])
  }

  @Get('/chart')
  @SwaggerDoc({
    isAuth: true,
    summary: 'get claim count comparison'
  })
  @UseGuards(AuthGuard)
  async getComparison(@Query() data: GetChartQueryDto) {
    return this.productivityService.getChartData(data)
  }

  @Get('/chart/own')
  @SwaggerDoc({
    isAuth: true,
    summary: 'get own claim count comparison'
  })
  @UseGuards(AuthGuard)
  async getOwnComparison(@Query() data: GetOwnChartQueryDto, @Headers() headers: HeadersNames) {
    return this.productivityService.getOwnChartData(data, headers[HeadersNames.DatAuthToken])
  }

  @Get('/:year/comparison')
  @SwaggerDoc({
    isAuth: true,
    summary: 'get single year comparison based on kind',
    typeResponse: GetSingleYearComparisonBasedOnKindResponse
  })
  @UseGuards(AuthGuard)
  async getSingleYearComparisonBasedOnKind(
    @Param('year') year: number,
    @Query() data: ComparisonBasedOnKindQueryDto
  ): Promise<GetSingleYearComparisonBasedOnKindResponse> {
    return this.productivityService.getSingleYearComparisonBasedOnKindWithDifferences(year, data)
  }

  @Delete('/:contractId')
  @SwaggerDoc({
    isAuth: true,
    summary: 'Delete claim event based on contractId'
  })
  @UseGuards(AuthGuard)
  async deleteProductivityBasedOnContractId(@Param('contractId') contractId: string) {
    return this.productivityService.deleteProductivityBasedOnContractId(contractId)
  }
}
