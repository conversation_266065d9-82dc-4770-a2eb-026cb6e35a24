import { BadRequestException, Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import OpenAI from 'openai'
import * as DAT5 from 'api2dat5'

import { ConfigService } from 'src/config/config.service'
import { AiCommandEntity } from 'src/ai/persistence/ai-command.entity'
import { AiCommandDto, OrchestratorBodyDto, OrchestratorResponseDto } from 'src/ai/ai.dto'
import { UserConfigService } from 'src/user-config/user-config.service'
import { getNestedField } from 'src/common/utils/getNestedField'
import { MyClaimService } from 'src/my-claim/my-claim.service'
import { getOrchestratorAssessment } from 'src/ai/utils/orchestrator.util'
import { DEFAULT_ORCHESTRATOR_CONFIGURATION } from 'src/ai/constants'
import { HeadersNames } from 'src/common/constants/headers-names'

@Injectable()
export class AiService {
  private readonly openaiClient: OpenAI

  constructor(
    private readonly configService: ConfigService,
    private readonly userConfigService: UserConfigService,
    private readonly myClaimService: MyClaimService,
    @InjectRepository(AiCommandEntity)
    private aiCommandRepository: Repository<AiCommandEntity>
  ) {
    this.openaiClient = new OpenAI({
      apiKey: this.configService.openAiApiKey,
      dangerouslyAllowBrowser: true
    })
  }

  async createCommand(data: AiCommandDto) {
    const commandInstance = this.aiCommandRepository.create(data)
    return this.aiCommandRepository.save(commandInstance)
  }

  getByIdOrSearchKey(idOrSearchKey: string) {
    return this.aiCommandRepository.findOne(this.createQueryBasedOnIdOrSearchKey(idOrSearchKey))
  }

  async deleteByIdOrSearchKey(idOrSearchKey: string) {
    const entity = await this.getAndValidateBasedOnIdOrSearchKey(idOrSearchKey)
    return this.aiCommandRepository.remove(entity)
  }

  async updateByIdOrSearchKey(idOrSearchKey: string, data: AiCommandDto) {
    return (
      (
        await this.aiCommandRepository
          .createQueryBuilder()
          .update()
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          .set(data)
          .where(this.createQueryBasedOnIdOrSearchKey(idOrSearchKey))
          .returning('*')
          .execute()
      ).raw[0]
    )
  }

  async executeCommand(idOrSearchKey: string, input: string) {
    const entity = await this.getAndValidateBasedOnIdOrSearchKey(idOrSearchKey)
    const completionResponse = await this.openaiClient.chat.completions.create({
      model: 'gpt-4o',
      messages: [...entity.tasks, { role: 'user', content: input }] as OpenAI.ChatCompletionMessageParam[],
      tools: entity.tools as OpenAI.ChatCompletionTool[]
    })
    return completionResponse.choices.map(choice => {
      const { content, tool_calls, refusal } = choice.message
      return {
        content,
        refusal,
        calls: tool_calls?.map(({ function: functionCall }) => ({
          name: functionCall.name,
          arguments: JSON.parse(functionCall.arguments)
        }))
      }
    })
  }

  async getOrchestratorResponse(data: OrchestratorBodyDto, token: string): Promise<OrchestratorResponseDto> {
    const { customerNumber, claimId, AIResponseField = 'AIJsonResponse' } = data
    let orchestratorConfig = DEFAULT_ORCHESTRATOR_CONFIGURATION

    if (customerNumber) {
      const potentialOrchestratorConfig = getNestedField(
        (await this.userConfigService.getUserConfig(customerNumber))?.config || {},
        ['templates', 'default', 'products', 'fast-track', 'settings', 'orchestrator']
      )

      if (potentialOrchestratorConfig) {
        orchestratorConfig = potentialOrchestratorConfig
      }
    }
    const contractRequest = await DAT5.MyClaimExternalService.getContract(
      {
        contractId: claimId,
        isWithHistoricalCalculations: false
      },
      {
        credentials: {
          [HeadersNames.DatAuthToken]: token
        }
      }
    )
    const contract = contractRequest.responseObj.return

    if (!contract) {
      throw new BadRequestException('Could not find claim associated with that claimId')
    }

    const potentialEntryArray = contract.customTemplateData?.entry
    const entries = Array.isArray(potentialEntryArray) ? potentialEntryArray : [potentialEntryArray]
    const AIJsonResponse = ((entries as any[]) || [])?.find(data => data?.key === AIResponseField)?.value
    if (!AIJsonResponse) {
      throw new BadRequestException('Could not find necessary JSON field inside the entries')
    }

    const result = getOrchestratorAssessment({
      AIResult: AIJsonResponse._value ? JSON.parse(AIJsonResponse._value) : AIJsonResponse,
      orchestrator: orchestratorConfig
    })

    if (!result) {
      throw new BadRequestException('Could not calculate correct response, check configuration or contract data')
    }
    return {
      claimId,
      orchestratorResponse: result
    }
  }

  private createQueryBasedOnIdOrSearchKey(idOrSearchKey: string) {
    return {
      where: [...(isNaN(idOrSearchKey as any) ? [] : [{ id: +idOrSearchKey }]), { key: idOrSearchKey }]
    }
  }

  private async getAndValidateBasedOnIdOrSearchKey(idOrSearchKey: string) {
    const potentialInstance = await this.getByIdOrSearchKey(idOrSearchKey)
    if (!potentialInstance) {
      throw new BadRequestException('There is not AI command with that id or key')
    }
    return potentialInstance
  }
}
