import { Body, Controller, Delete, Get, Headers, Param, Patch, Post, UseGuards } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'

import { SwaggerDoc } from 'src/common/decorators/swagger-doc.decorator'
import { AiService } from 'src/ai/ai.service'
import { AiCommandDto, ExecuteAiCommandDto, OrchestratorBodyDto, OrchestratorResponseDto } from 'src/ai/ai.dto'
import { AuthGuard } from 'src/common/guards/auth.guard'
import { HeadersNames } from 'src/common/constants/headers-names'

@Controller('ai')
@ApiTags('AI')
export class AiController {
  constructor(private readonly aiService: AiService) {}

  @Post('/command')
  @SwaggerDoc({
    isAuth: false,
    summary: 'Creates a command',
    bodySchema: AiCommandDto
  })
  createCommand(@Body() data: AiCommandDto) {
    return this.aiService.createCommand(data)
  }

  @Post('/command/:idOrSearchKey')
  @SwaggerDoc({
    isAuth: false,
    summary: 'Executes a command based on user input and search key',
    bodySchema: ExecuteAiCommandDto
  })
  executeAiCommand(@Param('idOrSearchKey') idOrSearchKey: string, @Body() data: ExecuteAiCommandDto) {
    return this.aiService.executeCommand(idOrSearchKey, data.input)
  }

  @Get('/command/:idOrSearchKey')
  @SwaggerDoc({
    isAuth: false,
    summary: 'Returns Ai Command with tasks',
    typeResponse: AiCommandDto
  })
  getCommandByIdOr(@Param('idOrSearchKey') idOrSearchKey: string) {
    return this.aiService.getByIdOrSearchKey(idOrSearchKey)
  }

  @Delete('/command/:idOrSearchKey')
  @SwaggerDoc({
    isAuth: false,
    summary: 'Delete Ai Command',
    typeResponse: AiCommandDto
  })
  deleteCommandById(@Param('idOrSearchKey') idOrSearchKey: string) {
    return this.aiService.deleteByIdOrSearchKey(idOrSearchKey)
  }

  @Patch('/command/:idOrSearchKey')
  @SwaggerDoc({
    isAuth: false,
    summary: 'Updates Ai Command',
    typeResponse: AiCommandDto
  })
  patchCommand(@Param('idOrSearchKey') idOrSearchKey: string, @Body() data: AiCommandDto) {
    return this.aiService.updateByIdOrSearchKey(idOrSearchKey, data)
  }

  @Post('/orchestrator')
  @SwaggerDoc({
    isAuth: true,
    summary: 'Orchestrator logic',
    typeResponse: OrchestratorResponseDto
  })
  @UseGuards(AuthGuard)
  orchestrator(@Body() data: OrchestratorBodyDto, @Headers() headers: HeadersNames) {
    return this.aiService.getOrchestratorResponse(data, headers[HeadersNames.DatAuthToken])
  }
}
