import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm'

export enum DEEPLINK_STATUS {
  CREATED = 'created',
  OPENED = 'opened',
  EXPIRED = 'expired',
  DELETED = 'deleted',
  COMPLETED = 'completed'
}

@Entity({
  name: 'deeplink'
})
export class DeeplinkEntity {
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  customerNumber: string

  @Column()
  contractId: string

  @Column()
  scenarioId: string

  @Column({ unique: true })
  hash: string

  @Column({ default: 21600 })
  expireAt: number

  @Column()
  uniqueId: string

  @CreateDateColumn()
  createdAt: Date

  @Column({ type: 'timestamp', nullable: true })
  openedAt: Date

  @Column({ type: 'timestamp', nullable: true })
  completedAt: Date

  @Column({ type: 'timestamp', nullable: true })
  deletedAt: Date

  @Column({ default: false })
  isCompleted: boolean

  @Column({
    type: 'enum',
    enum: DEEPLINK_STATUS,
    default: DEEPLINK_STATUS.CREATED
  })
  currentStatus: DEEPLINK_STATUS

  @Column({ nullable: true })
  phone: string

  @Column({ nullable: true })
  email: string

  @Column({ nullable: true })
  profileName: string

  @Column({ default: 0 })
  imagesCount: number

  @Column({
    type: 'jsonb',
    default: () => `'{}'::jsonb`
  })
  requestInfo: Record<string, string>
}
