import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { v4 as uuidv4 } from 'uuid'
import { Response } from 'express'

import * as DAT5 from 'api2dat5'
import { AuthService } from 'src/auth/auth.service'
import { UserConfigService } from 'src/user-config/user-config.service'
import { DeeplinkEntity, DEEPLINK_STATUS } from './persistance/deeplink.entity'
import { UpdateDeeplinkDto, GetDeeplinksQueryDto, GenerateDeeplinkDto } from './dto'
import { applyQueryFilters } from 'src/common/utils/queryFilters'
import { encrypt } from './utils/encryptDeeplink'
import { convertHoursToSeconds } from './utils/convertHoursToSeconds'
import { formatDateForSavingToMemo } from './utils/formatDateForSavingToMemo'
import { calculateExpireScheduleTime } from './utils/calculateExpireScheduleTime'
import { timestampToSeconds, secondsToTimestamp } from './utils/timeConversion'
import { ExpireScheduleTime } from './types/expireScheduleTime'
import {
  DEFAULT_COUNTRY,
  DEFAULT_EXPIRE_TIME_IN_HOURS,
  DEFAULT_PAGE,
  DEFAULT_PAGE_LIMIT,
  DEFAULT_SORT_COLUMN,
  DEFAULT_SORT_ORDER,
  MAX_LENGTH_FILE_NAME
} from './constants'
import { format } from 'date-fns'

@Injectable()
export class DeeplinkService {
  constructor(
    @InjectRepository(DeeplinkEntity)
    private readonly deeplinkRepository: Repository<DeeplinkEntity>,
    private readonly authService: AuthService,
    private readonly userConfigService: UserConfigService
  ) {}

  private DEEPLINK_TECH_USER = 'deepLinkUserTest'
  private DEEPLINK_TECH_PASSWORD = 'deepLinkUser12!@'
  private getCredentials(customerNumber: string) {
    return {
      customerNumber,
      user: this.DEEPLINK_TECH_USER,
      password: this.DEEPLINK_TECH_PASSWORD
    }
  }

  async generateDeepLink(data: GenerateDeeplinkDto) {
    const { customerNumber, contractId, scenarioId, phone, email, requestInfo = {}, profileName } = data
    const { scenariosConfig, globalConfig } = await this.getConfig(customerNumber)

    const uniqueId = uuidv4()
    const encryptedPath = encrypt(`cn${customerNumber}cid${contractId}sid${scenarioId}${uniqueId}`)
    let expireAt
    if (data.expireAt) {
      expireAt = timestampToSeconds(+data.expireAt)
    } else {
      const { expireTime, expireScheduleTime } = this.getExpireTimeForScenario(
        scenariosConfig,
        scenarioId,
        globalConfig
      )
      expireAt = expireScheduleTime || expireTime
    }

    await this.deeplinkRepository.save({
      customerNumber,
      contractId,
      scenarioId,
      expireAt,
      uniqueId,
      createdAt: new Date(),
      hash: encryptedPath,
      currentStatus: DEEPLINK_STATUS.CREATED,
      phone,
      email,
      requestInfo,
      profileName
    })

    return {
      path: encryptedPath,
      expireAt,
      expireDate: new Date(Date.now() + secondsToTimestamp(expireAt))
    }
  }

  private async getConfig(customerNumber: string): Promise<{
    globalConfig: DAT.Configuration | undefined
    scenariosConfig: DAT.Deeplink.DeeplinkConfiguration
  }> {
    try {
      const userConfig = await this.userConfigService.getUserConfigOnAuth(+customerNumber)
      const scenariosConfig = userConfig?.fields?.scenarios?.scenarios as DAT.Deeplink.DeeplinkConfiguration
      if (!scenariosConfig) throw new BadRequestException('Scenarios configuration not found')

      return {
        globalConfig: userConfig?.config,
        scenariosConfig
      }
    } catch (error) {
      throw new BadRequestException('Failed to fetch user configuration')
    }
  }

  private getExpireTimeForScenario(
    scenariosConfig: DAT.Deeplink.DeeplinkConfiguration,
    scenarioId: string,
    globalConfig?: DAT.Configuration
  ) {
    const templates = globalConfig?.templates
    const defaultCountry = templates?.default?.settings?.country
    const country =
      defaultCountry ||
      (templates && Object.values(templates).find(template => !!template?.settings?.country)?.settings?.country) ||
      DEFAULT_COUNTRY

    const scenarioExpireScheduleTime = scenariosConfig?.scenarios?.[scenarioId]?.settings
      ?.expireScheduleTime as ExpireScheduleTime
    const defaultExpireScheduleTime = scenariosConfig?.scenarios?.default?.settings
      ?.expireScheduleTime as ExpireScheduleTime
    const expireScheduleTime = scenarioExpireScheduleTime || defaultExpireScheduleTime
    const calculatedSeconds =
      expireScheduleTime && calculateExpireScheduleTime({ ...expireScheduleTime, locale: country })

    const scenarioExpireDate = scenariosConfig?.scenarios?.[scenarioId]?.settings?.expireDate
    const defaultExpireDate = scenariosConfig?.scenarios?.default?.settings?.expireDate || DEFAULT_EXPIRE_TIME_IN_HOURS
    const expireTime = convertHoursToSeconds(scenarioExpireDate) || convertHoursToSeconds(defaultExpireDate)

    return {
      expireTime: Number.parseInt(expireTime.toString()),
      expireScheduleTime: calculatedSeconds
    }
  }

  async authorize(urlEncrypted: string) {
    const foundDeeplink = await this.findDataByHash(urlEncrypted)
    const { customerNumber, contractId, createdAt, expireAt: timeExpireAt, scenarioId } = foundDeeplink
    const isExpired = this.isDeeplinkExpired(createdAt, timeExpireAt)
    const { scenariosConfig, globalConfig } = await this.getConfig(customerNumber)
    const { accessToken, refreshToken, expireAt } = await this.authUser(customerNumber)

    if (isExpired) {
      foundDeeplink.currentStatus = DEEPLINK_STATUS.EXPIRED
      await this.deeplinkRepository.save(foundDeeplink)
      this.handleUpdateContract(scenariosConfig, scenarioId, DEEPLINK_STATUS.EXPIRED, +contractId, accessToken)
    }

    // if status deleted, then send only deeplink configuration and current status
    // else send full auth response
    // For the BID system, even for expired deeplinks, authorization is required
    if (foundDeeplink.currentStatus === DEEPLINK_STATUS.DELETED) {
      return {
        deeplinkConfiguration: scenariosConfig,
        status: foundDeeplink.currentStatus
      }
    } else {
      if (
        foundDeeplink.currentStatus !== DEEPLINK_STATUS.EXPIRED &&
        foundDeeplink.currentStatus !== DEEPLINK_STATUS.COMPLETED
      ) {
        foundDeeplink.openedAt = new Date()
        foundDeeplink.currentStatus = DEEPLINK_STATUS.OPENED
        await this.deeplinkRepository.save(foundDeeplink)
        this.handleUpdateContract(scenariosConfig, scenarioId, DEEPLINK_STATUS.OPENED, +contractId, accessToken)
      }

      return {
        contractId,
        token: accessToken,
        refreshToken,
        expireAt,
        configuration: globalConfig,
        deeplinkConfiguration: scenariosConfig,
        scenarioId,
        status: foundDeeplink.currentStatus,
        ...(foundDeeplink.profileName ? { profileName: foundDeeplink.profileName } : {})
      }
    }
  }

  private isDeeplinkExpired(createdAt: Date, expireAtInSeconds: number): boolean {
    const createdAtDate = createdAt.getTime()
    const expireTime = createdAtDate + secondsToTimestamp(expireAtInSeconds)
    const currentDate = Date.now()

    return currentDate > expireTime
  }

  private async authUser(customerNumber: string) {
    return this.authService.generateToken(this.getCredentials(customerNumber))
  }

  private async handleUpdateContract(
    scenariosConfig: DAT.DeeplinkScenarios['scenarios'] | undefined,
    scenarioId: string,
    statusType: 'opened' | 'finalized' | 'expired',
    contractId: number,
    token: string
  ) {
    const options: DAT5.Options = {
      credentials: {
        'dat-authorizationtoken': token
      }
    }

    await this.handleUpdateDeeplinkMemoFieldInContract(options, statusType, contractId)
    await this.handleUpdateContractStatus(scenariosConfig, options, scenarioId, statusType, contractId)
  }

  private async handleUpdateDeeplinkMemoFieldInContract(
    options: DAT5.Options,
    statusType: 'opened' | 'finalized' | 'expired',
    contractId: number
  ) {
    try {
      const contract = (
        await DAT5.MyClaimExternalService.getContract(
          { contractId: +contractId, isWithHistoricalCalculations: true },
          options
        )
      ).responseObj['return']

      const currentDate = new Date()
      const deeplinkStatusExistingValue = contract?.customTemplateData.entry?.find(e => e.key === 'deepLinkResponse')
        ?.value?._value
      const parsedDeepLinkMemoField = deeplinkStatusExistingValue ? JSON.parse(deeplinkStatusExistingValue) : {}
      const updatedDeepLinkMemoField = JSON.stringify({
        ...parsedDeepLinkMemoField,
        [statusType]: formatDateForSavingToMemo(currentDate)
      })

      if (contract?.Dossier) {
        await DAT5.MyClaimExternalService.createOrUpdateContract(
          {
            contractId,
            contractType: contract.complexTemplateData?.[0]?._attr_templateType,
            networkType: contract.networkType,
            templateId: contract.complexTemplateData?.[0]?._attr_templateId,
            Dossier: {
              Country: contract.Dossier.Country,
              Language: contract.Dossier.Language
            },
            templateData: {
              entry: [
                {
                  key: 'deepLinkResponse',
                  value: { _value: updatedDeepLinkMemoField, _attr_type: 'xs:string' }
                }
              ]
            }
          },
          options
        )
      }
    } catch (e) {
      console.error(`${new Date().toISOString()} ERROR_UPDATE_CONTRACT_MEMO_FIELD ERROR_MESSAGE: ${e.message}`)
    }
  }

  private async handleUpdateContractStatus(
    scenariosConfig: DAT.DeeplinkScenarios['scenarios'] | undefined,
    options: DAT5.Options,
    scenarioId: string,
    statusType: 'opened' | 'finalized' | 'expired',
    contractId: number
  ) {
    try {
      const deeplinkStatuses =
        scenariosConfig?.scenarios[scenarioId]?.settings?.statuses ||
        scenariosConfig?.scenarios?.default?.settings?.statuses ||
        {}

      const deeplinkStatus = deeplinkStatuses[statusType]

      if (deeplinkStatus) {
        const { responseObj } = await DAT5.MyClaimExternalService.getPossibleContractStatusTransitions(
          { contractId },
          options
        )

        const statusInInternalList = responseObj.PossibleContractStatusTransitions?.find(
          item => item.name === deeplinkStatus
        )

        if (statusInInternalList) {
          await DAT5.MyClaimExternalService.changeContractStatus(
            {
              templateData: {},
              contractId,
              statusType:
                statusInInternalList.statusType === 'custom' ? deeplinkStatus : statusInInternalList.statusType
            },
            options
          )
        }
      }
    } catch (e) {
      console.error(`${new Date().toISOString()} ERROR_UPDATE_CONTRACT_STATUS ERROR_MESSAGE: ${e.message}`)
    }
  }

  async findDataByHash(hash: string): Promise<DeeplinkEntity> {
    const deeplinkRecord = await this.deeplinkRepository.findOne({ where: { hash } })
    if (!deeplinkRecord) throw new BadRequestException('Invalid deeplink')

    return deeplinkRecord
  }

  async complete(urlEncrypted: string) {
    const deeplink = await this.findDataByHash(urlEncrypted)

    const { customerNumber, contractId, scenarioId } = deeplink
    const { scenariosConfig } = await this.getConfig(customerNumber)
    const { accessToken } = await this.authUser(customerNumber)

    this.handleUpdateContract(scenariosConfig, scenarioId, 'finalized', +contractId, accessToken)

    deeplink.isCompleted = true
    deeplink.completedAt = new Date()
    deeplink.currentStatus = DEEPLINK_STATUS.COMPLETED
    await this.deeplinkRepository.save(deeplink)

    return {
      message: 'Creation via deeplink successfully completed'
    }
  }

  // CRUD Operations
  async findAll(
    input: GetDeeplinksQueryDto
  ): Promise<{ list: DeeplinkEntity[]; total: number; page: number; limit: number }> {
    const {
      customerNumber,
      contractId,
      scenarioId,
      isCompleted,
      currentStatus,
      phone,
      email,
      page = DEFAULT_PAGE,
      limit = DEFAULT_PAGE_LIMIT,
      withoutLimit,
      sortBy = DEFAULT_SORT_COLUMN,
      sortOrder = DEFAULT_SORT_ORDER,
      createdDateStart,
      createdDateEnd,
      completedDateStart,
      completedDateEnd
    } = input

    const query = this.deeplinkRepository.createQueryBuilder('deeplink')
    applyQueryFilters(
      query,
      {
        simple: {
          customerNumber,
          contractId,
          scenarioId,
          isCompleted, // NOTE: isCompleted deprecated, use currentStatus instead
          phone,
          email
        },
        array: { currentStatus },
        dateRange: {
          createdAt: { start: createdDateStart, end: createdDateEnd },
          completedAt: { start: completedDateStart, end: completedDateEnd }
        }
      },
      'deeplink'
    )

    if (sortBy) {
      query.orderBy(`deeplink.${sortBy}`, sortOrder)
    }

    const total = await query.getCount()
    if (!withoutLimit) {
      query.skip((page - 1) * limit).take(limit)
    }
    const list = await query.getMany()

    return {
      list,
      total,
      page,
      limit: withoutLimit ? total : limit
    }
  }

  async findOne(id: number): Promise<DeeplinkEntity> {
    const deeplink = await this.deeplinkRepository.findOne({
      where: { id }
    })
    if (!deeplink) throw new NotFoundException(`Deeplink not found`)

    return deeplink
  }

  async update(id: number, updateDeeplinkDto: UpdateDeeplinkDto): Promise<DeeplinkEntity> {
    try {
      const deeplink = await this.findOne(id)
      const imagesCount = deeplink.imagesCount + (updateDeeplinkDto.imagesCount || 0)
      const requestInfo = { ...deeplink.requestInfo, ...updateDeeplinkDto.requestInfo }

      Object.assign(deeplink, { ...updateDeeplinkDto, imagesCount, requestInfo })
      return await this.deeplinkRepository.save(deeplink)
    } catch (e) {
      throw new BadRequestException(`Error during update: `, e?.message)
    }
  }

  async remove(id: number) {
    const deeplink = await this.findOne(id)
    deeplink.deletedAt = new Date()
    deeplink.currentStatus = DEEPLINK_STATUS.DELETED
    return await this.deeplinkRepository.save(deeplink)
  }

  getFileName(input: GetDeeplinksQueryDto) {
    let name = 'deeplink_export'
    const ext = '.csv'
    if (input?.customerNumber) name += `_cn${input.customerNumber}`
    if (input?.createdDateStart || input?.createdDateEnd) {
      const start = input?.createdDateStart ? format(new Date(input?.createdDateStart), 'dd.MM.yy') : 'x'
      const end = input?.createdDateEnd ? format(new Date(input?.createdDateEnd), 'dd.MM.yy') : 'x'
      name += `_created-${start}-${end}`
    }
    if (input?.completedDateStart || input?.completedDateEnd) {
      const start = input?.completedDateStart ? format(new Date(input?.completedDateStart), 'dd.MM.yy') : 'x'
      const end = input?.completedDateEnd ? format(new Date(input?.completedDateEnd), 'dd.MM.yy') : 'x'
      name += `_completed-${start}-${end}`
    }
    const maxLengthFileNameWithoutExtension = MAX_LENGTH_FILE_NAME - ext.length
    if (name.length > maxLengthFileNameWithoutExtension) {
      name = name.slice(0, maxLengthFileNameWithoutExtension)
    }
    name += ext
    return name
  }

  async exportToCsv(input: GetDeeplinksQueryDto, res: Response) {
    const { list } = await this.findAll(input)
    if (!list.length) throw new NotFoundException(`Deeplinks not found`)
    const normalizedList = list.map(item => {
      const flatItem = Object.entries(item).reduce((acc, [key, value]) => {
        if (value instanceof Date) {
          acc[key] = value.toISOString()
        } else if (typeof value === 'object' && value !== null) {
          acc[key] = JSON.stringify(value)
        } else {
          acc[key] = value
        }
        return acc
      }, {} as Record<string, string>)

      return flatItem
    })

    const headers = Object.keys(normalizedList[0])

    const csvRows = [
      headers.join(','),
      ...normalizedList.map(row =>
        headers.map(header => `"${(row[header] ?? '').toString().replace(/"/g, '""')}"`).join(',')
      )
    ]

    const csvContent = csvRows.join('\n')
    const fileName = this.getFileName(input)
    res.setHeader('Content-Type', 'text/csv')
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`)
    res.send(csvContent)
  }
}
