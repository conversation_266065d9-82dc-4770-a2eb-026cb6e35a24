import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, Is<PERSON><PERSON>, IsObject } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class GenerateDeeplinkDto {
  @ApiProperty({ description: 'Customer number' })
  @IsString()
  customerNumber: string

  @ApiProperty({ description: 'Contract ID' })
  @IsString()
  contractId: string

  @ApiProperty({ description: 'Scenario ID' })
  @IsString()
  scenarioId: string

  @ApiProperty({ description: 'Expiration time in seconds', required: false })
  @IsOptional()
  @IsNumber()
  expireAt?: number

  @ApiProperty({ description: 'Profile Name of the user to be used in bidding', required: false })
  @IsOptional()
  @IsString()
  profileName?: string

  @ApiProperty({ description: 'Phone number', required: false })
  @IsOptional()
  @IsString()
  phone?: string

  @ApiProperty({ description: 'Email address', required: false })
  @IsOptional()
  @IsEmail()
  email?: string

  @ApiProperty({ description: 'Request information as JSON object', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  requestInfo?: Record<string, string>
}
