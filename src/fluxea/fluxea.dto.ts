import { <PERSON><PERSON>otE<PERSON>y, <PERSON><PERSON><PERSON><PERSON>, <PERSON>String, Min } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class FluxeaMaintenanceScheduleQueryDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  plate: string

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  country: string

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  lang: string

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  year: string

  @IsNumber()
  @Min(0)
  @ApiProperty()
  km: number
}
