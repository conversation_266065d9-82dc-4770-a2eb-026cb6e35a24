import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'

import { SwaggerDoc } from 'src/common/decorators/swagger-doc.decorator'
import { FluxeaService } from 'src/fluxea/fluxea.service'
import { FluxeaMaintenanceScheduleQueryDto } from 'src/fluxea/fluxea.dto'

@Controller('fluxea')
@ApiTags('Fluxea')
export class FluxeaController {
  constructor(private readonly fluxeaService: FluxeaService) {}

  @Get('/maintenanceScheduleData')
  @SwaggerDoc({
    isAuth: false,
    summary: 'Creates a command'
  })
  createCommand(@Query() data: FluxeaMaintenanceScheduleQueryDto) {
    return this.fluxeaService.getMaintenanceData(data)
  }
}
