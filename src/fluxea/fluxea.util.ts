import * as DAT5 from 'api2dat5'

export const createFluxeaSoapRequest = ({ method, body }: { method: string; body?: object }) => {
  const xmlTemplate = `<?xml version="1.0" encoding="utf-8"?>
    <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
      <soap:Body>
      ${
        body
          ? `<${method}>
              ${DAT5.objToXml(body)}
            </${method}>`
          : `<${method}/>`
      }
 
      </soap:Body>
    </soap:Envelope>      
  `

  return xmlTemplate
}
