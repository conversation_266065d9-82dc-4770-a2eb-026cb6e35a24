import { BadRequestException, Injectable } from '@nestjs/common'
import axios, { AxiosInstance, isAxiosError } from 'axios'
import { Agent } from 'node:https'
import { readFileSync } from 'fs'

import { FluxeaMaintenanceScheduleQueryDto } from 'src/fluxea/fluxea.dto'
import { createFluxeaSoapRequest } from './fluxea.util'
import { parseSoapResponse } from 'src/shared/soap/helpers/parseSoapResponse'

@Injectable()
export class FluxeaService {
  private axiosClient: AxiosInstance

  constructor() {
    const certificateAgent = new Agent({
      cert: readFileSync('./src/fluxea/fluxea.crt'),
      key: readFileSync('./src/fluxea/fluxea.key')
    })
    this.axiosClient = axios.create({
      httpsAgent: certificateAgent,
      baseURL: 'https://tis.auto-publitest.com/webservice/serveur.php',
      headers: {
        'Content-Type': 'text/xml; charset=utf-8'
      }
    })
  }

  async getMaintenanceData(data: FluxeaMaintenanceScheduleQueryDto) {
    try {
      const xmlDataOfVehicleData = await this.axiosClient.post(
        '',
        createFluxeaSoapRequest({
          body: {
            id: 'dat',
            typclevehi: 'VMO_Clecode',
            pays: data.country,
            plate: data.plate,
            langue: data.lang
          },
          method: 'getVersionPlate'
        })
      )
      const vehicleDataResultResponse = parseSoapResponse<Record<string, Record<string, string[]>>>(
        xmlDataOfVehicleData.data
      )
      const [clevehi] = vehicleDataResultResponse?.return?.ktyp || []
      if (!clevehi) {
        throw new BadRequestException(`No Clevehi was found for vehicle data`)
      }
      const xmlDataOfGetScheduleMaintenenceData = await this.axiosClient.post(
        '',
        createFluxeaSoapRequest({
          body: {
            id: 'dat',
            typclevehi: 'VMO_Clecode',
            clevehi,
            km: data.km,
            year: data.year,
            langue: data.lang
          },
          method: 'getScheduleMaintenenceData'
        })
      )
      const getScheduleMaintenenceDataResponse = parseSoapResponse<Record<string, any>>(
        xmlDataOfGetScheduleMaintenenceData.data
      )
      return getScheduleMaintenenceDataResponse.return
    } catch (err) {
      if (isAxiosError(err) && err.response) {
        throw new BadRequestException({
          status: err.response.status,
          error: err.response.data
        })
      }
      throw new BadRequestException(err)
    }
  }
}
