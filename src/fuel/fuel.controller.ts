import { Controller, Get, Param } from '@nestjs/common'
import { FuelService } from './fuel.service'
import { fuelTag } from 'src/common/constants/swagger'
import { ApiTags } from '@nestjs/swagger'
import { SwaggerDoc } from 'src/common/decorators/swagger-doc.decorator'
import { findByFitCodeResponse } from './types'

@ApiTags(fuelTag)
@Controller(fuelTag)
export class FuelCodesController {
  constructor(private readonly fuelCodesService: FuelService) {}

  @Get('/:code')
  @SwaggerDoc({
    isAuth: false,
    summary: 'Search FitCode',
    typeResponse: findByFitCodeResponse
  })
  async findByFitCode(@Param('code') code: string): Promise<findByFitCodeResponse | null> {
    return this.fuelCodesService.findByFitCode(code)
  }
}
