import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'

export class Fuel {
  @ApiProperty({ type: Number, nullable: false, required: true })
  id: number

  @ApiProperty({ type: String, nullable: true, required: true })
  FZA: string

  @ApiProperty({ type: String, nullable: true, required: true })
  HST: string

  @ApiProperty({ type: String, nullable: true, required: true })
  HT: string

  @ApiProperty({ type: String, nullable: true, required: true })
  UT: string

  @ApiProperty({ type: String, nullable: true, required: true })
  FICODE: string

  @ApiProperty({ type: String, nullable: true, required: true })
  AV: string

  @ApiProperty({ type: String, nullable: true, required: true })
  FLUID_TYPE: string

  @ApiProperty({ type: String, nullable: true, required: true })
  AMOUNT: string

  @ApiProperty({ type: String, nullable: true, required: true })
  UNIT: string

  @ApiProperty({ type: String, nullable: true, required: true })
  ACCURATE: string

  @ApiProperty({ type: String, nullable: true, required: true })
  createdAt: string
}

export class findByFitCodeResponse extends Fuel {}
