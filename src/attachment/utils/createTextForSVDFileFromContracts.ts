import { format, parse } from 'date-fns'
import * as DAT5 from 'api2dat5'
import { Inspection } from 'src/inspection-history/types'
import { getFieldText } from 'src/shared/soap/helpers/getFieldText'
import { getVehicleDescriptionFromContract } from './getVehicleDescriptionFromContract'
import { getValueFromTemplateEntries } from './getValueFromTemplateEntries'

import { myClaimWageToKfzWage } from './myClaimWageToKfzWage'
import { myClaimWageUnitsPerHourToKfz } from './myClaimWageUnitsPerHourToKfz'
import { SVD_FILE_TEMPLATE } from '../constants'
import { formatDatesInFields } from './formatDatesInFields'
import { safeJsonParse } from './safeJsonParse'
import { jsonParseSafe } from './jsonParseSafe'
import { CONTRACT_ENTRIES_KEYS } from 'src/common/constants/contract'
import { LacquerMethod } from '../types'

export const createTextForSVDFileFromContracts = ({
  templateEntries,
  Dossier,
  TradingData,
  VehicleData,
  RepairOrder,
  RepairCalculation,
  additionalFields
}: {
  templateEntries: DAT5.MyClaimExternalService_schema1.contractDetails_customTemplateData_entry[]
  Dossier?: DAT5.MyClaimExternalService_schema2.Dossier
  TradingData?: DAT5.MyClaimExternalService_schema2.TradingData
  VehicleData?: DAT5.MyClaimExternalService_schema2.Vehicle
  RepairOrder?: DAT5.MyClaimExternalService_schema2.RepairOrder
  RepairCalculation?: DAT5.MyClaimExternalService_schema2.RepairCalculation
  additionalFields?: {
    BEMERKUNGMEMO: string
    ALTSCHADENMEMO: string
    SCHADENMEMO: string
    ANMERKUNGMEMO: string
    MITTEILUNGSMEMO: string
  }
}) => {
  let svdText = ''

  Object.keys(SVD_FILE_TEMPLATE).forEach(sectionKey => {
    const sectionFields = SVD_FILE_TEMPLATE[sectionKey]

    if (typeof sectionFields === 'string') {
      let sectionFieldValue = ''
      switch (sectionFields) {
        case 'inspectionHistoryArray': {
          const inspections = safeJsonParse<Inspection[]>(
            getValueFromTemplateEntries(templateEntries, 'historyOfInspection')
          )
          inspections?.forEach((inspection, idx) => {
            const isLast = idx === inspections.length - 1
            const parsedDate = parse(inspection.inspectionHistoryDate?.toString(), 'dd.MM.yyyy HH:mm', new Date())
            const formattedDate = format(parsedDate, 'yyyyMMdd')
            const formattedTime = format(parsedDate, 'HH:mm')
            const contactName = inspection.inspectionHistoryContactName || ''
            const username = inspection.username || ''
            sectionFieldValue += `[BES.1.${
              idx + 1
            }]\nBES_DATUM=${formattedDate}\nBES_ZEIT=${formattedTime}BES_ANWESENDER=${contactName}BES_SVNAME=${username}\n${
              isLast ? '' : '\n'
            }`
          })
          break
        }
      }
      if (sectionFieldValue) svdText += sectionFieldValue
    } else {
      svdText += `${sectionKey}\n`

      Object.keys(sectionFields).forEach(sectionFieldKey => {
        let sectionFieldValue = sectionFields[sectionFieldKey]

        switch (sectionFieldValue) {
          case 'TODAY': {
            sectionFieldValue = formatDatesInFields(null, true)
            break
          }
          case 'VehicleDescription': {
            sectionFieldValue = getVehicleDescriptionFromContract(VehicleData)
            break
          }
          case 'TypeOfInsurance': {
            sectionFieldValue = String(RepairOrder?.TypeOfInsurance || '')
            break
          }
          case 'PolicyNumber': {
            sectionFieldValue = String(RepairOrder?.PolicyNumber || '')
            break
          }
          case 'DamageNumber': {
            sectionFieldValue = String(RepairOrder?.DamageNumber || '')
            break
          }
          case 'licencePlateInsurant': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'licencePlateInsurant')
            break
          }
          case 'compensationIBAN': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'compensationIBAN')
            break
          }
          case 'importantNote1': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'importantNote1')
            break
          }
          case 'importantNote2': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'importantNote2')
            break
          }
          case 'importantNote3': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'importantNote3')
            break
          }
          case 'contractInformation': {
            sectionFieldValue = `${getValueFromTemplateEntries(templateEntries, 'contractInformation') || ''}/DAT/`
            break
          }
          case 'impactArea': {
            const impactArea = getValueFromTemplateEntries(templateEntries, 'impactArea')
            sectionFieldValue = impactArea.replace(/[^A-Za-z0-9]/g, '') // make 'ABCDFGHM' from '[“A”,“B”,“C”,“D”,“F”,“G”,“H”,“M”]'
            break
          }
          case 'lacquerState': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'lacquerState')
            break
          }
          case 'vehicleState': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'vehicleState')
            break
          }
          case 'compensationBank': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'compensationBank')
            break
          }
          case 'compensationBIC': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'compensationBIC')
            break
          }
          case 'vehicleSafteyInspectionUpcomingDate': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'vehicleSafteyInspectionUpcomingDate')
            break
          }
          case 'unloadedWeight': {
            const value = getValueFromTemplateEntries(templateEntries, 'unloadedWeight')
            sectionFieldValue = value ? Number.parseInt(value).toString() : ''
            break
          }
          case 'inspectionHistoryCount': {
            const inspections = safeJsonParse<Inspection[]>(
              getValueFromTemplateEntries(templateEntries, 'historyOfInspection'),
              []
            )
            sectionFieldValue = inspections.length.toString()
            break
          }
          case 'CreateDate': {
            const createDate = Dossier?.CreateDate
            sectionFieldValue = createDate ? String(formatDatesInFields(null, true)) : ''
            break
          }
          case 'ContractNumber': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'contractNumber')
            break
          }
          case 'TradingData': {
            const Expert = TradingData?.Expert

            sectionFieldValue = `${Expert?.Title || ''} ${Expert?.NameLong || ''} ${Expert?.CompanyName || ''}`
            break
          }
          case 'VehicleIdentNumber': {
            sectionFieldValue = VehicleData?.VehicleIdentNumber || ''
            break
          }
          case 'LicenseNumber': {
            sectionFieldValue = VehicleData?.RegistrationData?.LicenseNumber || ''
            break
          }
          case 'ManufacturerName': {
            sectionFieldValue = getFieldText(VehicleData?.ManufacturerName) || ''
            break
          }
          case 'BaseModelName': {
            sectionFieldValue = getFieldText(VehicleData?.BaseModelName) || ''
            break
          }
          case 'SubModelName': {
            sectionFieldValue = getFieldText(VehicleData?.SubModelName) || ''
            break
          }
          case 'VehicleType': {
            sectionFieldValue = String(VehicleData?.VehicleType || '')
            break
          }
          case 'vehicleColorDescription': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'vehicleColorDescription')
            break
          }
          case 'InitialRegistration': {
            sectionFieldValue = VehicleData?.InitialRegistration
              ? formatDatesInFields(VehicleData?.InitialRegistration)
              : ''
            break
          }
          case 'MileageEstimated': {
            sectionFieldValue = String(VehicleData?.MileageEstimated || '')
            break
          }
          case 'DamageDate': {
            sectionFieldValue = RepairOrder?.DamageDate ? formatDatesInFields(RepairOrder?.DamageDate) : ''
            break
          }

          case 'AllSum': {
            const sparePartsCosts = Dossier?.RepairCalculation?.CalculationSummary?.SparePartsCosts
            sectionFieldValue = typeof sparePartsCosts === 'object' ? String(sparePartsCosts.AllSum || '') : ''
            break
          }

          case 'TotalGrossCorrected': {
            sectionFieldValue = String(RepairCalculation?.CalculationSummary?.TotalGrossCorrected || '')
            break
          }
          case 'CommonTotalSum': {
            sectionFieldValue = String(
              RepairCalculation?.CalcResultCommon?.RepairCalculationSummary?.LacquerCosts?.TotalSum || ''
            )
            break
          }
          case 'SummaryTotalSum': {
            sectionFieldValue = String(RepairCalculation?.CalculationSummary?.LacquerCosts?.TotalSum || '')
            break
          }
          case 'WagePrice': {
            sectionFieldValue = String(RepairCalculation?.CalculationSummary?.LacquerCosts?.Wage?.Price || '')
            break
          }
          case 'TotalNetCorrected': {
            sectionFieldValue = String(RepairCalculation?.CalculationSummary?.TotalNetCorrected || '')
            break
          }
          case 'CompensationValue': {
            const compensationValue = getValueFromTemplateEntries(templateEntries, 'compensationValue')

            sectionFieldValue = compensationValue ? String(Number(compensationValue.replace(',', '.')).toFixed(2)) : ''

            break
          }
          case 'CompensationValueTag': {
            const compensationValue = getValueFromTemplateEntries(templateEntries, 'compensationValue')

            sectionFieldKey = 'ABLOESE'
            sectionFieldValue = compensationValue && +compensationValue > 0 ? '.T.' : ''

            break
          }
          case 'CompensationIncludesVAT': {
            const compensationIncludesVAT = getValueFromTemplateEntries(templateEntries, 'compensationIncludesVAT')
            sectionFieldValue = compensationIncludesVAT === 'true' ? '0' : '1'
            break
          }
          case 'residualValueAmount': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'residualValueAmount')
            break
          }
          case 'residualValueDealer': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'residualValueDealer')
            break
          }
          case 'AdditionalCostsFlatAmount': {
            sectionFieldValue = String(RepairCalculation?.RepairParameters?.AdditionalCostsFlatAmount ?? 38.6)
            break
          }
          case 'DamageCategory': {
            const damageCategory = getValueFromTemplateEntries(templateEntries, 'damageCategory')

            if (damageCategory === 'J') sectionFieldValue = '1'
            else if (damageCategory === 'G') sectionFieldValue = '2'
            else sectionFieldValue = '0'

            break
          }
          case 'bodyWage1': {
            // NOTE: contract value has been replaced with a memo. I will leave the old version
            // sectionFieldValue = myClaimWageToKfzWage(
            //   RepairCalculation?.RepairWages?.CarBody1,
            //   RepairCalculation?.RepairWages?.WageUnitsPerHour
            // ).toFixed(2)

            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'bodyWage1')
            break
          }
          case 'ST_SPMO': {
            sectionFieldValue = myClaimWageUnitsPerHourToKfz(
              RepairCalculation?.RepairParameters?.TimeUnitsPerHour
            ).toString()
            break
          }
          case 'SS_LACK_LK1': {
            sectionFieldValue = myClaimWageToKfzWage(
              RepairCalculation?.CalculationSummary?.LacquerCosts?.Wage?.PricePerUnit,
              RepairCalculation?.RepairWages?.WageUnitsPerHour
            ).toFixed(2)
            break
          }
          case 'ST_LACK': {
            sectionFieldValue =
              RepairCalculation?.RepairParameters?.LacquerTimeUnitSystem === 'STD'
                ? '1'
                : myClaimWageUnitsPerHourToKfz(RepairCalculation?.RepairParameters?.TimeUnitsPerHour).toString()
            break
          }

          case 'MileageOdometer': {
            sectionFieldValue = String(Dossier?.RepairCalculation?.Vehicle?.MileageOdometer || '')
            break
          }

          case 'ProcurementCostsFromParts': {
            const sparePartsCosts = Dossier?.RepairCalculation?.CalculationSummary?.SparePartsCosts
            sectionFieldValue =
              typeof sparePartsCosts === 'object' ? String(sparePartsCosts.ProcurementCostsFromParts) : ''
            break
          }

          case 'DisposalCostsSpareParts': {
            const sparePartsCosts = Dossier?.RepairCalculation?.CalculationSummary?.SparePartsCosts
            sectionFieldValue =
              typeof sparePartsCosts === 'object' ? String(sparePartsCosts.DisposalCostsSpareParts) : ''
            break
          }

          case 'SumSmallSparePartCosts': {
            sectionFieldValue = String(
              Dossier?.RepairCalculation?.CalcResultCommon?.RepairCalculationSummary?.SumSmallSparePartCosts || ''
            )
            break
          }

          case 'LabourCostsTotalSum': {
            sectionFieldValue = String(
              Dossier?.RepairCalculation?.CalcResultCommon?.RepairCalculationSummary?.LabourCosts?.TotalSum || ''
            )
            break
          }

          case 'LacquerCostsWagePrice': {
            sectionFieldValue = String(
              Dossier?.RepairCalculation?.CalcResultCommon?.RepairCalculationSummary?.LacquerCosts?.Wage?.Price || ''
            )
            break
          }

          case 'LacquerCostsMaterialTotalSum': {
            sectionFieldValue = String(
              Dossier?.RepairCalculation?.CalcResultCommon?.RepairCalculationSummary?.LacquerCosts?.Material
                ?.TotalSum || ''
            )
            break
          }

          case 'vbrand': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'vbrand')
            break
          }

          case 'vmodel': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'vmodel')
            break
          }

          case 'vtype': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'vtype')
            break
          }

          case 'vehicleCC': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'vehicleCC')
            break
          }

          case 'nationalCodeAustria': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'nationalCodeAustria')
            break
          }

          case 'vehicleKW': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'vehicleKW')
            break
          }

          case 'repairable': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'repairable')
            break
          }

          case 'damageCategory': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'damageCategory') === 'J' ? '1' : '0'
            break
          }

          case 'insurance_id': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, 'insurance_id')
            break
          }

          case 'salesPriceGrossByActiveValuation': {
            const valuationKindActive = getValueFromTemplateEntries(
              templateEntries,
              CONTRACT_ENTRIES_KEYS.MEMO.valuationKindActive
            )
            if (valuationKindActive === 'APS') {
              const APSResult = jsonParseSafe<{
                tableData?: Record<string, { finalSalePrice?: string }>
                radioData?: { name?: string }
              }>(getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.apsResult))
              const fieldName = APSResult?.radioData?.name
              const salesPriceGross = fieldName && APSResult?.tableData?.[fieldName]?.finalSalePrice
              sectionFieldValue = salesPriceGross || ''
            } else if (valuationKindActive === 'Manuell') {
              sectionFieldValue = getValueFromTemplateEntries(
                templateEntries,
                CONTRACT_ENTRIES_KEYS.MEMO.manualValuationRV
              )
            } else if (valuationKindActive === 'DAT') {
              const valuationResult = jsonParseSafe<DAT5.DossierN_schema1.Dossier>(
                getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.valuationResult)
              )
              const salesPriceGross = valuationResult?.Valuation?.SalesPriceGross?.toString()
              sectionFieldValue = salesPriceGross || ''
            } else {
              sectionFieldValue = ''
            }

            break
          }
          case 'InsuredFullName': {
            const name = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.insured.name)
            const surname = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.insured.surname)
            sectionFieldValue = `${name} ${surname}`
            break
          }
          case 'InsuredMail': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.insured.mail)
            break
          }
          case 'InsuredPhone': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.insured.phone)
            break
          }
          case 'InsuredMobilePhone': {
            sectionFieldValue = getValueFromTemplateEntries(
              templateEntries,
              CONTRACT_ENTRIES_KEYS.MEMO.insured.mobilePhone
            )
            break
          }
          case 'InsuredAddress': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.insured.address)
            break
          }
          case 'InsuredZip': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.insured.zip)
            break
          }
          case 'InsuredCity': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.insured.city)
            break
          }
          case 'OwnerFullName': {
            const name = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.owner.name)
            const surname = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.owner.surname)
            sectionFieldValue = `${name} ${surname}`
            break
          }
          case 'OwnerMail': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.owner.mail)
            break
          }
          case 'OwnerPhone': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.owner.phone)
            break
          }
          case 'OwnerMobilePhone': {
            sectionFieldValue = getValueFromTemplateEntries(
              templateEntries,
              CONTRACT_ENTRIES_KEYS.MEMO.owner.mobilePhone
            )
            break
          }
          case 'OwnerAddress': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.owner.address)
            break
          }
          case 'OwnerZip': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.owner.zip)
            break
          }
          case 'OwnerCity': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.owner.city)
            break
          }

          case 'nationalCodeAustria': {
            sectionFieldValue = getValueFromTemplateEntries(
              templateEntries,
              CONTRACT_ENTRIES_KEYS.MEMO.nationalCodeAustria
            )
            break
          }

          case 'vehicleLastRegistration': {
            sectionFieldValue = getValueFromTemplateEntries(
              templateEntries,
              CONTRACT_ENTRIES_KEYS.MEMO.vehicleLastRegistration
            )
            break
          }

          case 'vehicleSeats': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.vehicleSeats)
            break
          }
          case 'permissableTotalWeight': {
            sectionFieldValue = getValueFromTemplateEntries(
              templateEntries,
              CONTRACT_ENTRIES_KEYS.MEMO.permissableTotalWeight
            )
            break
          }
          case 'permissableTotalWeightWithUnloadedWeight': {
            const permissableTotalWeight = getValueFromTemplateEntries(
              templateEntries,
              CONTRACT_ENTRIES_KEYS.MEMO.permissableTotalWeight
            )
            const unloadedWeight = getValueFromTemplateEntries(
              templateEntries,
              CONTRACT_ENTRIES_KEYS.MEMO.unloadedWeight
            )
            const value = Number.parseFloat(permissableTotalWeight) - Number.parseFloat(unloadedWeight)
            sectionFieldValue = value ? value.toString() : ''
            break
          }
          case 'lacquerState': {
            sectionFieldValue = getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.lacquerState)
            break
          }
          case 'vehicleColorType': {
            sectionFieldValue = getValueFromTemplateEntries(
              templateEntries,
              CONTRACT_ENTRIES_KEYS.MEMO.vehicleColorType
            )
            break
          }
          case 'manfredImpairment': {
            sectionFieldValue = getValueFromTemplateEntries(
              templateEntries,
              CONTRACT_ENTRIES_KEYS.MEMO.manfredImpairment
            )
            break
          }

          case 'NW_ERRECHNET': {
            const APSResult = jsonParseSafe<{
              tableData?: Record<string, { finalSalePrice?: string }>
              radioData?: { name?: string }
              calculationData: { listPriceEuro: number | null }
            }>(getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.apsResult))
            let listPriceEuro = APSResult?.calculationData?.listPriceEuro?.toString() || ''

            const valuationKindActive = getValueFromTemplateEntries(
              templateEntries,
              CONTRACT_ENTRIES_KEYS.MEMO.valuationKindActive
            ) as 'APS' | 'Manuell' | 'DAT'

            if (!listPriceEuro && valuationKindActive === 'Manuell') {
              const manualValuationEquipment = getValueFromTemplateEntries(
                templateEntries,
                CONTRACT_ENTRIES_KEYS.MEMO.manualValuationEquipment
              )
              const manualValuationListPrice = getValueFromTemplateEntries(
                templateEntries,
                CONTRACT_ENTRIES_KEYS.MEMO.manualValuationListPrice
              )
              const sumOfManualListPrice =
                (Number.parseFloat(manualValuationListPrice) || 0) + (Number.parseFloat(manualValuationEquipment) || 0)
              listPriceEuro = sumOfManualListPrice ? sumOfManualListPrice.toString() : ''
            }
            if (!listPriceEuro && valuationKindActive === 'DAT') {
              const appraisalEquipments = jsonParseSafe<{
                SpecialEquipment?: { EquipmentPosition?: Array<{ OriginalPriceGross?: number }> }
              }>(getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.appraisalEquipments))
              const priceOfAppraisalEquipmentData =
                appraisalEquipments?.SpecialEquipment?.EquipmentPosition?.reduce(
                  (
                    acc: number,
                    item: {
                      OriginalPriceGross?: number
                    }
                  ) => {
                    if (item?.OriginalPriceGross) {
                      return acc + item.OriginalPriceGross || 0
                    }
                    return acc
                  },
                  0
                ) || 0

              const fieldName = APSResult?.radioData?.name

              const originalPriceGross = Number.parseFloat(
                (fieldName && APSResult?.tableData?.[fieldName]?.finalSalePrice) || ''
              )
              const sum = originalPriceGross + priceOfAppraisalEquipmentData
              listPriceEuro = sum ? sum.toString() : ''
            }
            sectionFieldValue = listPriceEuro
            break
          }

          case 'SON_PREIS': {
            const appraisalEquipments = jsonParseSafe<{
              SpecialEquipment?: { EquipmentPosition?: Array<{ OriginalPriceGross?: number }> }
            }>(getValueFromTemplateEntries(templateEntries, CONTRACT_ENTRIES_KEYS.MEMO.appraisalEquipments))
            const priceOfAppraisalEquipmentData =
              appraisalEquipments?.SpecialEquipment?.EquipmentPosition?.reduce(
                (
                  acc: number,
                  item: {
                    OriginalPriceGross?: number
                  }
                ) => {
                  if (item?.OriginalPriceGross) {
                    return acc + item.OriginalPriceGross || 0
                  }
                  return acc
                },
                0
              ) || 0

            sectionFieldValue = priceOfAppraisalEquipmentData ? priceOfAppraisalEquipmentData.toString() : ''
            break
          }
          case 'BEMERKUNGMEMO': {
            sectionFieldValue = additionalFields?.BEMERKUNGMEMO || ''
            break
          }
          case 'ALTSCHADENMEMO': {
            sectionFieldValue = additionalFields?.ALTSCHADENMEMO || ''
            break
          }
          case 'SCHADENMEMO': {
            sectionFieldValue = additionalFields?.SCHADENMEMO || ''
            break
          }
          case 'ANMERKUNGMEMO': {
            sectionFieldValue = additionalFields?.ANMERKUNGMEMO || ''
            break
          }
          case 'MITTEILUNGSMEMO': {
            sectionFieldValue = additionalFields?.MITTEILUNGSMEMO || ''
            break
          }

          case 'SS_LACK_LK1': {
            const parameters = RepairCalculation?.ProcedureRelatedParameters?.ProcedureRelatedParameter
            const paintSystem = parameters?.find(item => item._attr_attribute === 'selectedLacquerMethod')?._value as
              | LacquerMethod
              | undefined
            if (!paintSystem) {
              sectionFieldValue = ''
            } else if (paintSystem === 'EURO_LACQUER') {
              const wage =
                parameters?.find(item => item._attr_attribute === 'wage' && item._attr_factor === 'EuroLacquerFactor')
                  ?._value || ''
              sectionFieldValue = wage
            } else if (paintSystem === 'MANUFACTURER_SPECIFIC') {
              const wage =
                parameters?.find(
                  item => item._attr_attribute === 'wage' && item._attr_factor === 'ManufacturerLacquerFactor'
                )?._value || ''
              sectionFieldValue = wage
            } else if (paintSystem === 'AZT') {
              const wage =
                parameters?.find(item => item._attr_attribute === 'wage' && item._attr_factor === 'AztLacquerFactor')
                  ?._value || ''
              sectionFieldValue = wage
            } else if (paintSystem === 'CZ') {
              const wage =
                parameters?.find(item => item._attr_attribute === 'wage' && item._attr_factor === 'CzLacquerFactor')
                  ?._value || ''
              sectionFieldValue = wage
            } else if (paintSystem === 'FULLY_MANUAL_LACQUER_FACTOR') {
              const wage =
                parameters?.find(
                  item => item._attr_attribute === 'wage' && item._attr_factor === 'FullyManualLacquerFactor'
                )?._value || ''
              sectionFieldValue = wage
            }
            break
          }
        }

        svdText += `${sectionFieldKey}=${sectionFieldValue}\n`
      })
    }
    svdText += '\n'
  })

  return svdText
}
