import { convert } from 'html-to-text'

export function markdownToPlainText(htmlString = '') {
  if (!htmlString || typeof htmlString !== 'string') return ''
  try {
    const plainText = convert(htmlString, {
      wordwrap: false,
      selectors: [
        {
          selector: 'font',
          format: 'block'
        }
      ],
      preserveNewlines: true
    })
      .replace(/^ \* /gm, ' - ')
      .replace(/\n{2,}/g, '\n')
      .trim()
    return plainText
  } catch (error) {
    console.log('error aaaa: ', error)
    return ''
  }
}
