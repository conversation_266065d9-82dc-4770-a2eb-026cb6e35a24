import { markdownToPlainText } from './markdownToPlainText'

describe('markdownToPlainText', () => {
  it(`should correct convert markdown to plain text`, () => {
    expect(
      markdownToPlainText(`<p><span style="color: rgb(100, 107, 107);">Lorem ipsum dolor sit amet</span></p>`)
    ).toBe('Lorem ipsum dolor sit amet')
    expect(
      markdownToPlainText(
        `<p>test<b> sad </b> <i>sdsda</i></p><font size="32pt" style="display: block; white-space: pre-wrap; margin-bottom: 0.5em;"><b><i>sadasds</i></b></font><font size="24pt" style="display: block; white-space: pre-wrap; margin-bottom: 0.5em;">asdasd</font><ul><li>1</li><li>2</li><li>3</li></ul>`
      )
    ).toBe(`test sad sdsda
sadasds
asdasd
 - 1
 - 2
 - 3`)
    expect(markdownToPlainText(`Schadenbeschreibung\n`)).toBe('Schadenbeschreibung')
    expect(markdownToPlainText(`<p></p>`)).toBe('')
    expect(markdownToPlainText(``)).toBe('')
    expect(markdownToPlainText({} as unknown as string)).toBe('')
    expect(markdownToPlainText([] as unknown as string)).toBe('')
    expect(markdownToPlainText(NaN as unknown as string)).toBe('')
  })
})
