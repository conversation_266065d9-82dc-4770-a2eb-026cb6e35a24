import { ApiProperty } from '@nestjs/swagger'

export class AttachmentsResponse {
  @ApiProperty({ type: Number })
  id: number

  @ApiProperty({ type: String })
  fileName: string

  @ApiProperty({ type: String })
  mimeType: string

  @ApiProperty({ type: Number })
  dataSize: number

  @ApiProperty({ type: String })
  binaryData: string

  @ApiProperty({ type: String })
  published: string

  @ApiProperty({ type: String })
  uploaded: string

  @ApiProperty({ type: String })
  documentType: string

  @ApiProperty({ type: Number })
  documentID: number

  @ApiProperty({ type: String })
  attachmentType: string

  @ApiProperty({ type: Number })
  uploaderId: number

  @ApiProperty({ type: String })
  uploaderCustomerNumber: string

  @ApiProperty({ type: String })
  uploaderRole: string

  @ApiProperty({ type: String })
  uploaderName: string

  @ApiProperty({ type: Number })
  uploaderUserId: number

  @ApiProperty({ type: String })
  uploaderUserLogin: string

  @ApiProperty({ type: String })
  uploaderUserName: string
}

export class AttachmentResponse {
  @ApiProperty({ type: String })
  type: string

  @ApiProperty({ type: String })
  link: string

  @ApiProperty({ type: String })
  extension: string

  @ApiProperty({ type: Number })
  id: number
}

export class AttachmentFolderResponse {
  @ApiProperty({ type: String })
  ETag: string

  @ApiProperty({ type: String })
  Location: string

  @ApiProperty({ type: String })
  key: string

  @ApiProperty({ type: String })
  Key: string

  @ApiProperty({ type: String })
  Bucket: string
}

export class AttachmentDeleteResponse {
  @ApiProperty({ type: Boolean })
  deleted: boolean
}

class ResponseObj {
  @ApiProperty({ type: Boolean })
  return: boolean
}

export class AttachmentsConvertPDFResponse {
  @ApiProperty({ type: ResponseObj })
  responseObj: ResponseObj

  @ApiProperty({ type: String })
  resXMLText: string
}

interface AssignedTags {
  description: string
  id: number
  identification: string
}
interface CustomTag {
  tagType: string
  description: string
  oncePerClaim: boolean
  identification: string
  customerId: number
  id: number
  systemTag: boolean
  networkType: DAT.NetworkType
  icon?: string
  urlParams?: any
  claimType?: string | null
  calculationType?: string | null
  category?: string | null
  iconsSelector?: string | null
}
interface FolderOfClaimAttachment {
  assignedTags?: AssignedTags[]
  binaryData?: {
    dataSize: number
    mimeType: string
    modified: string
    id: number
  }
  customer?: { role: string; id: number; name: string }
  document_id?: number
  fileName: string
  id: number
  orderColumn: number
  photo?: {
    fileName: string
    height: number
    id: number
    imageFormat: string
    imageSize: number
    imageSource: string
    width: number
  }
  published: string
  uploaded: string
  user?: { firstName: string; id: number; surname: string }
  base64?: string
}
export interface FolderOfClaim {
  attachments: FolderOfClaimAttachment[]
  autoPublish: boolean
  availableTags: CustomTag[]
  defaultTagsIds: number[]
  canEditShared: boolean
  canRepublish: boolean
  color: string
  defaultForSdii: boolean
  directoryName: string
  id: number
  identification: string
  name: string
  readonly: boolean
  shared: unknown[]
  single: boolean
  sphinxFile: boolean
  uploadDisabled: boolean
  maxCount: number
}
export type LacquerMethod = 'EURO_LACQUER' | 'MANUFACTURER_SPECIFIC' | 'AZT' | 'CZ' | 'FULLY_MANUAL_LACQUER_FACTOR'
export type SvdEntry = {
  binaryData: Buffer
  fileName: string
}
export type SvdEntries = SvdEntry[]
