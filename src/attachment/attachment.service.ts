import { BadRequestException, Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import * as AWS from 'aws-sdk'

import PDFDocument from 'pdfkit'
import sizeOf from 'image-size'

import { v4 as uuidv4 } from 'uuid'
import * as DAT5 from 'api2dat5'

import { MyClaimProxyService } from 'src/myClaim-proxy/myClaim-proxy.service'
import { UserConfigService } from 'src/user-config/user-config.service'
import { ConfigService } from '../config/config.service'
import { AuthService } from 'src/auth/auth.service'

import { AttachmentEntity } from './persistance/attachment.entity'

import { DEFAULT_IMAGES_PER_PAGE } from 'src/common/constants/gallery-constants'
import { HeadersNames, Networks } from 'src/common/constants/headers-names'
import { TypeOfAttachment, File } from './persistance/types'
import { dictForFileType } from './persistance/dict'

import { createTextForSVDFileFromContracts } from './utils/createTextForSVDFileFromContracts'
import { getParsedArraySafe } from 'src/shared/soap/helpers/getParsedArraySafe'
import { getPreparedDataForPdf } from './utils/getPreparedDataForPdf'
import { createZipArchive } from 'src/common/utils/createZipArchive'
import { renameAttachments } from './utils/renameAttachments'
import { ProxyRequest } from 'src/myClaim-proxy/types'
import { AttachmentsResponse, FolderOfClaim, SvdEntries } from './types'
import { getValueFromTemplateEntries } from './utils/getValueFromTemplateEntries'
import { markdownToPlainText } from './utils/markdownToPlainText'

export function _MeasureServiceDuration() {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const start = performance.now()

      try {
        const result = await originalMethod.apply(this, args)

        const end = performance.now()
        const duration = end - start

        console.log(`
          🚀 Performance Measurement:
          - Method: ${target.constructor.name}.${propertyKey}
          - Execution Time: ${duration.toFixed(2)}ms
        `)

        return result
      } catch (error) {
        const end = performance.now()
        const duration = end - start

        console.error(`
          ❌ Performance Error:
          - Method: ${target.constructor.name}.${propertyKey}
          - Execution Time: ${duration.toFixed(2)}ms
          - Error: ${error.message}
        `)

        throw error
      }
    }

    return descriptor
  }
}

@Injectable()
export class AttachmentService {
  private s3 = new AWS.S3({
    endpoint: 'fra1.digitaloceanspaces.com',
    useAccelerateEndpoint: false,
    credentials: new AWS.Credentials(this.configService.doKey, this.configService.doSecret, undefined)
  })

  constructor(
    @InjectRepository(AttachmentEntity)
    private attachmentRepository: Repository<AttachmentEntity>,
    private configService: ConfigService,
    private userConfigService: UserConfigService,
    private authService: AuthService,
    private proxyService: MyClaimProxyService
  ) {}

  private getFileType(extension: string): TypeOfAttachment {
    const lowerCaseExtension = extension.toLowerCase()
    if (dictForFileType.hasOwnProperty(lowerCaseExtension)) {
      return dictForFileType[lowerCaseExtension]
    }
    return TypeOfAttachment.photo
  }

  async uploadToS3(file: File, Key: string): Promise<AWS.S3.ManagedUpload.SendData> {
    try {
      if (!file) {
        throw new BadRequestException('file not attached')
      }

      const params = {
        Bucket: 'wedat-cdn',
        Key: `${Key}/${file.originalname}`
      }

      const input = {
        ...params,
        Body: file.buffer,
        ContentEncoding: file.encoding,
        ContentType: file.mimetype,
        ACL: 'public-read'
      }

      return this.uploadFileToS3WithParams(input)
    } catch (error) {
      throw new BadRequestException(error.message)
    }
  }

  async uploadFileToS3WithParams(params: AWS.S3.Types.PutObjectRequest) {
    try {
      return this.s3.upload(params).promise()
    } catch (error) {
      throw new BadRequestException(error.message)
    }
  }

  async uploadFile(file: File, key: string) {
    const uploadedFile = await this.uploadToS3(file, key)

    const fileExtension = file.originalname.split('.').pop()

    if (!fileExtension) {
      throw new BadRequestException('File extension is not defined')
    }

    const fileType = this.getFileType(fileExtension)

    const newFile = this.attachmentRepository.create({
      type: fileType,
      link: uploadedFile.Key,
      extension: fileExtension
    })

    return this.attachmentRepository.save(newFile)
  }

  async deleteAttachment(id: number) {
    try {
      const attachment = await this.attachmentRepository.findOne({ where: { id } })

      if (!attachment) {
        throw new Error('Attachment not found')
      }

      const params = {
        Bucket: 'wedat-cdn',
        Key: attachment.link
      }

      await this.s3.deleteObject(params).promise()
      const result = await this.attachmentRepository.delete(id)
      if (result.affected === 1) {
        return {
          deleted: true
        }
      }
    } catch (error) {
      throw new BadRequestException(error.message)
    }
  }

  @_MeasureServiceDuration()
  async getAttachmentsWithIds(contractId, headers) {
    const authToken = headers[HeadersNames.DatAuthToken]
    const network = headers.network || Networks.ANIA

    const body: ProxyRequest = {
      url: `/claims/${contractId}/folders`,
      method: 'GET',
      api: 'rest',
      network,
      params: {
        calculationPhotos: false,
        embedded: ['attachments']
      }
    }

    const [attachmentsList, attachmentsListWithIds] = await Promise.all([
      this.getAttachmentsList(contractId, authToken),
      this.proxyService.proxy(body, authToken) as Promise<FolderOfClaim[]>
    ])

    const attachmentMap = new Map(attachmentsList.map(att => [att.fileName + att.uploaded + att.published, att]))

    const filteredAttachments = attachmentsListWithIds
      .flatMap(item => item.attachments)
      .filter(att => attachmentMap.has(att.fileName + att.uploaded + att.published))
      .map(att => ({
        id: att.id,
        ...attachmentMap.get(att.fileName + att.uploaded + att.published)
      }))

    return filteredAttachments as AttachmentsResponse[]
  }

  @_MeasureServiceDuration()
  async getAttachments(headers, { contractId, attachmentIds, printoutIds, isSVDExportEnabled }, response) {
    const authToken = headers[HeadersNames.DatAuthToken]

    try {
      const [contract, attachmentsListWithIds, printout] = await Promise.all([
        this.getContract(contractId, authToken),
        this.getAttachmentsWithIds(contractId, headers),
        this.getPrintReport(contractId, authToken, printoutIds)
      ])

      const filteredAttachmentsInRightOrder = attachmentIds.length
        ? attachmentsListWithIds
            .filter(({ id }) => attachmentIds.includes(id))
            .sort((a, b) => attachmentIds.indexOf(a.id) - attachmentIds.indexOf(b.id))
        : []

      const renamedAttachments = renameAttachments(filteredAttachmentsInRightOrder)

      const svdEntries = isSVDExportEnabled === 'true' ? await this.generateSVDEntries(contract) : null

      const filesToZip = await this.prepareFilesToZip(renamedAttachments, svdEntries, printout)

      response.setHeader('Content-Type', 'application/zip')
      response.setHeader('Content-Disposition', 'attachment; filename="attachments.zip"')

      const zipFileStream = await createZipArchive(filesToZip)
      zipFileStream.pipe(response)
    } catch (error) {
      return response.status(500).send(`Query params are not valid: ${error.code}`)
    }
  }

  async convertToPdf(headers, { contractId, uploadId, ids, templateId, filename = '' }) {
    const authToken = headers[HeadersNames.DatAuthToken]
    const attachmentsListFromContract = await this.getAttachmentsList(contractId, authToken)

    const attachmentsListWithIds = await this.getAttachmentsWithIds(contractId, headers)

    const attachmentsMetadata = attachmentsListWithIds.filter(item => ids.includes(item.id))

    const filteredAttachmentsList = attachmentsListFromContract
      .filter(attachment => attachment.mimeType !== 'document/pdf')
      .filter(attachment =>
        attachmentsMetadata.some(item => attachment.uploaded === item.uploaded && attachment.fileName === item.fileName)
      )

    const pdfBuffer = await this.convertJpegBinaryToPdf(filteredAttachmentsList, templateId)

    const options: DAT5.Options = {
      credentials: {
        'dat-authorizationtoken': authToken
      }
    }

    const result = await DAT5.MyClaimExternalService.uploadAttachmentByFolderID(
      {
        contractId,
        attachmentItem: {
          fileName: `${filename || uuidv4()}.pdf`,
          mimeType: 'document/pdf',
          binaryData: pdfBuffer,
          documentID: uploadId
        }
      },
      options
    )

    return result
  }
  catch(error) {
    console.error('Error:', error.data)
    throw error
  }

  @_MeasureServiceDuration()
  async getAttachmentsList(
    contractId: number,
    authToken: string
  ): Promise<DAT5.MyClaimExternalService_schema1.attachmentItem[]> {
    const options: DAT5.Options = {
      credentials: {
        'dat-authorizationtoken': authToken
      }
    }
    const response = await DAT5.MyClaimExternalService.listAttachmentsOfContract({ contractId }, options)

    return response?.responseObj?.['return'] || []
  }

  @_MeasureServiceDuration()
  async getPrintReport(contractId: number, authToken: string, printoutIds: string): Promise<any> {
    try {
      const options: DAT5.Options = {
        credentials: {
          'dat-authorizationtoken': authToken
        }
      }

      const userConfig = await this.userConfigService.getUserConfig(Number(this.authService.customerNumber))

      const parsedPrintoutIds = JSON.parse(printoutIds).map(id => Number(id))

      let templates = {}

      for (const key in userConfig?.config?.templates) {
        if (Object.hasOwnProperty.call(userConfig?.config?.templates, key)) {
          const element = userConfig?.config?.templates[key]

          if (element.products) {
            templates = { ...templates, ...element.products }
          }
        }
      }

      const printouts = templates['printout'].printoutReports.flatMap(group =>
        group.reports.filter(report => parsedPrintoutIds.includes(report.printReportId))
      )

      const printoutBinariesPromises = printouts.map(async (element: any) => {
        try {
          const response = await DAT5.MyClaimExternalService.getPrintReport(
            { contractId, reportIdentification: element.id },
            options
          )
          return { ...response.responseObj['return'], id: element }
        } catch (error) {
          console.error(`Error fetching print report for element ${element.id}:`, error.code)
          return null
        }
      })

      const printoutBinaries = await Promise.all(printoutBinariesPromises)

      return printoutBinaries.filter(Boolean) || []
    } catch (error) {
      console.error('Error in getPrintReport request:', error.message)
      throw error
    }
  }

  @_MeasureServiceDuration()
  async getContract(
    contractId: number,
    authToken: string
  ): Promise<DAT5.MyClaimExternalService_schema1.contractDetails | null> {
    const options: DAT5.Options = {
      credentials: {
        'dat-authorizationtoken': authToken
      }
    }

    const response = await DAT5.MyClaimExternalService.getContract(
      { contractId, isWithHistoricalCalculations: true },
      options
    )
    return response.responseObj['return'] || null
  }

  generateSVDEntries(contract: DAT5.MyClaimExternalService_schema1.contractDetails | null) {
    const { customTemplateData, Dossier } = contract || {}
    const { entry } = customTemplateData || {}
    const { TradingData = {}, Vehicle = {}, RepairOrder = {}, RepairCalculation = {} } = Dossier || {}
    const templateEntries = getParsedArraySafe(entry)

    const BEMERKUNGMEMO = markdownToPlainText(getValueFromTemplateEntries(templateEntries, 'repairDescription'))
    const ALTSCHADENMEMO = markdownToPlainText(getValueFromTemplateEntries(templateEntries, 'oldDamageDescription'))
    const SCHADENMEMO = markdownToPlainText(getValueFromTemplateEntries(templateEntries, 'dynamicDescription'))
    const ANMERKUNGMEMO = markdownToPlainText(getValueFromTemplateEntries(templateEntries, 'repairAnnotation'))
    const MITTEILUNGSMEMO = markdownToPlainText(getValueFromTemplateEntries(templateEntries, 'announcementDescription'))

    const svdText = createTextForSVDFileFromContracts({
      templateEntries,
      Dossier,
      TradingData,
      VehicleData: Vehicle,
      RepairOrder,
      RepairCalculation,
      additionalFields: {
        BEMERKUNGMEMO: BEMERKUNGMEMO ? 'BEMERKUNGMEMO.txt' : '',
        ALTSCHADENMEMO: ALTSCHADENMEMO ? 'ALTSCHADENMEMO.txt' : '',
        SCHADENMEMO: SCHADENMEMO ? 'SCHADENMEMO.txt' : '',
        ANMERKUNGMEMO: ANMERKUNGMEMO ? 'ANMERKUNGMEMO.txt' : '',
        MITTEILUNGSMEMO: MITTEILUNGSMEMO ? 'MITTEILUNGSMEMO.txt' : ''
      }
    })
    const svdEntries: SvdEntries = []
    if (svdText) svdEntries.push({ binaryData: Buffer.from(svdText, 'utf-8'), fileName: 'result.svd' })
    if (BEMERKUNGMEMO)
      svdEntries.push({
        binaryData: Buffer.from(BEMERKUNGMEMO, 'utf-8'),
        fileName: `result/BEMERKUNGMEMO.txt`
      })
    if (ALTSCHADENMEMO)
      svdEntries.push({
        binaryData: Buffer.from(ALTSCHADENMEMO, 'utf-8'),
        fileName: `result/ALTSCHADENMEMO.txt`
      })
    if (SCHADENMEMO)
      svdEntries.push({
        binaryData: Buffer.from(SCHADENMEMO, 'utf-8'),
        fileName: `result/SCHADENMEMO.txt`
      })
    if (ANMERKUNGMEMO)
      svdEntries.push({
        binaryData: Buffer.from(ANMERKUNGMEMO, 'utf-8'),
        fileName: `result/ANMERKUNGMEMO.txt`
      })
    if (MITTEILUNGSMEMO)
      svdEntries.push({
        binaryData: Buffer.from(MITTEILUNGSMEMO, 'utf-8'),
        fileName: `result/MITTEILUNGSMEMO.txt`
      })

    return svdEntries
  }

  async prepareFilesToZip(attachmentsList: any, svdEntries: SvdEntries | null, printoutList: any[]): Promise<any> {
    const userConfig = await this.userConfigService.getUserConfig(Number(this.authService.customerNumber))
    const isATCountry = userConfig?.country?.toLowerCase() === 'at'
    let printoutBinaries
    let attachmentBinaries

    if (isATCountry) {
      printoutBinaries = printoutList.map(item => ({
        binaryData: Buffer.from(item.binaryData, 'base64'),
        fileName: `result/${item.id.description}.pdf`
      }))

      attachmentBinaries = attachmentsList.map(attachment => ({
        binaryData: Buffer.from(attachment.binaryData, 'base64'),
        fileName: `result/${attachment.fileName}`
      }))
    } else {
      printoutBinaries = printoutList.map(item => ({
        binaryData: Buffer.from(item.binaryData, 'base64'),
        fileName: `${item.id.description}.pdf`
      }))

      attachmentBinaries = attachmentsList.map(attachment => ({
        binaryData: Buffer.from(attachment.binaryData, 'base64'),
        fileName: attachment.fileName
      }))
    }

    const filesToZip = [
      ...(svdEntries || []),
      ...attachmentBinaries.map(({ binaryData, fileName }) => ({
        binaryData,
        fileName: fileName.replace(/\.(jpe?g)$/i, '.jpg')
      })),
      ...printoutBinaries
    ].reverse() // magic .reverse() is needed due order when downloading on the client

    return filesToZip
  }

  async convertJpegBinaryToPdf(attachmentsList, templateId): Promise<string> {
    const userConfig = await this.userConfigService.getUserConfig(Number(this.authService.customerNumber))
    const imagesPerPage: number =
      userConfig?.config?.templates?.[templateId]?.products?.gallery?.pdf?.imagesPerPage ||
      userConfig?.config?.templates?.default?.products?.gallery?.pdf?.imagesPerPage ||
      DEFAULT_IMAGES_PER_PAGE

    const pdfDoc = new PDFDocument({ autoFirstPage: false })

    const attachmentWithDimmension = attachmentsList.map(attachment => {
      const imageBuffer = Buffer.from(attachment.binaryData, 'base64')
      const dimensions = sizeOf(imageBuffer)

      return {
        imageBuffer,
        dimensions
      }
    })

    const pdfAttachments = getPreparedDataForPdf(attachmentWithDimmension, imagesPerPage)

    // Create the first blank page
    pdfDoc.addPage({ size: 'A4' })

    for (const attachment of pdfAttachments) {
      const {
        isRotate,
        scaleWidth,
        scaleHeight,
        adjustedX,
        adjustedY,
        imageBuffer,
        imageHeight,
        imageWidth,
        x,
        y,
        withNewPage
      } = attachment

      if (isRotate) {
        pdfDoc
          .save()
          .translate(adjustedX, adjustedY)
          .rotate(90)
          .image(imageBuffer, -scaleHeight, -scaleWidth, { width: imageHeight, height: imageWidth })
          .restore()
      } else {
        pdfDoc.image(imageBuffer, x, y, { width: imageWidth, height: imageHeight })
      }

      if (withNewPage) {
        pdfDoc.addPage({ size: 'A4' })
      }
    }

    return new Promise<string>((resolve, reject) => {
      const buffers: Buffer[] = []

      pdfDoc.on('data', chunk => buffers.push(chunk))
      pdfDoc.on('end', () => {
        const pdfBuffer = Buffer.concat(buffers)
        const pdfBase64 = pdfBuffer.toString('base64')
        resolve(pdfBase64)
      })
      pdfDoc.on('error', error => {
        reject(error)
      })

      pdfDoc.end()
    })
  }
}
