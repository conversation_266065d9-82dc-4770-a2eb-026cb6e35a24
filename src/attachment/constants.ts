export const SVD_FILE_TEMPLATE: Record<string, Record<string, string> | string> = {
  '[ABSENDER.1.1]': {
    PROGRAMM: 'KFZ5',
    PROGRAMMVERSION: '5.22.4',
    DBVERSION: '2006',
    ERSTELLUNGSDATUM: 'TODAY',
    ANZ_OE: '0'
  },
  '[INHALT.1]': {
    INFO: 'VehicleDescription',
    SVD_VERSION: '2.0',
    SVD_SUBVERSION: '2006',
    SVD_TYP: 'BEGUTACHTUNG',
    ANZ_ABSENDER: '1'
  },
  '[ALG.1.1]': {
    DECKUNGSART: 'TypeOfInsurance',
    POLIZZENR: 'PolicyNumber',
    SCHADENNR: 'DamageNumber',
    VN_KENNZ: 'licencePlateInsurant',
    HINWEIS: 'importantNote1',
    HINWEIS2: 'importantNote2',
    REPWEG_EINSPAR: 'importantNote3',
    BEMERKUNG_MEMO: 'BEMERKUNGMEMO' // txt contain value from memo repairDescription with richtext
  },
  '[BEG.1]': {
    BEGUTACHTUNGSSTATUS: '',
    ERSTELLUNGSDATUM: 'CreateDate',
    FREMDID_WERT: 'ContractNumber',
    GUA_NR: '',
    GUA_INFO: 'contractInformation',
    FREMDID_BEZ: 'Kontroll-Nr',
    FREMDID_RECHT: '1',
    BEG_WRACKBOERSE: '0',
    BEG_WB_EMPFAENGER: '',
    PROGVERSION: '5.22.4',
    DBVERSION: '2006',
    BEG_REPARIERBAR: 'repairable',
    BEG_AUFTRAGSSYSTEM: '1',
    BEG_AUFTRAGSMODUS: '1',
    INFO: 'VehicleDescription',
    BEG_SVNAME: 'TradingData',
    BEG_SVFOTO: '',
    BEG_WERKSTATTFOTO: '',
    ANZ_FZG: '1',
    ANZ_BTG: '2',
    ANZ_BES: 'inspectionHistoryCount',
    ANZ_ALG: '1',
    ANZ_WHKZSF: '1',
    DAT_RVP_WERT: 'residualValueAmount',
    DAT_RVP_NAME: 'residualValueDealer'
  },
  '[BES.1.X]': 'inspectionHistoryArray',
  '[FZG.1.1]': {
    FAHRGESTELLNR: 'VehicleIdentNumber',
    KENNZEICHEN1: 'LicenseNumber',
    FZG_MARKE_TXT: 'vbrand',
    FZG_MODELL_TXT: 'vmodel',
    FZG_ART_TXT: 'VehicleDescription',
    FZG_TYPE_TXT: 'vtype',
    FZG_NATIONALCODE: 'nationalCodeAustria',
    FZG_GDHSTATUS: '0',
    FZG_BEWGRUPPE: 'VehicleType',
    FZG_BEWLAND: 'AT',
    LETZTZULASSUNG: 'vehicleLastRegistration',
    SITZPLAETZE: 'vehicleSeats',
    GESAMTGEWICHT: 'permissableTotalWeight',
    NUTZLAST: 'permissableTotalWeightWithUnloadedWeight',
    LACKZUSTAND: 'lacquerState',
    LACKFARBE: 'vehicleColorDescription',
    LACKART: 'vehicleColorType',
    PLAKETTE_BIS: 'vehicleSafteyInspectionUpcomingDate',
    EIGENGEWICHT: 'unloadedWeight',
    ALLGEMEINZUSTAND: 'vehicleState'
  },
  '[PKW_AT.1.1.1]': {
    HUBRAUM: 'vehicleCC',
    LEISTUNG: 'vehicleKW',
    ERSTZULASSUNG: 'InitialRegistration',
    TACHO_IST: 'MileageOdometer',
    MOTORART: '',
    NW_ERRECHNET: 'NW_ERRECHNET', // Depends on the value of valuationKindActive, same logic as in APS calls ListPrice of the vehicle
    WBW_ERRECHNET: 'salesPriceGrossByActiveValuation',
    WERTMINDERUNG_WERT: 'manfredImpairment',
    RESTWERT_WERT: 'residualValueAmount',
    ALTSCHADEN_MEMO: 'ALTSCHADENMEMO' // txt contain value from memo oldDamageDescription with richtext
  },
  '[WHK.1.1.1]': {
    SCHADENNUMMER: 'DamageNumber',
    SCHADENDATUM: 'DamageDate',
    ANSTOSSBEREICH: 'impactArea',
    SCHADENART: 'damageCategory',
    SS_SPMO_LK1: 'bodyWage1',
    ST_SPMO: 'ST_SPMO',
    SS_LACK_LK1: 'SS_LACK_LK1', // wage of the selected paintsystem (AZT/Manufacturer/Eurolaque)
    ST_LACK: 'ST_LACK',
    NEBENKOSTEN_PRZ: '',
    SCHADENMEMO: 'SCHADENMEMO' // txt contain value from memo dynamicDescription with richtext
  },
  '[WHKEVAL.*******]': {
    ERSATZTEILEGESAMT: 'AllSum',
    WHKOSTENBRUTTO: 'TotalGrossCorrected',
    UNTERBODEN: '0',
    BESCHAFFUNGSKOSTEN: 'ProcurementCostsFromParts',
    ENTSORGUNG: 'DisposalCostsSpareParts',
    KLEINMATERIAL: 'SumSmallSparePartCosts',
    LOHNERSATZTEILE: 'LabourCostsTotalSum',
    LOHNLACKIERUNG: 'LacquerCostsWagePrice',
    LACKMATERIALGESAMT: 'LacquerCostsMaterialTotalSum'
  },
  '[WHKZSF.1.1]': {
    ABLOESE: 'CompensationValueTag',
    ABLOESE_ART: '2',
    ABLOESE_IBAN: 'compensationIBAN',
    ABLOESE_BANK: 'compensationBank',
    ABLOESE_BIC: 'compensationBIC',
    ABLOESE_WERT: 'CompensationValue',
    UST_ABLOESE: 'CompensationIncludesVAT',
    NEBENKOSTENPAUSCHALE: 'AdditionalCostsFlatAmount',
    SCHADENART: 'DamageCategory',
    WHKZSF_SUMMEBRUTTO: 'TotalGrossCorrected',
    WHKZSF_SUMMENETTO: 'TotalNetCorrected',
    ANMERKUNG_MEMO: 'ANMERKUNGMEMO', // txt contain value from memo repairAnnotation with richtext
    MITTEILUNGSMEMO: 'MITTEILUNGSMEMO' // txt contain value from memo announcementDescription with richtext
  },
  '[DATA1]': {
    VERSICHERUNG: 'insurance_id'
  },
  '[SON.1.1.1]': {
    SON_PREIS: 'SON_PREIS' // Depends on the value of valuationKindActive, same logic as in APS calls Sum of all special equipment
  },
  '[BTG.1.1]': {
    BTG_TYP: '24',
    BTG_NAME1: 'InsuredFullName', // Insured subject, name + „ „ + surname
    BTG_EMAIL: 'InsuredMail',
    BTG_TELEFON: 'InsuredPhone',
    BTG_HANDY: 'InsuredMobilePhone',
    BTG_STRASSE: 'InsuredAddress',
    BTG_PLZ: 'InsuredZip',
    BTG_ORT: 'InsuredCity'
  },
  '[BTG.1.2]': {
    BTG_TYP: '25',
    BTG_NAME1: 'OwnerFullName', // owner subject, name + „ „ + surname
    BTG_EMAIL: 'OwnerMail',
    BTG_TELEFON: 'OwnerPhone',
    BTG_HANDY: 'OwnerMobilePhone',
    BTG_STRASSE: 'OwnerAddress',
    BTG_PLZ: 'OwnerZip',
    BTG_ORT: 'OwnerCity'
  }
}
