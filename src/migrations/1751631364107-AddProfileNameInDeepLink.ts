import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddProfileNameInDeepLink1751631364107 implements MigrationInterface {
  name = 'AddProfileNameInDeepLink1751631364107'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "deeplink" ADD "profileName" character varying`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "deeplink" DROP COLUMN "profileName"`)
  }
}
