import { Injectable } from '@nestjs/common'
import { TypeOrmModuleOptions } from '@nestjs/typeorm'
import * as dotenv from 'dotenv'

import { TECH_PREFIX_FOR_NUMBER_ENVS } from './constants'

import { FuelEntity } from '../fuel/fuel.entity'
import { PartnerSchema } from '../partners/persistance'
import { Customer } from '../customer/persistance/customer.entity'
import { Token } from '../token-module/persistence/token.entity'
import { UserConfigSchema } from '../user-config/persistance/user-config.entity'
import { HistoryEntity } from '../history/entities/history.entity'
import { ParentUserSchema } from '../user-config/persistance/parent-config.entity'
import { NotificationSchema } from '../notifications/persistance/notification.entity'
import { InternalCustomer } from '../auth/persistance/internal-customer.entity'
import { ReleaseEntity } from '../release/persistance/release.entity'
import { AttachmentEntity } from '../attachment/persistance/attachment.entity'
import { FeatureEntity } from '../release/persistance/feature.entity'
import { MaintenanceEntity } from '../maintenance/persistance/maintenance.entity'
import { TranslationSchema } from '../translations/persistance'
import { InfosEntity } from '../infos/entities/infos.entity'
import { DeeplinkEntity } from '../deeplink/persistance/deeplink.entity'
import { AiCommandEntity } from '../ai/persistence/ai-command.entity'
import { ActivityManagerEntity } from '../activity-manager/persistance/activity-manager.entity'
import { InspectionHistoryEntity } from '../inspection-history/persistance/inspection-history.entity'
import { UserGuideEntity } from '../user-guide/persistance/user-guide.entity'
import { UserConfigHistorySchema } from '../user-config/persistance/user-config-history.entity'
import { PreviewEntity } from '../preview/persistance/preview.entity'
import { FavoriteConfigSchema } from '../user-config/persistance/favorite-config.entity'
import { ClaimEventEntity } from '../productivity/claim-event.entity'

dotenv.config()

const requiredParam = Symbol()
const getFromEnv = (key: string, fallback: any = requiredParam) => {
  const value = process.env[key]
  if (value === undefined) {
    if (fallback !== requiredParam) {
      return fallback
    }
    console.warn(`Required ENV variable missing: ${key}`)
  }
  if (value) {
    return value.trim()
  }
  console.warn(`Required ENV variable missing: ${key}`)
  return ''
}

@Injectable()
export class ConfigService {
  public readonly dbClient = getFromEnv('DATABASE_CLIENT')
  public readonly dbHost = getFromEnv('DATABASE_HOST')
  public readonly dbPort = getFromEnv('DATABASE_PORT')
  public readonly dbName = getFromEnv('DATABASE_NAME')
  public readonly dbUser = getFromEnv('DATABASE_USERNAME')
  public readonly dbPassword = getFromEnv('DATABASE_PASSWORD')
  public readonly doSecret = getFromEnv('DO_SPACES_SECRET')
  public readonly doKey = getFromEnv('DO_SPACES_KEY')
  public readonly spaceName = getFromEnv('DO_SPACES_NAME')
  public readonly aspisUrlDev = getFromEnv('ASPIS_URL_DEV')
  public readonly aspisUrlProd = getFromEnv('ASPIS_URL_PROD')
  public readonly aspisUsernameDev = getFromEnv('ASPIS_USERNAME_DEV')
  public readonly aspisUsernameProd = getFromEnv('ASPIS_USERNAME_PROD')
  public readonly aspisPasswordDev = getFromEnv('ASPIS_PASSWORD_DEV')
  public readonly aspisPasswordProd = getFromEnv('ASPIS_PASSWORD_PROD')
  public readonly partsCompletionUrl = getFromEnv('PARTS_COMPLETION_URL')
  public readonly plateSearchItalyUser = getFromEnv('PLATE_SEARCH_ITALY_USER')
  public readonly plateSearchItalyPassword = getFromEnv('PLATE_SEARCH_ITALY_PASSWORD')
  public readonly postaProntaUrl = getFromEnv('POSTA_PRONTA_URL')
  public readonly postaProntaUser = getFromEnv('POSTA_PRONTA_USER')?.replace(TECH_PREFIX_FOR_NUMBER_ENVS, '')
  public readonly postaProntaPassword = getFromEnv('POSTA_PRONTA_PASSWORD')
  public readonly slackNotificationToken = getFromEnv('SLACK_NOTIFICATION_TOKEN')
  public readonly googleApiKey = getFromEnv('GOOGLE_API_KEY')
  public readonly jwtSecret = getFromEnv('JWT_SECRET')
  public readonly environment = getFromEnv('NODE_ENV')
  public readonly baseUrl = getFromEnv('BASE_URL')
  public readonly apiUrl = getFromEnv('API_URL')
  public readonly emailUser = getFromEnv('REPORT_EMAIL_USER')
  public readonly emailPassword = getFromEnv('REPORT_EMAIL_PASSWORD')
  public readonly emailRecipients = getFromEnv('REPORT_EMAIL_RECIPIENTS')
  public readonly mfaEmailUser = getFromEnv('MFA_EMAIL_USER')
  public readonly mfaEmailPassword = getFromEnv('MFA_EMAIL_PASSWORD')
  public readonly username = getFromEnv('PART_COMPLETION_USERNAME')
  public readonly password = getFromEnv('PART_COMPLETION_PASSWORD')
  public readonly customerNumber = getFromEnv('PART_COMPLETION_CUSTOMER_NUMBER')
  public readonly proxyItalyApiUrl = getFromEnv('PROXY_ITALY_API_URL')
  public readonly openAiApiKey = getFromEnv('OPEN_AI_API_KEY')
  public readonly grapaDataBucketUrl = getFromEnv(
    'GRAPA_DATA_BUCKET_URL',
    'https://grapa-data.fra1.digitaloceanspaces.com'
  )
  public readonly bucketUrl = getFromEnv('VEHICLE_DATA_BUCKET_URL', 'https://fra1.digitaloceanspaces.com')
  public readonly vehicleDataEndpoint = getFromEnv('VEHICLE_DATA_ENDPOINT', 'https://fra1.digitaloceanspaces.com')
  public readonly region = getFromEnv('VEHICLE_DATA_REGION', 'REGION')
  public readonly Bucket = getFromEnv('VEHICLE_DATA_BUCKET', 'vehicle-static-data')
  public readonly accessKeyId = getFromEnv('VEHICLE_DATA_ACCESS_KEY_ID', 'DO00GK79LF4WMZVBENNH')
  public readonly secretAccessKey = getFromEnv('VEHICLE_DATA_SECRET_KEY', 'HlU3I+mlQixONEehSTIXft6NShgxvUu1SCRfbSX4LlA')
  public readonly vehicleStaticDataPathRoot = getFromEnv('VEHICLE_DATA_PATH_ROOT', '')
  public readonly wedatApiKey = getFromEnv('WEDAT_API_KEY')
  public readonly imageAnalysisUrl = getFromEnv('IMAGE_ANALYSIS_URL')
  public readonly imageServiceUrl = getFromEnv('IMAGE_SERVICE_URL')
  public readonly imageAnalysisInterfacePartnerNumber = getFromEnv('IMAGE_ANALYSIS_INTERFACE_PARTNER_NUMBER')
  public readonly imageAnalysisPartnerSignature = getFromEnv('IMAGE_ANALYSIS_INTERFACE_PARTNER_SIGNATURE')
  public readonly getCoordApiKey = getFromEnv('GET_COORD_API_KEY')
  public readonly datibericaUrl = getFromEnv('DATIBERICA_URL', 'https://datiberica.es')
  public readonly datibericaServicesUrl = getFromEnv('DATIBERICA_SERVICES_URL', 'https://services.datiberica.es')
  public readonly datibericaCustomerNumber = getFromEnv('DATIBERICA_CUSTOMER_NUMBER', '3300000')
  public readonly datibericaCustomerLogin = getFromEnv('DATIBERICA_CUSTOMER_LOGIN', 'eslicpla')
  public readonly datibericaCustomerSignature = getFromEnv(
    'DATIBERICA_CUSTOMER_SIGNATURE',
    'akEwRUF3TUN6UkgySGk3MWtnaGd5U2NzY05ienRORzIwSXBmTCtkS3hWOE5CSkt0Uk8zb0dETXZ4UnhHdzF5ZkF4YklJVFhGWlk0PQ=='
  )
  public readonly datgroupUrl = getFromEnv('DATGROUP_URL', 'https://www.datgroup.com')

  getTypeOrmConfig(): TypeOrmModuleOptions {
    const isProd = this.getEnvironment() === 'production'
    const isLocal = this.getEnvironment() === 'local'

    const securityConfig = isLocal
      ? {
          synchronize: false,
          ssl: false
        }
      : isProd
      ? {
          synchronize: false,
          ssl: true,
          extra: {
            ssl: {
              rejectUnauthorized: false,
              ca: 'ca-certificate-prod.crt'
            }
          }
        }
      : {
          synchronize: false,
          ssl: true,
          extra: {
            ssl: {
              rejectUnauthorized: false,
              ca: 'ca-certificate-dev.crt'
            }
          }
        }
    return {
      type: 'postgres',
      host: this.dbHost,
      port: this.dbPort,
      username: this.dbUser,
      password: this.dbPassword,
      database: this.dbName,
      migrationsTableName: 'table-migrations',
      migrations: [process.cwd() + '/dist/migrations/*.js'],
      entities: [
        Customer,
        Token,
        PartnerSchema,
        FuelEntity,
        HistoryEntity,
        ReleaseEntity,
        AttachmentEntity,
        FeatureEntity,
        MaintenanceEntity,
        InfosEntity,
        UserConfigSchema,
        ParentUserSchema,
        FavoriteConfigSchema,
        UserConfigHistorySchema,
        NotificationSchema,
        InternalCustomer,
        TranslationSchema,
        DeeplinkEntity,
        AiCommandEntity,
        ActivityManagerEntity,
        InspectionHistoryEntity,
        UserGuideEntity,
        PreviewEntity,
        ClaimEventEntity
      ],
      ...securityConfig
    }
  }

  getStaticVehicleDataConfig() {
    return {
      endpoint: this.vehicleDataEndpoint,
      region: this.region,
      Bucket: this.Bucket,
      accessKeyId: this.accessKeyId,
      secretAccessKey: this.secretAccessKey,
      vehicleStaticDataPathRoot: this.vehicleStaticDataPathRoot
    }
  }

  getEnvironment(): string {
    return this.environment
  }

  getJwtSecret(): string {
    return this.jwtSecret
  }

  getPlateSearchItalyData(): {
    plateSearchItalyUser: string
    plateSearchItalyPassword: string
  } {
    return {
      plateSearchItalyUser: this.plateSearchItalyUser,
      plateSearchItalyPassword: this.plateSearchItalyPassword
    }
  }

  getAspisData(): {
    aspisUrlDev: string
    aspisUrlProd: string
    aspisUsernameDev: string
    aspisUsernameProd: string
    aspisPasswordDev: string
    aspisPasswordProd: string
  } {
    return {
      aspisUrlDev: this.aspisUrlDev,
      aspisUrlProd: this.aspisUrlProd,
      aspisUsernameDev: this.aspisUsernameDev,
      aspisUsernameProd: this.aspisUsernameProd,
      aspisPasswordDev: this.aspisPasswordDev,
      aspisPasswordProd: this.aspisPasswordProd
    }
  }

  getReportData(): {
    emailUser: string
    emailPassword: string
    emailRecipients: string
  } {
    return {
      emailUser: this.emailUser,
      emailPassword: this.emailPassword,
      emailRecipients: this.emailRecipients
    }
  }

  getMFAEmailData(): {
    emailUser: string
    emailPassword: string
  } {
    return {
      emailUser: this.mfaEmailUser,
      emailPassword: this.mfaEmailPassword
    }
  }

  getPartCompletionData(): {
    username: string
    password: string
    customerNumber: string
    url: string
  } {
    return {
      username: this.username,
      password: this.password,
      customerNumber: this.customerNumber,
      url: this.partsCompletionUrl
    }
  }

  getDatibericaUrls(): {
    url: string
    servicesUrl: string
  } {
    return {
      url: this.datibericaUrl,
      servicesUrl: this.datibericaServicesUrl
    }
  }

  getDatibericaCredentials(): {
    customerNumber: string
    customerLogin: string
    customerSignature: string
  } {
    return {
      customerNumber: this.datibericaCustomerNumber,
      customerLogin: this.datibericaCustomerLogin,
      customerSignature: this.datibericaCustomerSignature
    }
  }

  getDatgroupUrl(): string {
    return this.datgroupUrl
  }
}
