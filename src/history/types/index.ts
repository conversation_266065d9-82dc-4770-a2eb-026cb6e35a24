import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'

import { BaseModel } from 'src/common/types/common'

export class History extends BaseModel {
  @ApiProperty({
    enum: ['opened', 'emailSent', 'statusChange', 'vehicleChanged', 'labourRatesChanged'],
    type: String,
    required: true
  })
  action: 'opened' | 'emailSent' | 'statusChange' | 'vehicleChanged' | 'labourRatesChanged'

  @ApiProperty({ type: Number, required: true })
  claimId: number

  @ApiProperty({ type: String, required: true })
  username: string

  @ApiProperty({ type: String, required: true })
  fullName: string

  @ApiPropertyOptional({ type: String, required: false })
  avatar?: string

  @ApiPropertyOptional({ type: String, required: false })
  role?: string

  @ApiProperty({ type: String, required: true })
  currentStatus: string

  @ApiProperty({ type: Date, required: true })
  date: Date
}

export class FindAllResponse extends History {}

export class CreateResponse extends History {}

export class FilterResponse extends History {}
