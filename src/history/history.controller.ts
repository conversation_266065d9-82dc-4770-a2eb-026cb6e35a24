import { Controller, Get, Post, Body, Query } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'

import { HistoryEntity } from './entities/history.entity'

import { HistoryService } from './history.service'

import { historyTag } from 'src/common/constants/swagger'
import { CreateResponse, FilterResponse, FindAllResponse } from './types'
import { SwaggerDoc } from 'src/common/decorators/swagger-doc.decorator'

@ApiTags(historyTag)
@Controller(historyTag)
export class HistoryController {
  constructor(private historyService: HistoryService) {}

  @Get()
  @SwaggerDoc({
    isAuth: false,
    summary: 'Get all history',
    typeResponse: FindAllResponse
  })
  async findAll(): Promise<FindAllResponse[]> {
    return this.historyService.findAll()
  }

  @Post()
  @SwaggerDoc({
    isAuth: false,
    summary: 'Get all history',
    typeResponse: CreateResponse
  })
  async create(@Body() body: HistoryEntity): Promise<CreateResponse> {
    return this.historyService.create(body)
  }

  @Get('/filter')
  @SwaggerDoc({
    isAuth: false,
    summary: 'Get all history',
    typeResponse: [FilterResponse]
  })
  async filter(@Query('username') username: string): Promise<FilterResponse[]> {
    return this.historyService.filter(username)
  }

  @Get('/claim/:id')
  @SwaggerDoc({
    isAuth: false,
    summary: 'Get all history'
  })
  async groupHistory(@Query('claimId') claimId: number) {
    return this.historyService.groupHistory(claimId)
  }
}
