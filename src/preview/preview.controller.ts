import { Body, Controller, Delete, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common'
import { Request } from 'express'
import {
  CreatePreviewDto,
  DeletePreviewsQueryDto,
  GetAllPreviewsQueryDto,
  GetAllPreviewsResponseDto,
  PreviewDto
} from 'src/preview/preview.dto'
import { ApiTags } from '@nestjs/swagger'

import { AuthGuard } from 'src/common/guards/auth.guard'

import { PreviewService } from 'src/preview/preview.service'

import { SwaggerDoc } from 'src/common/decorators/swagger-doc.decorator'

import { HeadersNames } from 'src/common/constants/headers-names'
import { previewTag } from 'src/common/constants/swagger'

@ApiTags(previewTag)
@Controller(previewTag)
export class PreviewController {
  constructor(private previewService: PreviewService) {}

  @Post()
  @SwaggerDoc({
    isAuth: false,
    summary: 'Create a new preview',
    bodySchema: CreatePreviewDto
  })
  create(@Body() data: CreatePreviewDto): Promise<void> {
    return this.previewService.create(data)
  }

  @Get()
  @UseGuards(AuthGuard)
  @SwaggerDoc({
    isAuth: true,
    summary: 'Get all previews',
    typeResponse: GetAllPreviewsResponseDto
  })
  findAll(@Req() request: Request, @Query() query: GetAllPreviewsQueryDto): Promise<GetAllPreviewsResponseDto> {
    return this.previewService.findAll(query, request.headers[HeadersNames.DatAuthToken]?.toString())
  }

  @Get(':releaseId')
  @UseGuards(AuthGuard)
  @SwaggerDoc({
    isAuth: true,
    summary: 'Get a preview by releaseId',
    typeResponse: PreviewDto
  })
  findOne(@Req() request: Request, @Param('releaseId') releaseId: string): Promise<PreviewDto> {
    return this.previewService.findOne(+releaseId, request.headers[HeadersNames.DatAuthToken]?.toString())
  }

  @Delete()
  @UseGuards(AuthGuard)
  @SwaggerDoc({
    isAuth: true,
    summary: 'Delete previews by releaseIds',
    querySchema: DeletePreviewsQueryDto
  })
  remove(@Req() request: Request, @Query() data: DeletePreviewsQueryDto): Promise<void> {
    return this.previewService.remove(data, request.headers[HeadersNames.DatAuthToken]?.toString())
  }
}
