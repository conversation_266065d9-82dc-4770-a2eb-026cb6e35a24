import { ArrayNotEmpty, IsArray, IsNumber, IsOptional, IsString, Min, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'

import { BaseModel } from 'src/common/types/common'

export class CreatePreviewDto {
  @IsString()
  @ApiProperty()
  name: string

  @IsNumber()
  @ApiProperty()
  releaseId: number

  @IsString()
  @ApiProperty()
  url: string
}

export class PreviewDto extends BaseModel {}

export class GetAllPreviewsQueryDto {
  @IsString()
  @IsOptional()
  @ApiProperty()
  search?: string

  @IsNumber()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  @ApiProperty({ required: false })
  limit?: number = 20

  @IsNumber()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  @ApiProperty({ required: false })
  page?: number = 1
}

export class PaginationDto {
  @IsNumber()
  @ApiProperty()
  page: number

  @IsNumber()
  @ApiProperty()
  limit: number

  @IsNumber()
  @ApiProperty()
  totalCount: number

  @IsNumber()
  @ApiProperty()
  totalPages: number
}

export class DeletePreviewsQueryDto {
  @ApiProperty({
    type: [Number],
    required: false,
    description: 'Array of release IDs to delete',
    example: [1, 2, 3]
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  releaseIds: number[]
}

export class GetAllPreviewsResponseDto {
  @ApiProperty({ type: [PreviewDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PreviewDto)
  data: PreviewDto[]

  @ApiProperty({ type: PaginationDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => PaginationDto)
  pagination: PaginationDto
}
