import type { TDocumentDefinitions } from 'src/print-report/types'

export const DEFAULT_STYLES: TDocumentDefinitions['defaultStyle'] = {
  fontSize: 8,
  margin: 0,
  fillColor: 'white'
}

export const PDF_MAKE_STYLES: TDocumentDefinitions['styles'] = {
  'text-bold': {
    bold: true
  },
  'table-additional': {
    italics: true
  },
  'text-green': {
    color: '#008000'
  },
  'text-silver': {
    color: '#c0c0c0'
  },
  'no-margin': {
    margin: 0
  },
  'text-center': {
    alignment: 'center'
  },
  'text-right': {
    alignment: 'right'
  }
}

export const TRANSLATIONS = {
  en: {
    reportName: 'Logbook',
    page: 'Page',
    head: {
      vehicle: 'Vehicle',
      filter: 'Filter',
      name: 'Name',
      period: 'Period',
      activityType: 'Activity type'
    },
    activityTable: {
      time: 'Time',
      work: 'Work',
      insp: 'Insp.',
      count: 'count',
      mileage: 'Mileage',
      date: 'Date',
      activityType: 'Activity',
      username: 'User',
      timeFrom: 'from',
      timeTo: 'to',
      timeDifference: 'hours',
      mileageStart: 'start',
      mileageEnd: 'end',
      privateRide: 'private',
      mileageDifference: 'work',
      allowance: 'allowance',
      sums: 'Sums',
      privateRideActivity: 'private ride',
      types: {
        'inspection on site': 'inspection on site',
        'inspection at drivein center': 'inspection at drivein center',
        'photo expertise': 'photo expertise',
        court: 'court',
        'seminar/conference': 'seminar/conference',
        meeting: 'meeting',
        'other company activities': 'other company activities',
        'expertise creation': 'expertise creation',
        holiday: 'holiday',
        'sick leave': 'sick leave',
        'lunch hour': 'lunch hour'
      }
    },
    inspectionTable: {
      status: 'Status',
      placeName: 'ZIP',
      inspectionCompany: 'Inspection Company',
      txtLossNumber: 'txtLossNumber',
      txtInsuranceNumber: 'txtInsuranceNumber',
      licenseNumber: 'LicenseNumber',
      statuses: {
        done: 'Done',
        open: 'Open',
        unsuccessful: 'Unsuccessful'
      },
      inspectionTypes: {
        onSite: 'On Site',
        driveIn: 'DriveIn',
        video: 'Video inspection',
        fotoexpertise: 'FotoExpertise'
      }
    },
    footer: {
      creationDate: 'Created on:'
    }
  },
  de: {
    reportName: 'Fahrtenbuch',
    page: 'Seite',
    head: {
      vehicle: 'Fahrzeug',
      filter: 'Filterkriterien',
      name: 'Benutzer',
      period: 'Zeitraum',
      from: 'Von',
      to: 'Von',
      activityType: 'Activity type'
    },
    activityTable: {
      time: 'Zeit',
      work: 'Arb.',
      insp: 'Bes.',
      count: 'Anzahl',
      mileage: 'Kilometerabrechnung',
      date: 'Datum',
      activityType: 'Tätigkeit',
      username: 'Benutzer',
      timeFrom: 'von',
      timeTo: 'bis',
      timeDifference: 'Stunden',
      mileageStart: 'Start',
      mileageEnd: 'Ende',
      privateRide: 'Privat',
      mileageDifference: 'Firma',
      allowance: 'KM-Geld',
      sums: 'Summen',
      privateRideActivity: 'Privatfahrt',
      types: {
        'inspection on site': 'Besichtigung vor Ort',
        'inspection at drivein center': 'DriveIn',
        'photo expertise': 'FotoExpertise',
        court: 'Gericht',
        'seminar/conference': 'Seminar',
        meeting: 'Meeting',
        'other company activities': 'Andere',
        'expertise creation': 'Gutachtenausfertigung',
        holiday: 'Urlaub',
        'sick leave': 'Krankheit',
        'lunch hour': 'Mittagessen'
      }
    },
    inspectionTable: {
      status: 'Status',
      placeName: 'PLZ',
      inspectionCompany: 'Firma',
      txtLossNumber: 'Schaden-Nr',
      txtInsuranceNumber: 'Polizze-Nr',
      licenseNumber: 'Kennzeichen',
      statuses: {
        done: 'Durchgeführt',
        open: 'Offen',
        unsuccessful: 'Abgewiesen'
      },
      inspectionTypes: {
        onSite: 'Vor Ort',
        driveIn: 'DriveIn',
        video: 'Video',
        fotoexpertise: 'Fotoexpertise'
      }
    },
    footer: {
      creationDate: 'Erstellt am:'
    }
  }
}

const REPORT_HEADER = `
  <table class="no-margin" data-pdfmake="{'layout':'noBorders', 'margin': [0, 0, 0, 0]}">
    <tr>
      <td colspan="2" class="text-bold no-margin" style="font-size: 20px;">{{t.reportName}}</td>
    </tr>
    
    {{#if vehicleDescription}}
      <tr>
        <td class="text-bold" style="font-size: 17px">{{t.head.vehicle}}:</td>
        <td class="text-bold" style="margin-left: 10px; font-size: 17px;">{{vehicleDescription}}</td>
      </tr>
    {{/if}}

    {{#if filters.length}}
      <tr>
        <td rowspan="4" class="text-bold" style="margin-top: 5px;">{{t.head.filter}}</td>
        <td></td>
      </tr>
  
      {{#each filters}}
        <tr>
          <td style="margin-left: 10px;">{{getFromObject ../t.head key}}: {{value}}</td>
        </tr>
      {{/each}}
    {{/if}}
  </table>
`

const ACTIVITIES_TABLE_HEADING = `
  <tr>
    <th colspan="3" class="no-margin"></th>
    <th class="text-bold no-margin text-center" colspan="2">{{t.activityTable.time}}</th>
    <th class="text-bold no-margin text-center">{{t.activityTable.work}}</th>
    <th class="text-bold no-margin text-center">{{t.activityTable.insp}}</th>
    <th class="text-bold no-margin text-center" colspan="4">{{t.activityTable.mileage}}</th>
    <th class="text-bold no-margin"></th>
  </tr>
  <tr>
    <th class="text-bold no-margin text-center">{{t.activityTable.date}}</th>
    <th class="text-bold no-margin">{{t.activityTable.activityType}}</th>
    <th class="text-bold no-margin text-center">{{t.activityTable.username}}</th>
    <th class="text-bold no-margin text-center">{{t.activityTable.timeFrom}}</th>
    <th class="text-bold no-margin text-center">{{t.activityTable.timeTo}}</th>
    <th class="text-bold no-margin text-center">{{t.activityTable.timeDifference}}</th>
    <th class="text-bold no-margin text-center">{{t.activityTable.count}}</th>
    <th class="text-bold no-margin text-center">{{t.activityTable.mileageStart}}</th>
    <th class="text-bold no-margin text-center">{{t.activityTable.mileageEnd}}</th>
    <th class="text-bold no-margin text-center text-silver">{{t.activityTable.privateRide}}</th>
    <th class="text-bold no-margin text-center">{{t.activityTable.mileageDifference}}</th>
    <th class="text-bold no-margin text-center">{{t.activityTable.allowance}}</th>
  </tr>
`

const PRIVATE_MILEAGE_ROW = `
  <tr class="no-margin text-silver">
    <td class="text-bold no-margin">{{inspectionDate}}</td>
    <td class="no-margin">{{../t.activityTable.[privateRideActivity]}}</td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td class="no-margin text-center">{{formatMileage mileageStart}}</td>
    <td class="no-margin text-center">{{formatMileage mileageEnd}}</td>
    <td class="no-margin text-center">{{mileageDifference}}</td>
    <td></td>
    <td></td>
  </tr>
`

const ACTIVITY_ROW = `
  <tr class="no-margin">
    <td class="text-bold no-margin">{{inspectionDate}}</td>
    <td class="no-margin">
      <b>{{getFromObject ../t.activityTable.types activityType}}</b>
      <br/>
      from {{inspectionStart.ZIP}} {{inspectionStart.city}} to {{inspectionEnd.ZIP}} {{inspectionEnd.city}}
    </td>
    <td class="no-margin text-center">{{username}}</td>
    <td class="no-margin text-center">{{inspectionStart.timeFormatted}}</td>
    <td class="no-margin text-center">{{inspectionEnd.timeFormatted}}</td>
    <td class="no-margin text-center">{{summary.timeDifferenceFormatted}}</td>
    <td class="no-margin text-center">{{inspections.length}}</td>
    <td class="no-margin text-center">{{formatMileage inspectionStart.mileage}}</td>
    <td class="no-margin text-center">{{formatMileage inspectionEnd.mileage}}</td>
    <td></td>
    <td class="no-margin text-right">{{summary.mileageDifference}}</td>
    <td class="no-margin text-right">{{summary.kmAllowance}}</td>
  </tr>
`

const INSPECTION_DATA_FOR_CURRENT_ACTIVITY_ROW = `
  <tr class="inspection-row no-margin">
      <td class="no-margin" > </td>
      <td colspan="10" class="no-margin" >
          <table class="table-additional no-margin" data-pdfmake="{'layout':'inspectionsTableLayout', 'width': '*', 'widths':['auto', '*', '*', 'auto', 'auto', 'auto'], 'margin': [0,0,0,0]}">
              <thead class="no-margin">
                <tr class="no-margin" >
                    <th class="no-margin">{{../t.inspectionTable.status}}</th>
                    <th class="no-margin">{{../t.inspectionTable.placeName}}</th>
                    <th class="no-margin">{{../t.inspectionTable.inspectionCompany}}</th>
                    <th class="no-margin">{{../t.inspectionTable.txtLossNumber}}</th>
                    <th class="no-margin">{{../t.inspectionTable.txtInsuranceNumber}}</th>
                    <th class="no-margin">{{../t.inspectionTable.licenseNumber}}</th>
                <tr>
              </thead>
              <tbody class="no-margin">
                {{#each inspections}}
                <tr class="text-green no-margin">
                    <td class="no-margin">{{getFromObject ../../t.inspectionTable.statuses inspectionHistoryStatus}}</td>
                    <td class="no-margin">{{zip}}</td>
                    <td class="no-margin">{{inspectionCompanyName}}</td>
                    <td class="no-margin">{{additionalValues.txtLossNumber}}</td>
                    <td class="no-margin">{{additionalValues.txtInsuranceNumber}}</td>
                    <td class="no-margin">{{additionalValues.licenseNumber}}</td>
                </tr>
                {{/each}}
              </tbody>
          </table>
      </td>
      <td class="no-margin"> </td>
  </tr>
`

const SUMMARY_ROW = `
  <tr class="no-margin">
    <td class="text-bold no-margin text-bold">{{t.activityTable.sums}}</td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td class="no-margin text-center text-bold">{{summary.workHoursFormatted}}</td>
    <td class="no-margin text-center">{{summary.inspectionCount}}</td>
    <td></td>
    <td></td>
    <td class="no-margin text-center text-silver text-bold">{{summary.privateRide}}</td>
    <td class="no-margin text-right text-bold">{{summary.mileage}}</td>
    <td class="no-margin text-right text-bold">{{summary.allowance}}</td>
  </tr>
`

// handlebars template usage documentation https://handlebarsjs.com/guide/
export const LOGBOOK_REPORT_TEMPLATE = `  
  ${REPORT_HEADER}

  <table data-pdfmake="{'layout':'activityTableLayout', 'width': '*', 'widths':['auto', '*', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto']}">
    <thead>    
      ${ACTIVITIES_TABLE_HEADING}
    </thead>
    
    <tbody>
      {{#each activities}}
      {{#if isPrivateMileage}}
        ${PRIVATE_MILEAGE_ROW}
      {{else}}
        ${ACTIVITY_ROW}
      
        {{#if inspections.length}}
           ${INSPECTION_DATA_FOR_CURRENT_ACTIVITY_ROW}
        {{/if}}
      {{/if}}
      {{/each}}
      
      ${SUMMARY_ROW}
    </tbody>
  </table>
`
