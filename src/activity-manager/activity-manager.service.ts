import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { format } from 'date-fns'
import { DateTime } from 'luxon'
import _ from 'lodash'

import { FieldsKey } from 'src/customer/types'
import { UserConfigService } from 'src/user-config/user-config.service'
import { InspectionHistoryService } from 'src/inspection-history/inspection-history.service'
import { PrintReportService } from 'src/print-report/print-report.service'
import { ActivityManagerEntity } from './persistance/activity-manager.entity'
import {
  ActivityTypes,
  CreateActivityDto,
  DietMoneyTypes,
  UpdateActivityDto,
  GetAllActivitiesDto,
  GetPrintReportDto,
  ReportIds
} from './dto/activity.dto'
import { AssessorVehicle, GetAllActivitiesResponse, Summary } from './types'
import {
  getCompletedFilters,
  getTranslationsLogbookReport,
  getFormattedActivities,
  getActivitiesSummary
} from './utils'
import { DEFAULT_STYLES, LOGBOOK_REPORT_TEMPLATE, PDF_MAKE_STYLES } from './constants'

@Injectable()
export class ActivityManagerService {
  constructor(
    @InjectRepository(ActivityManagerEntity)
    private readonly activityManagerRepository: Repository<ActivityManagerEntity>,
    private readonly userConfigService: UserConfigService,
    private readonly inspectionHistoryService: InspectionHistoryService,
    private readonly printReportService: PrintReportService
  ) {}

  async create(createActivityDto: CreateActivityDto): Promise<ActivityManagerEntity> {
    const activity = this.activityManagerRepository.create(createActivityDto)

    await this.updateKmAllowanceActivity(activity)
    await this.calculateActivitySummary(activity)
    await this.validateActivityData(activity)
    await this.getInspectionsDuringActivity(activity)
    return this.activityManagerRepository.save(activity)
  }

  async findAll(options: GetAllActivitiesDto): Promise<GetAllActivitiesResponse> {
    const {
      customerNumber,
      username,
      page = 1,
      limit = 10,
      sortBy = 'inspectionDate',
      sortOrder = 'ASC',
      vehicleId,
      activityType,
      startDate,
      endDate,
      withoutLimit = false
    } = options

    const query = this.activityManagerRepository
      .createQueryBuilder('activity')
      .leftJoinAndSelect('activity.inspections', 'inspections')
    if (customerNumber) query.andWhere(`activity."customerNumber" = :customerNumber`, { customerNumber })
    if (username) query.andWhere(`activity."username" = :username`, { username })
    if (vehicleId) query.andWhere(`activity."vehicle"->>'id' = :vehicleId`, { vehicleId })
    if (activityType) query.andWhere('activity."activityType" = :activityType', { activityType })
    if (startDate && endDate) {
      const normalizedStart = DateTime.fromISO(String(startDate), { setZone: true }).startOf('day').toISO()
      const normalizedEnd = DateTime.fromISO(String(endDate), { setZone: true }).endOf('day').toISO()

      query.andWhere(`activity."inspectionStart"->>'time' BETWEEN :startDate AND :endDate`, {
        startDate: normalizedStart,
        endDate: normalizedEnd
      })
    } else if (startDate) {
      const normalizedStart = DateTime.fromISO(String(startDate), { setZone: true }).startOf('day').toISO()
      query.andWhere(`activity."inspectionStart"->>'time' >= :startDate`, { startDate: normalizedStart })
    } else if (endDate) {
      const normalizedEnd = DateTime.fromISO(String(endDate), { setZone: true }).endOf('day').toISO()
      query.andWhere(`activity."inspectionEnd"->>'time' <= :endDate`, { endDate: normalizedEnd })
    }
    if (sortBy) {
      if (sortBy.includes('.')) {
        const [column, fieldName] = sortBy.split('.')
        query.orderBy(`activity."${column}"->>'${fieldName}'`, sortOrder)
      } else {
        query.orderBy(`activity.${sortBy}`, sortOrder)
      }
    }

    const total = await query.getCount()

    if (!withoutLimit) {
      query.skip((page - 1) * limit).take(limit)
    }

    const list = await query.getMany()

    return { list, total }
  }

  async findOne(id: number): Promise<ActivityManagerEntity> {
    const activity = await this.activityManagerRepository.findOne({ where: { id }, relations: ['inspections'] })
    if (!activity) throw new NotFoundException(`Activity not found`)
    return activity
  }

  async update(id: number, input: UpdateActivityDto): Promise<ActivityManagerEntity> {
    const foundActivity = await this.activityManagerRepository.findOne({ where: { id }, relations: ['inspections'] })
    if (!foundActivity) throw new NotFoundException(`Activity not found`)
    const activity = _.merge({}, foundActivity, input)

    await this.updateKmAllowanceActivity(activity)
    await this.calculateActivitySummary(activity)
    await this.validateActivityData(activity)

    if (input?.inspectionStart?.time || input?.inspectionEnd?.time) {
      await this.getInspectionsDuringActivity(activity)
    }

    return this.activityManagerRepository.save({ ...activity, id })
  }

  async getInspectionsDuringActivity(activity: ActivityManagerEntity): Promise<ActivityManagerEntity> {
    if (activity.activityType !== 'inspection on site') return activity

    const from = activity.inspectionStart.time ? new Date(activity.inspectionStart.time).toISOString() : undefined
    const to = activity.inspectionEnd.time ? new Date(activity.inspectionEnd.time).toISOString() : undefined
    const { list: inspections } = await this.inspectionHistoryService.findAll({
      customerNumber: activity.customerNumber,
      username: activity.username,
      from,
      to,
      withoutLimit: true
    })

    activity.inspections = inspections
    return activity
  }

  async remove(id: number): Promise<void> {
    const result = await this.activityManagerRepository.delete(id)
    if (result.affected === 0) {
      throw new NotFoundException(`Activity not found`)
    }
  }

  async getPrintReport({ reportId, query, locale = 'en-US' }: GetPrintReportDto): Promise<any> {
    switch (reportId) {
      case ReportIds.LOGBOOK:
        return this.getLogbookReport({ query, locale })
      default:
        throw new NotFoundException(`Report with reportId ${reportId} could not be found`)
    }
  }

  async getLogbookReport({ query, locale = 'en-US' }: { query: GetAllActivitiesDto; locale: DAT.Locale }) {
    const { list: activities } = await this.findAll({
      ...query,
      sortOrder: 'ASC',
      sortBy: 'inspectionDate',
      withoutLimit: true
    })

    const t = getTranslationsLogbookReport(locale)
    const formattedActivities = getFormattedActivities(activities)
    const completedFilters = getCompletedFilters(query)
    const summary = getActivitiesSummary(formattedActivities)
    const vehicleDescription = query.vehicleId
      ? await this.getAllAssessorVehicles({ customerNumber: query.customerNumber, username: query.username }).then(
          data => data.find(({ id }) => id === query.vehicleId)?.description
        )
      : undefined
    return this.printReportService.generatePrintReportByHtmlTemplate({
      template: LOGBOOK_REPORT_TEMPLATE,
      data: {
        t,
        creationDate: format(new Date(), 'dd.MM.yyyy'),
        username: query.username,
        startDate: query.startDate ? format(new Date(query.startDate), 'dd.MM.yyyy') : '',
        endDate: query.endDate ? format(new Date(query.endDate), 'dd.MM.yyyy') : '',
        vehicleDescription: vehicleDescription,
        activities: formattedActivities,
        filters: completedFilters,
        summary
      },
      params: {
        pageOrientation: 'landscape',
        styles: PDF_MAKE_STYLES,
        defaultStyle: DEFAULT_STYLES,
        pageMargins: [30, 60, 40, 30], // [left, top, right, bottom] or [horizontal, vertical]
        header: function (currentPage, pageCount) {
          return {
            table: {
              widths: ['*', '*'],
              body: [
                [
                  { text: '', bold: true },
                  { text: `${t.page} ${currentPage}/${pageCount}`, alignment: 'right', bold: true }
                ],
                [
                  { text: '', bold: true },
                  { text: ``, alignment: 'right', bold: true }
                ]
              ]
            },
            layout: 'noBorders',
            margin: [30, 20, 40, 0]
          }
        },
        footer: function (currentPage, pageCount) {
          return {
            columns: [
              { text: '' },
              {
                text: `${t.footer.creationDate} ${format(new Date(), 'dd.MM.yyyy')}`,
                alignment: 'right',
                fontSize: 9,
                margin: [0, 0, 0, 0]
              }
            ],
            margin: [40, 0, 40, 20]
          }
        }
      }
    })
  }

  async getAllAssessorVehicles({ customerNumber, username }: { customerNumber: number; username: string }) {
    const [{ usersSettings = {} }] = await this.userConfigService.getEditableFields<[DAT.UsersSettings]>(
      +customerNumber,
      FieldsKey.USERS_SETTINGS
    )
    return (usersSettings?.[username]?.common?.activityManager?.assessorVehicles || []) as AssessorVehicle[]
  }

  async updateKmAllowanceActivity(activity: ActivityManagerEntity) {
    if (activity.vehicle?.id) {
      const allAssessorVehicles = await this.getAllAssessorVehicles({
        customerNumber: activity.customerNumber,
        username: activity.username
      })
      const currentVehicle = allAssessorVehicles.find(({ id }) => activity.vehicle?.id === id)
      if (currentVehicle?.kind === 'Company') {
        activity.kmAllowance = 0
        activity.kmAllowanceApplicable = false
      }
    }
  }

  async calculateActivitySummary(activity: ActivityManagerEntity) {
    const {
      inspectionStart,
      inspectionEnd,
      dietMoney = 0,
      dietMoneyType = DietMoneyTypes.NO,
      inspectionDate,
      vehicle
    } = activity

    const updatedSummary: Summary = {
      timeDifference: 0,
      mileageDifference: 0,
      dietMoney: 0,
      kmAllowance: 0,
      privateRide: 0
    }

    if (inspectionStart?.time && inspectionEnd?.time) {
      const startInMilliseconds = inspectionStart.time ? new Date(inspectionStart.time).getTime() : 0
      const endInMilliseconds = inspectionEnd.time ? new Date(inspectionEnd.time).getTime() : 0
      updatedSummary.timeDifference = endInMilliseconds - startInMilliseconds
    }

    if (inspectionStart?.mileage !== undefined && inspectionEnd?.mileage !== undefined) {
      updatedSummary.mileageDifference = (inspectionEnd.mileage || 0) - (inspectionStart.mileage || 0)
    }

    if (dietMoneyType === DietMoneyTypes.NO) {
      updatedSummary.dietMoney = 0
    } else if (dietMoneyType === DietMoneyTypes.FLAT) {
      updatedSummary.dietMoney = dietMoney
    } else if (dietMoneyType === DietMoneyTypes.PER_HOUR) {
      const hours = updatedSummary.timeDifference ? updatedSummary.timeDifference / (1000 * 60 * 60) : 0
      updatedSummary.dietMoney = dietMoney * hours
    }

    if (vehicle?.id) {
      const lastActivityWithCurrentVehicle = await this.activityManagerRepository
        .createQueryBuilder('activity')
        .where(`"vehicle"->>'id' = :vehicleId`, { vehicleId: vehicle.id })
        .andWhere('"inspectionDate" < :inspectionDate', { inspectionDate })
        .orderBy(`"inspectionEnd"->>'time'`, 'DESC')
        .getOne()

      if (!!lastActivityWithCurrentVehicle?.inspectionEnd.mileage && !!inspectionStart.mileage) {
        if (inspectionStart.mileage > lastActivityWithCurrentVehicle.inspectionEnd.mileage) {
          updatedSummary.privateRide = inspectionStart.mileage - lastActivityWithCurrentVehicle.inspectionEnd.mileage
        }
      }
    }

    activity.summary = updatedSummary
  }

  async validateActivityData(activity: ActivityManagerEntity) {
    const { inspectionStart, inspectionEnd, vehicle } = activity

    if (!inspectionStart.time) throw new BadRequestException('inspectionStart.time is null')
    if (!inspectionEnd.time) throw new BadRequestException('inspectionEnd.time is null')
    if (new Date(inspectionEnd.time).getTime() < new Date(inspectionStart.time).getTime()) {
      throw new BadRequestException('inspectionEnd.time should be more than inspectionStart.time')
    }

    if (!inspectionEnd.mileage) throw new BadRequestException('inspectionEnd.mileage is null')
    if (!inspectionStart.mileage) throw new BadRequestException('inspectionStart.mileage is null')
    if (inspectionEnd.mileage < inspectionStart.mileage) {
      throw new BadRequestException('inspectionStart.mileage should be more than inspectionStart.mileage')
    }

    if (vehicle?.id) await this.validateVehicleData(activity)
    await this.validateUserActivityWorkHours(activity)
  }

  async validateVehicleData(activity: ActivityManagerEntity) {
    const { inspectionDate, inspectionStart, inspectionEnd, vehicle } = activity
    if (!inspectionStart.time || !inspectionEnd.time || !inspectionStart.mileage || !inspectionEnd.mileage) return

    const isOtherActivity = ActivityTypes.OTHER === activity.activityType
    const isBreakActivity = [ActivityTypes.HOLIDAY, ActivityTypes.SICK, ActivityTypes.LUNCH].includes(
      activity.activityType
    )

    const inspectionDateParsed = DateTime.fromISO(String(inspectionDate), { setZone: true })
    const startOfPreviousDay = inspectionDateParsed.minus({ days: 1 }).startOf('day').toISO()
    const endOfCurrentDay = inspectionDateParsed.endOf('day').toISO()

    const activities = await this.activityManagerRepository
      .createQueryBuilder('activity')
      .where(`"inspectionDate" BETWEEN :startOfPreviousDay AND :endOfCurrentDay`, {
        startOfPreviousDay: startOfPreviousDay,
        endOfCurrentDay: endOfCurrentDay
      })
      .andWhere(`"vehicle"->>'id' = :vehicleId`, { vehicleId: vehicle.id })
      .orderBy(`"inspectionStart"->>'time'`, 'ASC')
      .getMany()

    let lastMileage = 0

    for (const act of activities) {
      if (activity.id && act.id === activity.id) continue
      const existingStart = act.inspectionStart.time ? new Date(act.inspectionStart.time).getTime() : null
      const existingEnd = act.inspectionEnd.time ? new Date(act.inspectionEnd.time).getTime() : null
      const newStart = new Date(inspectionStart.time).getTime()
      const newEnd = new Date(inspectionEnd.time).getTime()

      // the time of the current activity should not overlap with other activities
      if (existingStart && existingEnd && !isBreakActivity && !isOtherActivity) {
        if (
          (newStart >= existingStart && newStart < existingEnd) ||
          (newEnd > existingStart && newEnd <= existingEnd) ||
          (newStart <= existingStart && newEnd >= existingEnd) ||
          (existingStart >= newStart && existingEnd <= newEnd)
        ) {
          throw new BadRequestException('Time range overlaps with existing activity')
        }
      }

      // The current activity's mileage must not overlap with existing ones
      if (act.inspectionStart.mileage && act.inspectionEnd.mileage) {
        if (
          (inspectionStart.mileage > act.inspectionStart.mileage &&
            inspectionStart.mileage < act.inspectionEnd.mileage) ||
          (inspectionEnd.mileage > act.inspectionStart.mileage && inspectionEnd.mileage < act.inspectionEnd.mileage) ||
          (inspectionStart.mileage < act.inspectionStart.mileage &&
            inspectionEnd.mileage > act.inspectionEnd.mileage) ||
          (act.inspectionStart.mileage > inspectionStart.mileage && act.inspectionEnd.mileage < inspectionEnd.mileage)
        ) {
          throw new BadRequestException('Mileage range overlaps with existing activity')
        }

        // Additionally, we check that the mileage is strictly in ascending order
        if (!!lastMileage && inspectionStart.mileage < lastMileage && existingStart && newEnd >= existingStart) {
          throw new BadRequestException('Mileage must be greater than the previous activity')
        }

        lastMileage =
          inspectionEnd.mileage < act.inspectionEnd.mileage ? inspectionEnd.mileage : act.inspectionEnd.mileage
      }
    }
  }

  async validateUserActivityWorkHours(activity: ActivityManagerEntity) {
    const { inspectionStart, inspectionEnd, inspectionDate, customerNumber, username, summary } = activity
    const currentDurationInHours = (summary.timeDifference || 0) / (1000 * 60 * 60)

    if (!inspectionStart.time || !inspectionEnd.time) return

    const isBreakActivity = [ActivityTypes.HOLIDAY, ActivityTypes.SICK, ActivityTypes.LUNCH].includes(
      activity.activityType
    )

    const inspectionDateParsed = DateTime.fromISO(String(inspectionDate), { setZone: true })
    const startOfPreviousDay = inspectionDateParsed.minus({ days: 1 }).startOf('day').toISO()
    const endOfCurrentDay = inspectionDateParsed.endOf('day').toISO()

    const userActivities = await this.activityManagerRepository
      .createQueryBuilder('activity')
      .where(`"inspectionDate" BETWEEN :startOfPreviousDay AND :endOfCurrentDay`, {
        startOfPreviousDay,
        endOfCurrentDay
      })
      .andWhere(`"customerNumber" = :customerNumber`, { customerNumber })
      .andWhere(`"username" = :username`, { username })
      .orderBy(`"inspectionStart"->>'time'`, 'ASC')
      .getMany()

    const newStart = new Date(inspectionStart.time).getTime()
    const newEnd = new Date(inspectionEnd.time).getTime()

    for (const act of userActivities) {
      if (activity.id && act.id === activity.id) continue
      const existingStart = act.inspectionStart.time ? new Date(act.inspectionStart.time).getTime() : null
      const existingEnd = act.inspectionEnd.time ? new Date(act.inspectionEnd.time).getTime() : null

      if (existingStart && existingEnd) {
        if (
          (newStart >= existingStart && newStart < existingEnd) ||
          (newEnd > existingStart && newEnd <= existingEnd) ||
          (newStart <= existingStart && newEnd >= existingEnd)
        ) {
          throw new BadRequestException('User cannot have overlapping activities')
        }
      }
    }

    let totalWorkedHours = 0
    let timeBeforeLunch = 0
    let hasLunch = false
    const onlyCurrentDayActivities = userActivities.filter(
      ({ inspectionDate }) =>
        new Date(activity.inspectionDate).toDateString() === new Date(inspectionDate).toDateString()
    )
    for (const act of onlyCurrentDayActivities) {
      const existingStart = act.inspectionStart.time ? new Date(act.inspectionStart.time).getTime() : null
      const existingEnd = act.inspectionEnd.time ? new Date(act.inspectionEnd.time).getTime() : null

      if (existingStart && existingEnd) {
        const diffHours = (existingEnd - existingStart) / (1000 * 60 * 60)
        totalWorkedHours += diffHours
        if (!hasLunch) timeBeforeLunch += diffHours
        if (act.activityType === ActivityTypes.LUNCH) hasLunch = true
      }
    }

    if (totalWorkedHours + currentDurationInHours > 10) {
      throw new BadRequestException('Max working time per day is 10 hours')
    }

    if (!isBreakActivity && !hasLunch && timeBeforeLunch + currentDurationInHours > 6) {
      throw new BadRequestException('For working hours over 6 hours, a lunch break is required')
    }

    const durationMs = new Date(inspectionEnd.time).getTime() - new Date(inspectionStart.time).getTime()
    const durationHours = durationMs / (1000 * 60 * 60)
    if (!isBreakActivity && durationHours > 6) {
      throw new BadRequestException('Max activity duration is 6 hours')
    }
  }

  async getLastActivityByVehicleId(vehicleId: string) {
    const lastActivityWithCurrentVehicle = await this.activityManagerRepository
      .createQueryBuilder('activity')
      .where(`"vehicle"->>'id' = :vehicleId`, { vehicleId })
      .orderBy(`"inspectionEnd"->>'time'`, 'DESC')
      .getOne()

    if (!lastActivityWithCurrentVehicle) throw new NotFoundException(`Activity not found`)
    return lastActivityWithCurrentVehicle
  }
}
