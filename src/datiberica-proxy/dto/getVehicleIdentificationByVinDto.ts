import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsNotEmpty, IsObject, IsString, ValidateNested } from 'class-validator'
import { LocaleDto } from 'src/common/dto'

class DatIbericaOriginValueStringDto {
  @ApiProperty({ example: 'dat', type: String })
  origin: string

  @ApiProperty({ example: 'example value', type: String })
  value: string
}

class DatIbericaOriginValueNumberDto {
  @ApiProperty({ example: 'dat', type: String })
  origin: string

  @ApiProperty({ example: 12345, type: Number })
  value: number
}

class DatIbericaOriginalPriceInfoDto {
  @ApiProperty({ example: 14400, type: Number })
  OriginalPriceGross: number

  @ApiProperty({ example: 752, type: Number })
  RegistrationTax: number

  @ApiProperty({ example: 11373, type: Number })
  OriginalPriceNet: number

  @ApiProperty({ example: 20, type: Number })
  OriginalPriceVATRate: number
}

class DatIbericaPaintTypeDto {
  @ApiProperty({ example: '12', type: String })
  value: string
}

class DatIbericaPaintTypesDto {
  @ApiProperty({ type: [DatIbericaPaintTypeDto] })
  PaintType: DatIbericaPaintTypeDto[]
}

class DatIbericaVINEquipmentDto {
  @ApiProperty({ required: false, example: 'ShortName example', type: String })
  ShortName?: string

  @ApiProperty({ required: false, example: '1809', type: String })
  AvNumberDat?: string
}

class DatIbericaVINEquipmentsDto {
  @ApiProperty({ type: [DatIbericaVINEquipmentDto] })
  VINEquipment: DatIbericaVINEquipmentDto[]
}

class DatIbericaVINumberDto {
  @ApiProperty({ example: 'ZFA19900001486122', type: String })
  VinCode: string
}

class DatIbericaVINVehicleDto {
  @ApiProperty({ type: DatIbericaVINumberDto })
  VINumber: DatIbericaVINumberDto
}

export class DatIbericaVINContainerDto {
  @ApiProperty({ example: '002', type: String })
  Container: string

  @ApiProperty({ example: '280', type: String })
  ManufacturerKey: string

  @ApiProperty({ example: '2410', type: String })
  VehicleMainTypeKey: string

  @ApiProperty({ example: '12', type: String })
  VehicleSubTypeKey: string

  @ApiProperty({ example: '4711', type: String })
  VehicleConstructionTime: string

  @ApiProperty({ example: '5', type: String })
  VehicleTypeKey: string
}

class DatIbericaVINContainersDto {
  @ApiProperty({ type: [DatIbericaVINContainerDto] })
  VINContainer: DatIbericaVINContainerDto[]
}

class DatIbericaVINECodeDto {
  @ApiProperty({ example: '3', type: String })
  VehicleSubTypeVariantKey: string

  @ApiProperty({ example: '4700', type: String })
  ConstructionTimePriceList: string

  @ApiProperty({ example: '4710', type: String })
  ConstructionTimeMin: string

  @ApiProperty({ example: '4450', type: String })
  ConstructionTimeEdge: string

  @ApiProperty({ example: '54', type: String })
  VehicleSubTypeKey: string

  @ApiProperty({ example: '280', type: String })
  ManufacturerKey: string

  @ApiProperty({ example: '4711', type: String })
  ConstructionTime: string

  @ApiProperty({ example: '4711', type: String })
  ConstructionTimeProd: string

  @ApiProperty({ example: '74', type: String })
  VehicleMainTypeKey: string

  @ApiProperty({ example: 'IT', type: String })
  Country: string

  @ApiProperty({ type: DatIbericaVINContainersDto })
  VINContainers: DatIbericaVINContainersDto

  @ApiProperty({ example: '0', type: String })
  Sign: string

  @ApiProperty({ example: '1', type: String })
  VehicleTypeKey: string
}

class DatIbericaVINECodesDto {
  @ApiProperty({ type: [DatIbericaVINECodeDto] })
  VINECode: DatIbericaVINECodeDto[]
}

class DatIbericaVINColorDto {
  @ApiProperty({ required: false, example: 'COLORE INTERNO (BLU NAVY)', type: String })
  Description?: string

  @ApiProperty({ example: 'COLEST448', type: String })
  Code: string

  @ApiProperty({ example: 'A1', type: String })
  ColorID: string

  @ApiProperty({ required: false, example: '2 COAT', type: String })
  CountCoat?: string

  @ApiProperty({ required: false, example: '5', type: String })
  StandardColor?: string

  @ApiProperty({ required: false, example: '12', type: String })
  PaintType?: string
}

class DatIbericaVINColorsDto {
  @ApiProperty({ type: [DatIbericaVINColorDto] })
  VINColor: DatIbericaVINColorDto[]
}

class DatIbericaContainedEquipmentPositionDto {
  @ApiProperty({ example: 'Sistema assistenza guida: servofreno', type: String })
  Description: string

  @ApiProperty({ required: false, example: 'FA01', type: String })
  EquipmentGroup?: string

  @ApiProperty({ example: '40801', type: String })
  DatEquipmentId: string
}

class DatIbericaContainedEquipmentPositionsDto {
  @ApiProperty({ type: [DatIbericaContainedEquipmentPositionDto] })
  EquipmentPosition: DatIbericaContainedEquipmentPositionDto[]
}

class DatIbericaSpecialEquipmentPositionDto {
  @ApiProperty({ example: 'Electronic Stability Program (ESP / ESC)', type: String })
  Description: string

  @ApiProperty({ required: false, example: 420, type: Number })
  OriginalPrice?: number

  @ApiProperty({ required: false, type: DatIbericaContainedEquipmentPositionsDto })
  ContainedEquipmentPositions?: DatIbericaContainedEquipmentPositionsDto

  @ApiProperty({ required: false, example: 'ESP', type: String })
  EquipmentGroup?: string

  @ApiProperty({ required: false, example: '', type: String })
  ManufacturerEquipmentId?: string

  @ApiProperty({ example: '70308', type: String })
  DatEquipmentId: string
}

class DatIbericaSpecialEquipmentDto {
  @ApiProperty({ type: [DatIbericaSpecialEquipmentPositionDto] })
  EquipmentPosition: DatIbericaSpecialEquipmentPositionDto[]
}

export class DatIbericaSeriesEquipmentPositionDto {
  @ApiProperty({ example: 'airbag lat. ant.', type: String })
  Description: string

  @ApiProperty({ required: false, example: 'AIR3', type: String })
  EquipmentGroup?: string

  @ApiProperty({ required: false, example: '', type: String })
  ManufacturerEquipmentId?: string

  @ApiProperty({ example: '26903', type: String })
  DatEquipmentId: string

  @ApiProperty({ required: false, example: '11', type: String })
  EquipmentClass?: string
}

class DatIbericaSeriesEquipmentDto {
  @ApiProperty({ type: [DatIbericaSeriesEquipmentPositionDto] })
  EquipmentPosition: DatIbericaSeriesEquipmentPositionDto[]
}

class DatIbericaEquipmentDto {
  @ApiProperty({ type: DatIbericaSpecialEquipmentDto })
  SpecialEquipment: DatIbericaSpecialEquipmentDto

  @ApiProperty({ type: DatIbericaSeriesEquipmentDto })
  SeriesEquipment: DatIbericaSeriesEquipmentDto
}

class DatIbericaVINResultDto {
  @ApiProperty({ example: '2.5', type: String })
  VinInterfaceVersion: string

  @ApiProperty({ type: DatIbericaVINEquipmentsDto })
  VINEquipments: DatIbericaVINEquipmentsDto

  @ApiProperty({ example: '', type: String })
  VinEquipmentsEncrypted: string

  @ApiProperty({ type: DatIbericaVINVehicleDto })
  VINVehicle: DatIbericaVINVehicleDto

  @ApiProperty({ type: DatIbericaVINECodesDto })
  VINECodes: DatIbericaVINECodesDto

  @ApiProperty({ type: DatIbericaVINColorsDto })
  VINColors: DatIbericaVINColorsDto
}

class DatIbericaDATECodeEquipmentPositionDto {
  @ApiProperty({ example: '1', type: String })
  EquipmentClass: string

  @ApiProperty({ example: 'motore 1,4 Ltr. - 57 kW CAT', type: String })
  Description: string

  @ApiProperty({ example: '82502', type: String })
  DatEquipmentId: string
}

class DatIbericaDATECodeEquipmentDto {
  @ApiProperty({ type: [DatIbericaDATECodeEquipmentPositionDto] })
  EquipmentPosition: DatIbericaDATECodeEquipmentPositionDto[]
}

class DatIbericaVehicleDto {
  @ApiProperty({ type: DatIbericaOriginValueNumberDto })
  OriginalPrice: DatIbericaOriginValueNumberDto

  @ApiProperty({ type: DatIbericaOriginValueStringDto })
  SubModelName: DatIbericaOriginValueStringDto

  @ApiProperty({ type: DatIbericaOriginValueStringDto })
  BaseModelName: DatIbericaOriginValueStringDto

  @ApiProperty({ type: DatIbericaOriginValueStringDto })
  ContainerNameN: DatIbericaOriginValueStringDto

  @ApiProperty({ example: '54', type: String })
  SubModel: string

  @ApiProperty({ example: '012800740540003', type: String })
  DatECode: string

  @ApiProperty({ type: DatIbericaOriginValueNumberDto })
  OriginalPriceGross: DatIbericaOriginValueNumberDto

  @ApiProperty({ example: 'IT002', type: String })
  Container: string

  @ApiProperty({ example: 'Autovetture,SUV', type: String })
  VehicleTypeName: string

  @ApiProperty({ example: 'IT - Berl2v5 1.4 8V EU4, Dynamic, 2007 - 2009', type: String })
  ContainerName: string

  @ApiProperty({ example: '74', type: String })
  BaseModel: string

  @ApiProperty({ example: 'Grande Punto', type: String })
  MainTypeGroupName: string

  @ApiProperty({ example: '280', type: String })
  Manufacturer: string

  @ApiProperty({ example: '3', type: String })
  SubModelVariant: string

  @ApiProperty({ example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', type: String })
  TokenOfVinResult: string

  @ApiProperty({ type: DatIbericaOriginValueStringDto })
  VehicleTypeNameN: DatIbericaOriginValueStringDto

  @ApiProperty({ example: 'IDENTIFICATIONSOURCE_VIN', type: String })
  IdentificationSource: string

  @ApiProperty({ type: DatIbericaOriginValueStringDto })
  ManufacturerName: DatIbericaOriginValueStringDto

  @ApiProperty({ example: 'GPU', type: String })
  MainTypeGroup: string

  @ApiProperty({ example: '1', type: String })
  VehicleType: string

  @ApiProperty({ type: DatIbericaOriginalPriceInfoDto })
  OriginalPriceInfo: DatIbericaOriginalPriceInfoDto

  @ApiProperty({ example: 'Grande Punto 1.4 Dynamic 08', type: String })
  SalesDescription: string

  @ApiProperty({ type: DatIbericaPaintTypesDto })
  PaintTypes: DatIbericaPaintTypesDto

  @ApiProperty({ example: '0', type: String })
  VinAccuracy: string

  @ApiProperty({ example: '4711', type: String })
  ConstructionTime: string

  @ApiProperty({ example: '4450', type: String })
  ConstructionTimeFrom: string

  @ApiProperty({ example: '4809', type: String })
  ConstructionTimeTo: string

  @ApiProperty({ example: 'ALL', type: String })
  ReleaseIndicator: string

  @ApiProperty({ type: DatIbericaVINResultDto })
  VINResult: DatIbericaVINResultDto

  @ApiProperty({ type: DatIbericaEquipmentDto })
  Equipment: DatIbericaEquipmentDto

  @ApiProperty({ type: DatIbericaDATECodeEquipmentDto })
  DATECodeEquipment: DatIbericaDATECodeEquipmentDto

  // To be continued in next steps
}

class DatIbericaDossierDto {
  @ApiProperty({ type: DatIbericaVehicleDto })
  Vehicle: DatIbericaVehicleDto

  @ApiProperty({ example: 'it_IT', type: String })
  Language: string

  @ApiProperty({ example: 'IT', type: String })
  Country: string
}

export class GetVehicleIdentificationByVinResponseDto {
  @ApiProperty({ type: [DatIbericaDossierDto] })
  Dossier: DatIbericaDossierDto[]
}

class GetVehicleIdentificationDto implements DAT.Request.WithRestriction {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  vin: string

  @ApiProperty({ type: LocaleDto })
  @IsObject()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => LocaleDto)
  locale: LocaleDto

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  restriction: DAT.Restriction
}

export class GetVehicleIdentificationByVinRequestDto {
  @ApiProperty({ type: GetVehicleIdentificationDto })
  @IsObject()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => GetVehicleIdentificationDto)
  request: GetVehicleIdentificationDto
}
