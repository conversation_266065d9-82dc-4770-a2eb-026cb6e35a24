import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

class MarcaDto {
  @ApiProperty({ example: '6694.', description: 'Code', type: String })
  codigo: string

  @ApiProperty({ example: 'FORD', description: 'Description', type: String })
  descripcion: string
}

class PropulsionDto {
  @ApiProperty({ example: 0, description: 'Code', type: Number })
  codigo: number | string

  @ApiProperty({ example: 'Gasoline', description: 'Description', type: String })
  descripcion: string
}

class CombustibleEmisionesDto {
  @ApiProperty({ type: PropulsionDto, description: 'Primary propulsion information' })
  propulsion: PropulsionDto

  @ApiProperty({
    type: PropulsionDto,
    description: 'Secondary propulsion information',
    example: { codigo: '', descripcion: 'Gasoline' }
  })
  propulsion2: PropulsionDto
}

class DescripcionVehiculoDto {
  @ApiProperty({ example: 'WF0JXXGAHJNY03871', description: 'Chassis number (VIN)', type: String })
  bastidor: string

  @ApiProperty({ type: MarcaDto, description: 'Brand information' })
  marca: MarcaDto

  @ApiProperty({ example: 'FIESTA', description: 'Model', type: String })
  modelo: string

  @ApiProperty({ example: '', description: 'NIVE (Vehicle Identification Number Extension)', type: String })
  nive: string
}

class FechasControlDto {
  @ApiProperty({ example: '2022-06-13T00:00:00+02:00', description: 'Registration date', type: String })
  fechaMatriculacion: string

  @ApiProperty({ example: '2022-06-13T00:00:00+02:00', description: 'First registration date', type: String })
  fechaPrimeraMatriculacion: string
}

class MatriculacionDto {
  @ApiProperty({ example: '2022-06-13T00:00:00+02:00', description: 'Registration date', type: String })
  fechaMatriculacion: string

  @ApiProperty({ example: '5720LYM', description: 'License plate', type: String })
  matricula: string
}

class DatosGeneralesDto {
  @ApiProperty({ type: DescripcionVehiculoDto, description: 'Vehicle description' })
  descripcionVehiculo: DescripcionVehiculoDto

  @ApiProperty({ type: FechasControlDto, description: 'Control dates' })
  fechasControl: FechasControlDto

  @ApiProperty({ type: MatriculacionDto, description: 'Registration information' })
  matriculacion: MatriculacionDto
}

class DatosTecnicosDto {
  @ApiProperty({ type: CombustibleEmisionesDto, description: 'Fuel and emissions information' })
  combustibleEmisiones: CombustibleEmisionesDto
}

class DatosAdicionalesDto {
  @ApiProperty({ example: '2022-06-13', description: 'Registration date', type: String })
  FEC_MATRICULA: string

  @ApiProperty({ example: 0, description: 'Registration class code', type: Number })
  COD_CLASE_MAT: number

  @ApiProperty({ example: '', description: 'Processing date', type: String })
  FEC_TRAMITACION: string

  @ApiProperty({ example: 'FORD', description: 'ITV brand', type: String })
  MARCA_ITV: string

  @ApiProperty({ example: 'FIESTA', description: 'ITV model', type: String })
  MODELO_ITV: string

  @ApiProperty({ example: 3, description: 'ITV origin code', type: Number })
  COD_PROCEDENCIA_ITV: number

  @ApiProperty({ example: 'WF0JXXGAHJNY03871', description: 'ITV chassis number', type: String })
  BASTIDOR_ITV: string

  @ApiProperty({ example: 40, description: 'Type code', type: Number })
  COD_TIPO: number

  @ApiProperty({ example: 0, description: 'ITV propulsion code', type: Number })
  COD_PROPULSION_ITV: number

  @ApiProperty({ example: 999, description: 'ITV engine displacement', type: Number })
  CILINDRADA_ITV: number

  @ApiProperty({ example: 7.82, description: 'ITV power', type: Number })
  POTENCIA_ITV: number

  @ApiProperty({ example: 0, description: 'Tare weight', type: Number })
  TARA: number

  @ApiProperty({ example: 1690, description: 'Maximum weight', type: Number })
  PESO_MAX: number

  @ApiProperty({ example: 5, description: 'Number of seats', type: Number })
  NUM_PLAZAS: number

  @ApiProperty({ example: '', description: 'Sealed indicator', type: String })
  IND_PRECINTO: string

  @ApiProperty({ example: '', description: 'Embargo indicator', type: String })
  IND_EMBARGO: string

  @ApiProperty({ example: 0, description: 'Number of transfers', type: Number })
  NUM_TRANSMISIONES: number

  @ApiProperty({ example: 1, description: 'Number of owners', type: Number })
  NUM_TITULARES: number

  @ApiProperty({ example: 'MORALZARZAL', description: 'Vehicle locality', type: String })
  LOCALIDAD_VEHICULO: string

  @ApiProperty({ example: 'M', description: 'Vehicle province code', type: String })
  COD_PROVINCIA_VEH: string

  @ApiProperty({ example: 'M', description: 'Registration province code', type: String })
  COD_PROVINCIA_MAT: string

  @ApiProperty({ example: 1, description: 'Procedure key', type: Number })
  CLAVE_TRAMITE: number

  @ApiProperty({ example: '2022-06-13', description: 'Procedure date', type: String })
  FEC_TRAMITE: string

  @ApiProperty({ example: 28411, description: 'Postal code', type: Number })
  CODIGO_POSTAL: number

  @ApiProperty({ example: '', description: 'First registration date', type: String })
  FEC_PRIM_MATRICULACION: string

  @ApiProperty({ example: 'N', description: 'New/used indicator', type: String })
  IND_NUEVO_USADO: string

  @ApiProperty({ example: 'X', description: 'Physical/legal person', type: String })
  PERSONA_FISICA_JURIDICA: string

  @ApiProperty({ example: '', description: 'ITV code', type: String })
  CODIGO_ITV: string

  @ApiProperty({ example: 'B00', description: 'Service', type: String })
  SERVICIO: string

  @ApiProperty({ example: 28090, description: 'INE municipality code', type: Number })
  COD_MUNICIPIO_INE_VEH: number

  @ApiProperty({ example: 'MORALZARZAL', description: 'Municipality', type: String })
  MUNICIPIO: string

  @ApiProperty({ example: 91.9, description: 'ITV power in kW', type: Number })
  KW_ITV: number

  @ApiProperty({ example: 5, description: 'Maximum number of seats', type: Number })
  NUM_PLAZAS_MAX: number

  @ApiProperty({ example: 114, description: 'ITV CO2 emissions', type: Number })
  CO2_ITV: number

  @ApiProperty({ example: 'S', description: 'Renting', type: String })
  RENTING: string

  @ApiProperty({ example: '', description: 'Guardianship code', type: String })
  COD_TUTELA: string

  @ApiProperty({ example: '', description: 'Possession code', type: String })
  COD_POSESION: string

  @ApiProperty({ example: '', description: 'Definitive deregistration indicator', type: String })
  IND_BAJA_DEF: string

  @ApiProperty({ example: '', description: 'Temporary deregistration indicator', type: String })
  IND_BAJA_TEMP: string

  @ApiProperty({ example: '', description: 'Theft indicator', type: String })
  IND_SUSTRACCION: string

  @ApiProperty({ example: '', description: 'Telematic deregistration', type: String })
  BAJA_TELEMATICA: string

  @ApiProperty({ example: '', description: 'ITV type', type: String })
  TIPO_ITV: string

  @ApiProperty({ example: 'B7JB1JX', description: 'ITV variant', type: String })
  VARIANTE_ITV: string

  @ApiProperty({ example: '5CDPRNA5ADZ', description: 'ITV version', type: String })
  VERSION_ITV: string

  @ApiProperty({ example: 'FORD-WERKE GMBH', description: 'ITV manufacturer', type: String })
  FABRICANTE_ITV: string

  @ApiProperty({ example: 1246, description: 'ITV curb weight', type: Number })
  MASA_ORDEN_MARCHA_ITV: number

  @ApiProperty({ example: 1690, description: 'ITV maximum technical admissible mass', type: Number })
  MASA_MAXIMA_TECNICA_ADMISIBLE_ITV: number

  @ApiProperty({ example: 'M1', description: 'ITV European homologation category', type: String })
  CATEGORIA_HOMOLOGACION_EUROPEA_ITV: string

  @ApiProperty({ example: 'AB', description: 'Bodywork', type: String })
  CARROCERIA: string

  @ApiProperty({ example: 0, description: 'Standing places', type: Number })
  PLAZAS_PIE: number

  @ApiProperty({ example: 'EURO 6AP', description: 'ITV Euro emission level', type: String })
  NIVEL_EMISIONES_EURO_ITV: string

  @ApiProperty({ example: 0, description: 'ITV consumption in Wh/km', type: Number })
  CONSUMO_WH_KM_ITV: number

  @ApiProperty({ example: 1000, description: 'ITV vehicle regulation classification', type: Number })
  CLASIFICACION_REGLAMENTO_VEHICULOS_ITV: number

  @ApiProperty({ example: 'HEV', description: 'Electric vehicle category', type: String })
  CATEGORIA_VEHICULO_ELECTRICO: string

  @ApiProperty({ example: '', description: 'Electric vehicle range', type: String })
  AUTONOMIA_VEHICULO_ELECTRICO: string

  @ApiProperty({ example: '', description: 'Base vehicle brand', type: String })
  MARCA_VEHICULO_BASE: string

  @ApiProperty({ example: '', description: 'Base vehicle manufacturer', type: String })
  FABRICANTE_VEHICULO_BASE: string

  @ApiProperty({ example: '', description: 'Base vehicle type', type: String })
  TIPO_VEHICULO_BASE: string

  @ApiProperty({ example: '', description: 'Base vehicle variant', type: String })
  VARIANTE_VEHICULO_BASE: string

  @ApiProperty({ example: '', description: 'Base vehicle version', type: String })
  VERSION_VEHICULO_BASE: string

  @ApiProperty({ example: 2493, description: 'ITV axle distance 1-2', type: Number })
  DISTANCIA_EJES_12_ITV: number

  @ApiProperty({ example: 1503, description: 'ITV front track', type: Number })
  VIA_ANTERIOR_ITV: number

  @ApiProperty({ example: 1466, description: 'ITV rear track', type: Number })
  VIA_POSTERIOR_ITV: number

  @ApiProperty({ example: 'M', description: 'ITV feeding type', type: String })
  TIPO_ALIMENTACION_ITV: string

  @ApiProperty({ example: '', description: 'ITV homologation password', type: String })
  CONTRASENA_HOMOLOGACION_ITV: string

  @ApiProperty({ example: 'Y', description: 'ITV eco innovation', type: String })
  ECO_INNOVACION_ITV: string

  @ApiProperty({ example: '', description: 'ITV eco reduction', type: String })
  REDUCCION_ECO_ITV: string

  @ApiProperty({ example: 'E9 32 37', description: 'ITV eco code', type: String })
  CODIGO_ECO_ITV: string

  @ApiProperty({ example: '2022-06-13', description: 'Process date', type: String })
  FEC_PROCESO: string

  @ApiProperty({ example: 'N', description: 'RACE type', type: String })
  TIPO_RACE: string

  @ApiProperty({ example: 'N', description: 'OPTOVEN type', type: String })
  TIPO_OPTOVEN: string
}

class MicrodatosDto {
  @ApiProperty({ example: '2022-06-13', description: 'Registration date', type: String })
  FEC_MATRICULA: string

  @ApiProperty({ example: 0, description: 'Registration class code', type: Number })
  COD_CLASE_MAT: number

  @ApiProperty({ example: '', description: 'Processing date', type: String })
  FEC_TRAMITACION: string

  @ApiProperty({ example: 'FORD', description: 'ITV brand', type: String })
  MARCA_ITV: string

  @ApiProperty({ example: 'FIESTA', description: 'ITV model', type: String })
  MODELO_ITV: string

  @ApiProperty({ example: 3, description: 'ITV origin code', type: Number })
  COD_PROCEDENCIA_ITV: number

  @ApiProperty({ example: 'WF0JXXGAHJNY03871', description: 'ITV chassis number', type: String })
  BASTIDOR_ITV: string

  @ApiProperty({ example: 40, description: 'Type code', type: Number })
  COD_TIPO: number

  @ApiProperty({ example: 0, description: 'ITV propulsion code', type: Number })
  COD_PROPULSION_ITV: number

  @ApiProperty({ example: 999, description: 'ITV engine displacement', type: Number })
  CILINDRADA_ITV: number

  @ApiProperty({ example: 7.82, description: 'ITV power', type: Number })
  POTENCIA_ITV: number

  @ApiProperty({ example: 0, description: 'Tare weight', type: Number })
  TARA: number

  @ApiProperty({ example: 1690, description: 'Maximum weight', type: Number })
  PESO_MAX: number

  @ApiProperty({ example: 5, description: 'Number of seats', type: Number })
  NUM_PLAZAS: number

  @ApiProperty({ example: '', description: 'Sealed indicator', type: String })
  IND_PRECINTO: string

  @ApiProperty({ example: '', description: 'Embargo indicator', type: String })
  IND_EMBARGO: string

  @ApiProperty({ example: 0, description: 'Number of transfers', type: Number })
  NUM_TRANSMISIONES: number

  @ApiProperty({ example: 1, description: 'Number of owners', type: Number })
  NUM_TITULARES: number

  @ApiProperty({ example: 'MORALZARZAL', description: 'Vehicle locality', type: String })
  LOCALIDAD_VEHICULO: string

  @ApiProperty({ example: 'M', description: 'Vehicle province code', type: String })
  COD_PROVINCIA_VEH: string

  @ApiProperty({ example: 'M', description: 'Registration province code', type: String })
  COD_PROVINCIA_MAT: string

  @ApiProperty({ example: 1, description: 'Procedure key', type: Number })
  CLAVE_TRAMITE: number

  @ApiProperty({ example: '2022-06-13', description: 'Procedure date', type: String })
  FEC_TRAMITE: string

  @ApiProperty({ example: 28411, description: 'Postal code', type: Number })
  CODIGO_POSTAL: number

  @ApiProperty({ example: '', description: 'First registration date', type: String })
  FEC_PRIM_MATRICULACION: string

  @ApiProperty({ example: 'N', description: 'New/used indicator', type: String })
  IND_NUEVO_USADO: string

  @ApiProperty({ example: 'X', description: 'Physical/legal person', type: String })
  PERSONA_FISICA_JURIDICA: string

  @ApiProperty({ example: '', description: 'ITV code', type: String })
  CODIGO_ITV: string

  @ApiProperty({ example: 'B00', description: 'Service', type: String })
  SERVICIO: string

  @ApiProperty({ example: 28090, description: 'INE municipality code', type: Number })
  COD_MUNICIPIO_INE_VEH: number

  @ApiProperty({ example: 'MORALZARZAL', description: 'Municipality', type: String })
  MUNICIPIO: string

  @ApiProperty({ example: 91.9, description: 'ITV power in kW', type: Number })
  KW_ITV: number

  @ApiProperty({ example: 5, description: 'Maximum number of seats', type: Number })
  NUM_PLAZAS_MAX: number

  @ApiProperty({ example: 114, description: 'ITV CO2 emissions', type: Number })
  CO2_ITV: number

  @ApiProperty({ example: 'S', description: 'Renting', type: String })
  RENTING: string

  @ApiProperty({ example: '', description: 'Guardianship code', type: String })
  COD_TUTELA: string

  @ApiProperty({ example: '', description: 'Possession code', type: String })
  COD_POSESION: string

  @ApiProperty({ example: '', description: 'Definitive deregistration indicator', type: String })
  IND_BAJA_DEF: string

  @ApiProperty({ example: '', description: 'Temporary deregistration indicator', type: String })
  IND_BAJA_TEMP: string

  @ApiProperty({ example: '', description: 'Theft indicator', type: String })
  IND_SUSTRACCION: string

  @ApiProperty({ example: '', description: 'Telematic deregistration', type: String })
  BAJA_TELEMATICA: string

  @ApiProperty({ example: '', description: 'ITV type', type: String })
  TIPO_ITV: string

  @ApiProperty({ example: 'B7JB1JX', description: 'ITV variant', type: String })
  VARIANTE_ITV: string

  @ApiProperty({ example: '5CDPRNA5ADZ', description: 'ITV version', type: String })
  VERSION_ITV: string

  @ApiProperty({ example: 'FORD-WERKE GMBH', description: 'ITV manufacturer', type: String })
  FABRICANTE_ITV: string

  @ApiProperty({ example: 1246, description: 'ITV curb weight', type: Number })
  MASA_ORDEN_MARCHA_ITV: number

  @ApiProperty({ example: 1690, description: 'ITV maximum technical admissible weight', type: Number })
  MASA_MÁXIMA_TECNICA_ADMISIBLE_ITV: number

  @ApiProperty({ example: 'M1', description: 'ITV European homologation category', type: String })
  CATEGORÍA_HOMOLOGACIÓN_EUROPEA_ITV: string

  @ApiProperty({ example: 'AB', description: 'Bodywork', type: String })
  CARROCERIA: string

  @ApiProperty({ example: 0, description: 'Standing places', type: Number })
  PLAZAS_PIE: number

  @ApiProperty({ example: 'EURO 6AP', description: 'ITV Euro emission level', type: String })
  NIVEL_EMISIONES_EURO_ITV: string

  @ApiProperty({ example: 0, description: 'ITV consumption in Wh/km', type: Number })
  CONSUMO_WH_KM_ITV: number

  @ApiProperty({ example: 1000, description: 'ITV vehicle regulation classification', type: Number })
  CLASIFICACIÓN_REGLAMENTO_VEHICULOS_ITV: number

  @ApiProperty({ example: 'HEV', description: 'ITV electric vehicle category', type: String })
  CATEGORÍA_VEHÍCULO_ELÉCTRICO: string

  @ApiProperty({ example: '', description: 'ITV electric vehicle autonomy', type: String })
  AUTONOMÍA_VEHÍCULO_ELÉCTRICO: string

  @ApiProperty({ example: '', description: 'ITV vehicle base brand', type: String })
  MARCA_VEHÍCULO_BASE: string

  @ApiProperty({ example: '', description: 'ITV vehicle base manufacturer', type: String })
  FABRICANTE_VEHÍCULO_BASE: string

  @ApiProperty({ example: '', description: 'ITV vehicle base type', type: String })
  TIPO_VEHÍCULO_BASE: string

  @ApiProperty({ example: '', description: 'ITV vehicle base variant', type: String })
  VARIANTE_VEHÍCULO_BASE: string

  @ApiProperty({ example: '', description: 'ITV vehicle base version', type: String })
  VERSIÓN_VEHÍCULO_BASE: string

  @ApiProperty({ example: 2493, description: 'ITV axle distance 1-2', type: Number })
  DISTANCIA_EJES_12_ITV: number

  @ApiProperty({ example: 1503, description: 'ITV front track', type: Number })
  VIA_ANTERIOR_ITV: number

  @ApiProperty({ example: 1466, description: 'ITV rear track', type: Number })
  VIA_POSTERIOR_ITV: number

  @ApiProperty({ example: 'M', description: 'ITV feeding type', type: String })
  TIPO_ALIMENTACION_ITV: string

  @ApiProperty({ example: '', description: 'ITV homologation password', type: String })
  CONTRASEÑA_HOMOLOGACION_ITV: string

  @ApiProperty({ example: 'Y', description: 'ITV eco innovation', type: String })
  ECO_INNOVACION_ITV: string

  @ApiProperty({ example: '', description: 'ITV eco reduction', type: String })
  REDUCCION_ECO_ITV: string

  @ApiProperty({ example: 'E9 32 37', description: 'ITV eco code', type: String })
  CODIGO_ECO_ITV: string

  @ApiProperty({ example: '2022-06-13', description: 'ITV process date', type: String })
  FEC_PROCESO: string

  @ApiProperty({ example: 'TURISMO', description: 'ITV type', type: String })
  TIPO: string

  @ApiProperty({ example: 'Gasolina', description: 'ITV propulsion', type: String })
  PROPULSION_ITV: string
}

class Vehiculo {
  @ApiProperty({ description: 'General vehicle information', type: DatosGeneralesDto })
  datosGenerales: DatosGeneralesDto

  @ApiProperty({ description: 'Technical vehicle information', type: DatosTecnicosDto })
  datosTecnicos: DatosTecnicosDto

  @ApiProperty({ description: 'Additional vehicle information', type: DatosAdicionalesDto })
  datosAdicionales: DatosAdicionalesDto
}

class DGT {
  @ApiProperty({ description: 'Vehicle information', type: Vehiculo })
  vehiculo: Vehiculo

  @ApiProperty({ description: 'Microdata', type: MicrodatosDto })
  microdatos: MicrodatosDto
}

class DatibericaVXS {
  @ApiProperty({ description: 'DGT', type: DGT })
  DGT: DGT
}

class GetVehicleIdentificationByMatReturnDto {
  @ApiProperty({ example: 'xsd:string', description: 'Type', type: String })
  type: string

  @ApiProperty({ type: DatibericaVXS, description: 'VXS' })
  VXS: DatibericaVXS
}

export class GetVehicleIdentificationByPlateResponseDto {
  @ApiProperty({
    description: 'Get vehicle identification by plate return',
    type: GetVehicleIdentificationByMatReturnDto
  })
  getVehicleIdentificationByMatReturn: GetVehicleIdentificationByMatReturnDto
}

export class GetVehicleIdentificationByPlateRequestDto {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  plateNumber: string
}
