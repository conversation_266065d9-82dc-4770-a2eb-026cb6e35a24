import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ScheduleModule } from '@nestjs/schedule'

// Modules
import { SoapModule } from './shared/soap/soap.module'
import { DatModule } from './shared/dat/dat.module'
import { MyClaimModule } from './my-claim/my-claim.module'
import { AdminAccountModule } from './admin-account/admin-account.module'
import { UserModule } from './user/user.module'
import { AddressBookModule } from './address-book/address-book.module'
import { TranslationsModule } from './translations/translations.module'
import { ChatModule } from './chat/chat.module'
import { CustomerModule } from './customer/customer.module'
import { ContractModule } from './contract/contract.module'
import { MyClaimProxyModule } from './myClaim-proxy/myClaim-proxy.module'
import { NotificationsModule } from './notifications/notifications.module'
import { ConfigModule } from './config/config.module'
import { JwtTokenModule } from './token-module/token.module'
import { AuthModule } from './auth/auth.module'
import { PartnersModule } from './partners/partners.module'
import { EfficiencyModule } from './efficiency/efficiency.module'
import { ContractValuationModule } from './contract-valuation/contract-valuation.module'
import { FuelModule } from './fuel/fuel.module'
import { UserConfigModule } from './user-config/user-config.module'
import { AspisModule } from './aspis/aspis.module'
import { SpoModule } from './spo/spo.module'
import { SpoSpainModule } from './spo-spain/spo-spain.module'
import { HistoryModule } from './history/history.module'
import { MaintenanceModule } from './maintenance/maintenance.module'
import { VehicleDataModule } from './vechicle-data/vehicle-data.module'
import { AttachmentModule } from './attachment/attachment.module'
import { ReleaseModule } from './release/release.module'
import { UserGuideModule } from 'src/user-guide/user-guide.module'

// Services
import { ConfigService } from './config/config.service'
import { PrometheusModule } from './prometheus/prometheus.module'
import { MetricsModule } from './metrics/metrics.module'
import { DeeplinkModule } from './deeplink/deeplink.module'
import typeormDataSource from './config/migration.config'
import { InfosModule } from './infos/infos.module'
import { PlateSearchItalyModule } from './plate-search-italy/plateSearchItaly.module'
import { PostaProntaModule } from './posta-pronta/posta-pronta.module'
import { APIParserInitializerModule } from './api-parser-initializer/api-parser-initializer.module'
import { ReportModule } from './report/report.module'
import { AiGalleryModule } from './ai-gallery/ai-gallery.module'
import { SlackNotificationModule } from './slack-notification/slack-notification.module'
import { PartCompletionModule } from './part-completion/part-completion.module'
import { EurosvSvdImportModule } from './eurosv-svd-import/eurosv-svd-import.module'
import { GoogleMapsModule } from 'src/google-maps/google-maps.module'
import { ProxyModule } from 'src/proxy/proxy.module'
import { AiModule } from 'src/ai/ai.module'
import { ProfilesModule } from 'src/profiles/profiles.module'
import { ImageAnalysisModule } from './image-analysis/image-analysis.module'
import { ActivityManagerModule } from './activity-manager/activity-manager.module'
import { InspectionHistoryModule } from './inspection-history/inspection-history.module'
import { PartslinkModule } from './partslink/partslink.module'
import { PrintReportModule } from 'src/print-report/print-report.module'
import { HealthCheckModule } from 'src/health-check/health-check.module'
import { ProductivityModule } from 'src/productivity/productivity.module'
import { DatibericaProxyModule } from './datiberica-proxy/datiberica-proxy.module'
import { PreviewModule } from 'src/preview/preview.module'
import { FluxeaModule } from 'src/fluxea/fluxea.module'
import { DatgroupProxyModule } from './datgroup-proxy/datgroup-proxy.module'

@Module({
  imports: [
    AdminAccountModule,
    AddressBookModule,
    MaintenanceModule,
    AuthModule,
    ChatModule,
    AttachmentModule,
    CustomerModule,
    ContractModule,
    DatModule,
    JwtTokenModule,
    MyClaimModule,
    SoapModule,
    UserModule,
    EfficiencyModule,
    MyClaimProxyModule,
    UserConfigModule,
    NotificationsModule,
    TranslationsModule,
    ContractValuationModule,
    FuelModule,
    InfosModule,
    AspisModule,
    PlateSearchItalyModule,
    SpoModule,
    SpoSpainModule,
    VehicleDataModule,
    ReleaseModule,
    HistoryModule,
    PartnersModule,
    ScheduleModule.forRoot(),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => config.getTypeOrmConfig(),
      dataSourceFactory: () => typeormDataSource.initialize(),
      inject: [ConfigService]
    }),
    PrometheusModule,
    MetricsModule,
    DeeplinkModule,
    PostaProntaModule,
    ImageAnalysisModule,
    APIParserInitializerModule,
    ReportModule,
    AiGalleryModule,
    SlackNotificationModule,
    PartCompletionModule,
    GoogleMapsModule,
    ProxyModule,
    ProfilesModule,
    AiModule,
    PrintReportModule,
    InspectionHistoryModule,
    ActivityManagerModule,
    EurosvSvdImportModule,
    UserGuideModule,
    PartslinkModule,
    PreviewModule,
    HealthCheckModule,
    ProductivityModule,
    DatibericaProxyModule,
    FluxeaModule,
    DatgroupProxyModule
  ]
})
export class AppModule {}
