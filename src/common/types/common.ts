import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON>ber, IsOptional } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Transform } from 'class-transformer'

export type PlainObject = Record<string | number, any>

export interface HttpHeaders {
  [key: string]: string
}

export class ErrorResponse {
  @ApiProperty()
  message: string

  @ApiProperty()
  statusCode: number

  @ApiProperty({ required: false })
  error?: string
}

export class SuccessMessageResponse {
  @ApiProperty()
  message: string
}

export class PaginationData {
  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @Transform(({ value }) => Number(value))
  @IsNumber()
  readonly limit?: number

  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @Transform(({ value }) => Number(value))
  @IsNumber()
  readonly page?: number
}

export class BasePaginationResponse {
  @ApiProperty({ type: Number })
  total: number

  @ApiProperty({ type: Number })
  totalPages: number

  @ApiProperty({ type: Number })
  currentPage: number
}

export class BaseModel {
  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  id: number

  @ApiProperty({ type: Date })
  @IsDate()
  createdAt: Date

  @ApiProperty({ type: Date })
  @IsDate()
  updatedAt: Date
}
