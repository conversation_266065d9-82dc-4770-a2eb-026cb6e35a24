import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery
} from '@nestjs/swagger'

import { BAD_REQUEST, FORBIDDEN, SERVER_ERROR, SUCCESS_MESSAGE } from '../constants/messages'
import { HeadersNames } from 'src/common/constants/headers-names'
import { ErrorResponse } from '../types/common'

type SwaggerDocOptions = {
  isAuth: boolean // set endpoint auth, default true
  summary: string // short endpoint description
  typeResponse?: any // type endpoint response
  bodySchema?: any // endpoint body request
  querySchema?: any // endpoint querySchema
}

export function SwaggerDoc(options: SwaggerDocOptions): MethodDecorator {
  return function (target: any, key: string, descriptor: PropertyDescriptor) {
    if (options.isAuth) {
      ApiBearerAuth(HeadersNames.DatAuthToken)(target, key, descriptor)
    }

    ApiInternalServerErrorResponse({ type: ErrorResponse, description: SERVER_ERROR })(target, key, descriptor)
    ApiOkResponse({ type: options.typeResponse, description: SUCCESS_MESSAGE })(target, key, descriptor)
    ApiBadRequestResponse({ type: ErrorResponse, description: BAD_REQUEST })(target, key, descriptor)
    ApiForbiddenResponse({ type: ErrorResponse, description: FORBIDDEN })(target, key, descriptor)
    ApiOperation({ summary: options.summary })(target, key, descriptor)
    ApiQuery({ schema: options.querySchema })(target, key, descriptor)
    ApiBody({ schema: options.bodySchema })
  }
}
