export const CONTRACT_ENTRIES_KEYS = {
  MEMO: {
    labourRates: 'jsonLabourRates',
    vinResult: 'VINResult',
    AIResult: 'AIJsonResponse',
    chat: 'chat',
    assignedPartners: 'assignedPartners',
    orderData: 'orderData', // used for calculation order parts system
    advancedParts: 'packagesPositions',
    paintLabour: 'paintLabour', // used for French specific paint price
    summary: 'jsonTotalCalculation_FR', // used for French specific summary in calculation
    valuationResult: 'valuationResult',
    basicValuation: 'basicValuation',
    inquiryResult: 'inquiryResult',
    totalSalesPrice: 'totalSalesPrice',
    totalPurchasePrice: 'totalPurchasePrice',
    appraisalEquipments: 'appraisalEquipments',
    favPhoto: 'favPhoto',
    mileageOdometer: 'mileageOdometer',
    vehicleFirstRegistration: 'vehicleFirstRegistration',
    vehicleDescription: 'vehicleDescription',
    vehicleEngine: 'vehicleEngine',
    vehicleBody: 'vehicleBody',
    vehicleTransmission: 'vehicleTransmission',
    vehicleColorType: 'vehicleColorType',
    vehicleColorDescription: 'vehicleColorDescription',
    vehicleColorCode: 'vehicleColorCode',
    vehicleHP: 'vehicleHP',
    vehicleKW: 'vehicleKW',
    emailLogs: 'emailLogs',
    dateVinQueriedOn: 'dateVinQueriedOn',
    strVinQueriedOn: 'strVinQueriedOn',
    originalPartListASPIS: 'originalPartListASPIS',
    AspisAssessmentNumber: 'AspisAssessmentNumber',
    lastRevisionDate: 'revision2',
    revision4: 'revision4',
    modelPermissionNumber: 'approvalCode',
    paintKind: 'paintKind',
    memoAscale2: 'memoAscale2',
    memoAscale: 'memoAscale',
    memoAchanged: 'memoAchanged',
    subCalculationDraftWD: 'subCalculationDraftWD',
    bodyWage: 'bodyWage',
    mechanicWage: 'mechanicWage',
    electricWage: 'electricWage',
    contractNumber: 'contractNumber',
    auditionDate: 'auditionDate',
    orderDate: 'orderDate',
    contractNote: 'contractNote',
    creationDate: 'creationDate',
    createdByName: 'createdByName',
    createdByAlias: 'createdByAlias',
    euroSVCase: 'euroSVCase',
    firstEstimate: 'firstEstimate',
    nationalCodeAustria: 'nationalCodeAustria',
    vehicleLastRegistration: 'vehicleLastRegistration',
    damageCause: 'damageCause',
    damageLocation: 'damageLocation',
    damageDate: 'damageDate',
    poi: 'poi',
    advancedPoi: 'advancedPoi',
    manfredTires: 'manfredTires',
    customDepreciation: 'manfredCustomDepreciation',
    depreciationCalculationMethod: 'manfredDepreciationCalculationMethod',
    maxCarAge: 'manfredMaxCarAge',
    manfredImpairment: 'manfredImpairment',
    depreciationRound: 'manfredDepreciationRound',
    manualValuationEquipment: 'manualValuationEquipment',
    manualValuationListPrice: 'manualValuationListPrice',
    impairmentNote: 'manfredImpairmentNote',
    carAge: 'manfredCarAge',
    impairmentOriginalPriceGross: 'manfredImpairmentOriginalPriceGross',
    impairmentRepairCosts: 'manfredImpairmentRepairCosts',
    valuation: 'valuationResult',
    impairmentDealerPurchaseValue: 'manfredImpairmentDealerPurchaseValue',
    imagesInfo: 'imagesInfo',
    vehicleStructure: 'vehicleStructure',
    VehIdentNumber: 'VehIdentNumber',
    licenseNumber: 'LicenseNumber',
    vehicleSitNumber: 'vehicleSitNumber',
    vehicleDoorNumber: 'vehicleDoorNumber',
    vehicleCC: 'vehicleCC',
    vehicleFuelType: 'vehicleFuelType',
    manfredTypeOfCover: 'manfredTypeOfCover',
    vehicleKBA: 'vehicleKBA',
    viperProIframe: 'viperProIframe',
    historyVehicleFlag: 'historyVehicleFlag',
    kindOfMileage: 'kindOfMileage',
    deeplink: 'deepLink',
    vehicleClass: 'vehicleClass',
    autoVehicleClassFlag: 'autoVehicleClassFlag',
    smartRepairResult: 'SmartRepairResult',
    claimNumber: 'claimNumber',
    selectedAttachmentsInArchiveDrawer: 'selectedAttachments',
    impactArea: 'impactArea',
    vehicleTypeName: 'vehicleTypeName',
    apsResult: 'APSResult',
    valuationKindActive: 'valuationKindActive',
    manualValuationRV: 'manualValuationRV',
    orchestrator: 'orchestrator',
    subClaim: 'subClaim',
    maintenance: 'maintenance',
    postaProntaMessageBody: 'postaProntaMessageBody',
    vehicleWheelbase: 'vehicleWheelbase',
    vehicleTraction: 'vehicleTraction',
    manualLacquerParts: 'manualLacquerParts',
    lacquerState: 'lacquerState',
    vehicleSeats: 'vehicleSeats',
    unloadedWeight: 'unloadedWeight',
    permissableTotalWeight: 'permissableTotalWeight',
    txtLossNumber: 'txtLossNumber',
    txtInsuranceNumber: 'txtInsuranceNumber',
    alternativeDATECode: 'alternativeDATECode',
    historyComment: 'historyComment',
    damageMapping: 'damageMapping',
    profileInfo: 'profileInfo',
    calculatedByWeDat: 'calculatedByWeDat',

    owner: {
      name: 'owner_name',
      surname: 'owner_surname',
      mobilePhone: 'owner_mobilePhone',
      city: 'owner_city',
      address: 'owner_address',
      streetNumber: 'owner_streetNumber',
      province: 'owner_province',
      zip: 'owner_zip',
      gender: 'owner_gender',
      bornCity: 'owner_bornCity',
      bornProvince: 'owner_bornProvince',
      bornDate: 'owner_bornDate',
      idVatNumber: 'owner_idVatNumber',
      phone: 'owner_phone',
      vatNumber: 'owner_vatNumber',
      policyNumber: 'owner_policyNumber',
      type: 'owner_type',
      recipientCode: 'owner_recipientCode',
      taxRegime: 'owner_taxRegime',
      mail: 'owner_mail',
      pec: 'owner_pec',
      swift: 'owner_swift',
      notes: 'owner_notes',
      bank: 'owner_bank',
      iban: 'owner_iban',
      fax: 'owner_fax',
      name_bk: 'owner_name_bk',
      surname_bk: 'owner_surname_bk'
    },

    insured: {
      name: 'insured_name',
      surname: 'insured_surname',
      mobilePhone: 'insured_mobilePhone',
      city: 'insured_city',
      address: 'insured_address',
      streetNumber: 'insured_streetNumber',
      province: 'insured_province',
      zip: 'insured_zip',
      gender: 'insured_gender',
      bornCity: 'insured_bornCity',
      bornProvince: 'insured_bornProvince',
      bornDate: 'insured_bornDate',
      idVatNumber: 'insured_idVatNumber',
      phone: 'insured_phone',
      vatNumber: 'insured_vatNumber',
      policyNumber: 'insured_policyNumber',
      type: 'insured_type',
      recipientCode: 'insured_recipientCode',
      taxRegime: 'insured_taxRegime',
      mail: 'insured_mail',
      pec: 'insured_pec',
      swift: 'insured_swift',
      notes: 'insured_notes',
      bank: 'insured_bank',
      iban: 'insured_iban',
      fax: 'insured_fax',
      name_bk: 'insured_name_bk',
      surname_bk: 'insured_surname_bk'
    },

    insurance: {
      id: 'insurance_id',
      name: 'insurance_name',
      surname: 'insurance_surname',
      mobilePhone: 'insurance_mobilePhone',
      code: 'insurance_code',
      city: 'insurance_city',
      address: 'insurance_address',
      streetNumber: 'insurance_streetNumber',
      province: 'insurance_province',
      zip: 'insurance_zip',
      gender: 'insurance_gender',
      bornCity: 'insurance_bornCity',
      bornProvince: 'insurance_bornProvince',
      bornDate: 'insurance_bornDate',
      idVatNumber: 'insurance_idVatNumber',
      phone: 'insurance_phone',
      vatNumber: 'insurance_vatNumber',
      policyNumber: 'insurance_policyNumber',
      type: 'insurance_type',
      recipientCode: 'insurance_recipientCode',
      taxRegime: 'insurance_taxRegime',
      mail: 'insurance_mail',
      pec: 'insurance_pec',
      swift: 'insurance_swift',
      notes: 'insurance_notes',
      bank: 'insurance_bank',
      iban: 'insurance_iban',
      fax: 'insurance_fax',
      name_bk: 'insurance_name_bk',
      surname_bk: 'insurance_surname_bk'
    },

    repairer: {
      name: 'repairer_name',
      street: 'repairer_street',
      address: 'repairer_address',
      zip: 'repairer_zip',
      city: 'repairer_city',
      phone: 'repairer_phone',
      code: 'repairer_code'
    },

    contactPerson: {
      name: 'contactPerson_name',
      phone: 'contactPerson_phone',
      mail: 'contactPerson_mail'
    },

    OCR: {
      vehicleModelOCR: 'vehicleModelOCR',
      vehicleColorOCR: 'vehicleColorOCR',
      vehicleMakerOCR: 'vehicleMakerOCR',
      predictedPlateOCR: 'predictedPlateOCR',
      OCRPlateScanDate: 'OCRPlateScanDate',
      OCRVINScanDate: 'OCRVINScanDate'
    },

    ADMIN: {
      profiles: 'profiles',
      incrementalNumber: 'incrementalNumber',
      patternIncrementalNumber: 'patternIncrementalNumber',
      notifications: 'notifications',
      appointmentDates: 'appointmentDates',
      expiryClaimsDate: 'expiryClaimsDate'
    }
  }
}
