declare namespace DAT {
  import type { PartialDeep } from 'type-fest'
  namespace Request {
    type GenerateToken = Credentials
  }

  namespace Response {
    type GenerateToken = string
  }

  interface ParsedToken {
    clnt: string
    user: string
    iat: number
    org:
      | 'FR' // France
      | 'IT' // Italy
      | 'CH' // Switzerland
      | 'ES' // Spain
      | 'NL' // Netherlands
      | 'CZ' // Czech Republic
      | 'CN' // China
      | 'KR' // South Korea
      | 'TR' // Turkey
      | 'GR' // Greece
      | string
  }

  interface Credentials {
    customerNumber: string
    user: string
    password: string
    interfacePartnerNumber?: string
    interfacePartnerSignature?: string
  }

  interface WorkingHour {
    dayOfWeek: string
    startTime?: string
    endTime?: string
    timeZone: string
    weekend: boolean
  }

  interface UserProfile {
    name: string
    surname: string
    telephone: string
    email: string
    image?: string
    workingHours: WorkingHour[]
  }

  type TemplateId = number

  type CustomerRole = 'REPAIRER' | 'MANUFACTURER' | 'EXPERT' | 'INSURANCE'

  interface CustomerSettings {
    role?: CustomerRole
    bffUrl?: string
    apiUrl?: string
    aniaUrl?: string
    prUrl?: string
    disableCache?: boolean
    contractAPIShownFields?: string[]
    isMFAEnabled?: boolean
    MFARedirectUrl?: string
  }

  type ActivityTracking = Record<
    string,
    {
      status: string[]
    }
  >

  interface UserSettings {
    locale: Locale
    availableTemplateIds?: TemplateId[]
    defaultTemplateId?: TemplateId
    welcomePopup?: boolean
    activityTracking: ActivityTracking
    profileManagement?: boolean
    roles: Record<string, any>
    euroSvFilesFolderId?: number | null
    euroSvImagesFolderId?: number | null
  }

  /* User */
  interface UserConfiguration {
    settings: UserSettings
  }

  interface TemplateConfiguration {
    settings: TemplateSettings
    products?: ProductsConfiguration
  }

  interface TemplateSettings {
    networkType: NetworkType
    contractType: ContractType
    country: CountryCode
    currency: string
    restriction: Restriction
    initialStatus?: string
    disabledStatuses?: string[]
    externalJS?: string[]
    externalCSS?: string[]
  }

  type ProductsConfiguration = Record<string, any>

  interface TemplateConfiguration {
    settings: TemplateSettings
    products?: ProductsConfiguration
  }

  interface Configuration {
    settings: CustomerSettings

    users: {
      default: UserConfiguration
      [username: string]: Partial<UserConfiguration>
    }

    templates: {
      default: TemplateConfiguration
      [templateId: number]: Partial<TemplateConfiguration>
    }
  }

  interface ConfigurationHistory {
    config: Partial<Configuration>
    timestamp: Date
  }

  interface AppointmentInfo {
    contractId: string
    fromDate: string
    toDate: string
    type: string
    description: string
    registrationNumber: string
    customerInfo: {
      name: string
      surname: string
      telephone: string
      email: string
    }
    address: string
    city: string
    zip: string
    createdBy: number
    assignee: number
    customerName: string
    id: string
  }

  interface ExpiryClaimDate {
    contractId: string
    date: string
    type: string
    description: string
    registrationNumber: string
    customerInfo: {
      name: string
      surname: string
      telephone: string
      email: string
    }
    address: string
    city: string
    zip: string
    createdBy: number
    assignee: number
    id: string
  }

  interface IncrementalNumberData {
    day?: number
    month?: number
    year?: number
    incrementalNumber?: number
  }

  interface IncrementalNumber {
    incrementalNumber?: IncrementalNumberData | Record<string | 'default', IncrementalNumberData>
  }

  interface PatternIncrementalNumber {
    patternIncrementalNumber?: string
  }

  type Plugin =
    | 'common'
    | 'ai-claim'
    | 'ai-gallery'
    | 'calculation'
    | 'claim-management'
    | 'claim-stepper'
    | 'labour-rates-generic'
    | 'assign-partner'
    | 'vhc'
    | 'darva'
    | 'equipment'
    | 'fast-track'
    | 'gallery'
    | 'grapa'
    | 'inbox'
    | 'italian-calculation'
    | 'labour-rates'
    | 'mail'
    | 'part-selection'
    | 'printout'
    | 'profile'
    | 'valuate-finance'
    | 'vehicle-selection'
  type FieldValue = null | string | number | boolean | FieldValue[] | Record<string, FieldValue>
  type Settings = Partial<Record<Plugin, Record<string, FieldValue>>>
  type Username = string

  interface UsersSettings {
    usersSettings: Record<Username, Settings>
  }

  interface AppointmentDates {
    appointmentDates: AppointmentInfo[]
  }

  interface ExpiryClaimDates {
    expiryClaimsDate: ExpiryClaimDate[]
  }

  interface ParcellaConfig {
    parcellaConfig: Record<string, any>
  }

  namespace Deeplink {
    export type ScenarioId = number
    export type ScenarioTheme = {
      dark?: boolean
      silverLogo?: boolean
    }

    export interface DeeplinkSettings {
      defaultScenarioId: ScenarioId
    }

    // need for set custom styles and logo in the header
    export interface ClaimColorPalette {
      colorBg?: string
      colorText?: string
      colorActive?: string
      companyLogo?: string
      companyLogoAlignLeft?: boolean | undefined
      companyLogoBorder?: number | undefined
      companyLogoHeight?: 'extraLarge' | 'large' | 'medium' | 'small'
    }

    export interface ModalContent {
      title?: string
      description?: string
    }

    // for set custom text content
    // if not specified, then the default text from the translations is taken
    export interface DeeplinkTexts {
      header?: string
      completedModal?: ModalContent
      alreadyCompletedModal?: ModalContent
      isNotAvailableModal?: ModalContent
      notAllowedModal?: ModalContent
    }
    // statuses that will be set when certain events
    export interface ScenarioStatuses {
      opened?: string
      finalized?: string
      expired?: string
      pluginCarsCompleted?: string
    }

    // to send the generated link through the specified method
    // for example, an email or message to a phone number
    export interface SendDeeplinkVia {
      description?: string
      requiredInputFields?: string[]
      ruleSetId?: number
    }

    export interface DeeplinkScenarioSettings {
      theme?: ScenarioTheme
      isHidden?: boolean
      description?: string
      expireDate?: number
      expireScheduleTime?: {
        value: string // "year|month|day|HH:mm"
        timezone: string // IANA Format. Ex.: "Europe/Vienna"
        workingDaysOnly: boolean //
        locale: string // "AT"
      }
      statuses?: ScenarioStatuses
      sendVia?: SendDeeplinkVia[]
      texts?: DeeplinkTexts
      claimColorPalette?: ClaimColorPalette
    }

    interface ScenarioConfiguration {
      settings?: DeeplinkScenarioSettings
      'claim-stepper': {
        [key: string]: any
        config?: null | {
          title: string
          plugin?: string[]
          status?: Partial<{
            contractId: number
            statusName: string
            statusType?: string
            statusId?: number
            comment?: string
          }>
          options: any
        }
      }
    }

    interface DeeplinkConfiguration {
      settings: DeeplinkSettings
      scenarios: {
        default: ScenarioConfiguration
        [scenarioId: ScenarioId]: PartialDeep<ScenarioConfiguration>
      }
    }
  }

  interface DeeplinkScenarios {
    scenarios: Deeplink.DeeplinkConfiguration
  }

  type Field = {
    name: string
    type: string
  }

  interface AddressBook {
    addressBook: {
      [key: string]: {
        fields: Field[]
        subjects: Array<Record<string, any>>
      }
    }
  }

  interface Profiles {
    profiles: {
      [username: string]: UserProfile
    }
  }

  interface EditableFields {
    incrementalNumber?: IncrementalNumber
    appointmentDates?: AppointmentDates
    expiryClaimsDate?: ExpiryClaimDates
    scenarios: DeeplinkScenarios
    parcellaConfig: ParcellaConfig
    addressBook: AddressBook
    profiles: Profiles
    patternIncrementalNumber?: PatternIncrementalNumber
    usersSettings?: UsersSettings
    iniMapping?: IniMappingMemoField
  }

  interface GetAppointmentDatesResponse {
    appointmentDates?: AppointmentInfo[]
    expiryClaimsDate?: ExpiryClaimDate[]
  }

  interface IniMappingCompanyData {
    id?: string // company id
    description?: string // company description
    section?: string // section that contains id
    key?: string // key of section that contains id
    // Ex.: for data '014~AXA ASSICURAZIONI' separator is '~' and index is 0
    separator?: string // for case when data containing multi values joined by some symbol and company id is a some part data
    index?: number // if separator specified, index indicates exact part split data with company id
  }

  interface IniMappingSubfield {
    index?: number // if separator exist, then index is part of the split string
    type?: 'string' | 'integer' | 'float' | 'date'
    initialDateFormat?: string // optional param, for parsing date (when type === 'date') ex: 'dd.MM.yyyy' 'ddMMyyyy'
    dateFormat?: string // optional param, for formatting date (when type === 'date') ex: 'dd.MM.yyyy' 'MM/dd/yyyy hh:mm'
    thousandSeparator?: '.' | ',' | ' '
    floatDecimalSeparator?: '.' | ','
    floatFixedDecimalScale?: number
    templateData?: string // memo field name where data should be saved. Ex.: 'licensePlate'
    memoFieldType?: 'xs:string' | 'xs:dateTime' | 'xs:date' | 'xs:decimal' | 'xs:integer' | 'xs:boolean' | string
    dossierData?: string // dossier path where data should be saved. Ex.: 'Vehicle.RegistrationData.NumberPlate' or 'Vehicle.DatECodeEquipment.[0].EquipmentId'
  }

  interface IniMappingField {
    iniSection?: string // ini section where we get data from
    iniKey?: string // ini key in section where we get data from
    separator?: string // for data containing multi values joined by some symbol
    subfields?: IniMappingSubfield[]
  }

  interface IniMappingContractBaseData {
    templateId?: number
    networkType?: DAT.NetworkType
    contractType?: DAT.ContractType
    country?: DAT.CountryCode
    language?: DAT.Locale
    currency?: DAT.Locale
  }

  interface IniMappingContent {
    contractBaseData?: IniMappingContractBaseData
    companyData?: IniMappingCompanyData // company data, needed for detect right fields config
    fields?: IniMappingField[] // fields config with settings for saving data to contract
  }

  interface IniMappingMemoField {
    iniMapping?: IniMappingContent[]
  }
}
