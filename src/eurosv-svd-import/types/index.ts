export interface SvdAttachmentData {
  DIR_FNAME?: string
  DIR_FFUNC?: string
  CR_ORGID?: string
  DIR_ARC?: string
  DIR_TYPE?: string
  ED_ORGID?: string
  CR_TIME?: string
  GF_STATUS?: string
  ED_BENID?: string
  DIR_FEXT?: string
  DIR_FORDER?: string
  DIR_STATUS?: string
  DIR_FGROUP?: string
  ED_TIME?: string
  DIR_FALLOW?: string
  DIR_BENID?: string
  CR_DATE?: string
  CR_BENID?: string
  ED_DATE?: string
}

export interface Assessment {
  DAT_RVP_NAME?: string
  DAT_RVP_WERT?: string
  BEGUTACHTUNGSSTATUS?: string
  QUELLENSTATUS?: string
  ZIELSTATUS?: string
  BEG_SVFOTO?: string
  BEG_WERKSTATTFOTO?: string
  BEG_IDORIGINAL?: string
  REC_ANGELEGT?: string
  ERSTELLUNGSDATUM?: string
  FREMDID_WERT?: string
  GUA_NR?: string
  GUA_INFO?: string
  FREMDID_BEZ?: string
  FREMDID_RECHT?: string
  BEG_FOTOKETTE?: string
  DAX_TASK?: string
  REC_STATUS?: string
  DAX_PROCID?: string
  BEG_WRACKBOERSE?: string
  DAX_STATUS?: string
  BEG_WB_EMPFAENGER?: string
  BEG_SVOEID?: string
  BEG_WB_AUTOONLINE?: string
  DBVERSION?: string
  PROGVERSION?: string
  BEG_AUFTRAGSSYSTEM?: string
  BEG_AUFTRAGSMODUS?: string
  BEG_FOTOKETTEWERK?: string
  BEG_SPARWERT?: string
  BEG_WB_CARTV?: string
  BEG_SPARGRUND?: string
  AUDANET_TASKID?: string
  AUDANET_STATUS?: string
  BEG_REPARIERBAR?: string
  ANZ_FZG?: string
  ANZ_BTG?: string
  ANZ_BES?: string
  ANZ_ALG?: string
  ANZ_WHKZSF?: string
  INFO?: string
  BEG_SVORGID?: string
  BEG_SVBENIDKURZ?: string
  BEG_SVNAME?: string
  BEG_SVLOGINNAME?: string
  BEG_ID?: string
}

export interface Fzg {
  BESCHREIBUNG_MEMO?: string
  FZG_GDHSTATUS?: string
  FZG_ETXMONAT?: string
  ETXFZG_EINFUEHRUNG?: string
  ETXFZG_AUSLAUFEN?: string
  ETXFZG_NEUPREIS?: string
  ETXSON_EINFUEHRUNG?: string
  FZG_BEWGRUPPE?: string
  FZG_BEWLAND?: string
  FZG_ART_TXT?: string
  FZG_MARKE_TXT?: string
  FZG_MODELL_TXT?: string
  FZG_TYPE_TXT?: string
  LETZTZULASSUNG?: string
  PLAKETTE_BIS?: string
  PLAKETTE_NR?: string
  VIGNETTE_NR?: string
  KENNZEICHEN1?: string
  KENNZEICHEN2?: string
  FAHRGESTELLNR?: string
  MOTORKENNUNG?: string
  EIGENGEWICHT?: string
  NUTZLAST?: string
  GESAMTGEWICHT?: string
  SATTELLAST?: string
  SITZPLAETZE?: string
  STEHPLAETZE?: string
  ALLGEMEINZUSTAND?: string
  LACKART?: string
  LACKFARBE?: string
  LACKZUSTAND?: string
  FZG_WAEHRUNG?: string
  FZG_L2E_KURS?: string
  FZG_L2E_EXPONENT?: string
  FZG_ETXHAUPTCODE?: string
  FZG_ETXJAHR?: string
  ETXSON_AUSLAUFEN?: string
  FZG_AXVINSTATUS?: string
  LACKSCHICHTEN?: string
  FZG_BLOBID?: string
  PLAKETTE_BEZ?: string
  FZG_NATIONALCODE?: string
  FZG_AXHAUPTTYP?: string
  FZG_AXUNTERTYP?: string
  MODELLNUMMER?: string
  FZG_AXHERSTELLER?: string
  VIGNETTE_ART?: string
  VIGNETTE_BIS?: string
  FZG_DATART?: string
  FZG_DATHERST?: string
  FZG_DATHT?: string
  FZG_DATUT?: string
  FZG_VPSTATUS?: string
  FZG_VINPLUS_STATUS?: string
  KENNZEICHEN1_LAND?: string
  KENNZEICHEN2_LAND?: string
  FZG_MARKTANALYSE_TYP?: string
  FZG_GDHANSTOSSBEREICH?: string
  FZG_GDHIGNORESEND?: string
  ANZ_PKW_AT?: string
  ANZ_FZGBLOB?: string
}

// Repairer, Insurance,… and etc
export interface Participants {
  BTG_ORT?: string
  BTG_MAILDOKEXPORT?: string
  BTG_WAEHRUNG?: string
  BTG_TELEFON?: string
  BTG_WRACKBOERSE?: string
  BTG_FibuKontoNr?: string
  BTG_SORT?: string
  BTG_BTGSID?: string
  BTG_BESCHREIBUNG?: string
  BTG_MAILATT?: string
  BTG_PLZ?: string
  BTG_HANDY?: string
  BTG_NAME1?: string
  BTG_NAME2?: string
  BTG_ZUHANDEN?: string
  REC_STATUS?: string
  BTG_NAME1_BEZ?: string
  BTG_GEOCOORD_LAT?: string
  BTG_GEOCOORD_LONG?: string
  BTG_EMAIL?: string
  BTG_AUDANETID?: string
  BTG_UIDNR?: string
  BTG_TYP?: string
  BTG_GBTID?: string
  BTG_NR?: string
  BTG_EXPORTPROGRAMM?: string
  BTG_LANGUAGE?: string
  BTG_STRASSE?: string
  BTG_FAX?: string
  BTG_LKZ?: string
  BTG_AUDANETNAME?: string
}

export interface Order {
  KVA_NETTO?: string
  AUFTRAGGEBER?: string
  REPWEG_PRUEFEN?: string
  HINWEIS?: string
  ALL_BEGID_OUT?: string
  POLIZZENR?: string
  SCHADENNR?: string
  LEASING?: string
  AUFTRAGSDATUM?: string
  KVA_DATUM?: string
  CHECK_INOUT_STATUS?: string
  BEMERKUNG_MEMO?: string
  REPWEG_EINSPAR?: string
  AUFTRAGGEBER_ANREDE?: string
  POLIZZENR_BEZ?: string
  ALL_BEGID?: string
  VN_KENNZ?: string
  DECKUNGSART?: string
  HINWEIS2?: string
  KVA_NUMMER?: string
  ANZ_DOCBLOB?: string
  VN_KENNZ_LAND?: string
  ALG_GUAART?: string
  ALG_SCHADENERW?: string
}

export interface Sender {
  DBVERSION?: string
  ERSTELLUNGSDATUM?: string
  PROGRAMM?: string
  PROGRAMMVERSION?: string
  ANZ_OE?: string
}

export interface SvdInfo {
  ANZ_ABSENDER?: string
  SVD_VERSION?: string
  SVD_SUBVERSION?: string
  INFO?: string
  SVD_TYP?: string
}

export interface Pkw {
  RESTWERT_BEZ?: string
  BERECHNUNGSD_KETTE?: string
  ANZAHL_TUEREN?: string
  EINZELGEN_ABW?: string
  SBG_NUTZUNGSART_ZUSCHLAG?: string
  TAXI_ABW?: string
  NOT_EZ_VON?: string
  NW_PRZMONATLICH?: string
  STEUER_AUSGABE?: string
  SBG_ANZVORSCHADEN?: string
  V5080_KLASSE?: string
  BERECHNUNGSDATUM?: string
  NW_BEZ?: string
  DIVERSE_BART?: string
  SBG_NUTZUNGSART?: string
  AUFBAUCODE?: string
  NW_NOVA_AUFABSCHLAG?: string
  NW_ERRECHNET?: string
  NW_NOVA?: string
  PKW_U2ART?: string
  KM_KETTE?: string
  UST_TXTDRUCKEN?: string
  WERTENETTO?: string
  KM_SOLL?: string
  PKW_ART?: string
  EINZELGEN_BEZ?: string
  UST_ABZUGSPFLICHT?: string
  RESTWERT_WERT?: string
  NOT_2_PRZHELP?: string
  VP_INDEX?: string
  NOTB_EZ?: string
  VP_MEDIAN?: string
  NEUWERT?: string
  DRUCK_GB_AUSWAHL?: string
  WERTMINDERUNG_MEMO?: string
  ERSTZULASSUNG?: string
  NW_PRZANMELDUNG?: string
  TACHO_ABGELESEN?: string
  UST_NEUWERT?: string
  SA_ZUABW_G?: string
  NOTG_2?: string
  ALTSCHADEN_MEMO?: string
  WERTMINDERUNG_WERT?: string
  WERTMINDERUNG_KETTE?: string
  MONKORR_BEW_ART?: string
  KM_ABWMAX?: string
  SA_BEW_ART?: string
  HUBRAUM?: string
  RND_WERTMINDERUNG?: string
  STEUER_EINGABE?: string
  RND_ZWWERTE?: string
  VW_BEZ?: string
  VW_ERRECHNET?: string
  MOTORART?: string
  VP_KORRWERT?: string
  NOTG_EZ?: string
  PKW_UART?: string
  VORBESITZER_BEZ?: string
  ALTSCHADEN_ABW?: string
  UST_AUSWAHL?: string
  DIVERSE_BEW?: string
  GETRIEBEART?: string
  TACHO_DIM?: string
  NEUWERT_VONETX?: string
  SBG_EINSATZBEDINGUNGSART?: string
  TAXI_BEZ?: string
  V5080_ABW?: string
  VP_RED_PROZENT?: string
  SBG_VORSCHADEN_WBW?: string
  RESTWERT_TXT?: string
  WBW_ERRECHNET?: string
  RND_NETTOWERTE?: string
  VP_MAX?: string
  TACHO_IST?: string
  WERTMINDERUNG_ART?: string
  KM_AUFWMAX?: string
  LEISTUNG?: string
  WERTMINDERUNG?: string
  WBW_BEZ?: string
  RND_ENDWERTE?: string
  VORBESITZER_ANZ?: string
  NOT_2_VON?: string
  KM_KORREKTUR_ART?: string
  VORBESITZER_ABW?: string
  DIVERSE_TXT?: string
  VERSION?: string
  NOTB_2?: string
  VP_MIN?: string
  RESTWERT?: string
  UST_FZGWERTE?: string
  WEM_FZGALTER_MAX?: string
  VERH_G2B?: string
}

export interface Calculation {
  SCHADENART?: string
  ERSTSCHAETZUNG?: string
  KALKULATIONSTATUS?: string
  AZ_OPTV?: string
  SS_OPTV?: string
  NEBENKOSTEN_PRZ?: string
  ST_MEEL?: string
  FREMDLEISTUNGTEXT?: string
  WHK_FREMDSYSTEM?: string
  LACKMAT_FAKTOR?: string
  KLEINMATERIAL_WERTLK5?: string
  ZT_SPMO?: string
  LACKMAT_BART?: string
  SS_SPMO_HERSTELLER?: string
  TZ2_LACK?: string
  REC_STATUS?: string
  SS_MEEL?: string
  ST_OPTV?: string
  SCHADENMEMO?: string
  KLEINMAT_WERT?: string
  ZT_LACK?: string
  SS_SPMO_LK1?: string
  SS_SPMO_LK3?: string
  SS_SPMO_LK2?: string
  SS_SPMO_LK5?: string
  WHK_BLOBID?: string
  SS_SPMO_LK4?: string
  WHK_SUMMEBRUTTO?: string
  WHK_SUMMEMAXIDT?: string
  AX_KALKANZ?: string
  WHK_TSUMBRUTTO?: string
  TZ1_LACK?: string
  AX_KALKULATIONSNR?: string
  AX_WHKOSTEN?: string
  AX_OPTIMIERT?: string
  UNTERBODEN_WERT?: string
  NZEIT_BART?: string
  WHK_IDTMANUFACTURERID?: string
  WHK_SUMMEUST?: string
  WHK_FAKTOR?: string
  NR_SPMO_LK1?: string
  NR_SPMO_LK2?: string
  NR_SPMO_LK3?: string
  WHK_PRZ_MKT?: string
  WHK_IDTMODIFIED?: string
  BESCHAFFUNGSK_MAXART?: string
  FREMDLEISTUNG_WERT?: string
  WHK_FREMD_DATENSTAND?: string
  WHK_SUMMEIDT?: string
  TEILSCHADEN?: string
  SCHADENNUMMER_BEZ?: string
  AX_KALKDATUM?: string
  SCHADENNUMMER?: string
  WHK_FREMDKOSTEN?: string
  WHK_LAUFLEISTUNG_ANALOG?: string
  AX_MODIFIED?: string
  BESCHAFFUNGSK_MAXWERT?: string
  SS_NZEIT?: string
  SCHADENDATUM2?: string
  AUDANET_LASTEDITED?: string
  SS_SPMO_NR?: string
  OPTV_BART?: string
  ST_SPMO?: string
  NR_LACK_LK1?: string
  ZT_MEEL?: string
  DAT_WHKOSTEN?: string
  AX_TERMINALID?: string
  NR_LACK_LK2?: string
  NR_LACK_LK3?: string
  WHK_SORT?: string
  WHK_SUMMENETTO?: string
  ANSTOSSBEREICH?: string
  MEEL_BEZ?: string
  WHK_SUMMEORIGINALERS?: string
  ST_NZEIT?: string
  AUDANET_TASKID?: string
  SCHADENDATUM?: string
  SCHADEN_BEZ?: string
  ENTSORGUNG_BART?: string
  SS_LACK_LK1?: string
  ANZ_WHKEVAL?: string
  ANZ_WHKBLOB?: string
  ZT_OPTV?: string
  WHK_LACKSYSTEM?: string
  WHK_WHKIDHAUPTSCHADEN?: string
  FREMDLEISTUNG_BEZ?: string
  ENTSORGUNG_MAXWERT?: string
  WHK_PRZ_VZM?: string
  BESCHAFFUNGSK_MAXPROZENT?: string
  WHK_PRZ_VZK?: string
  REPARATURMEMO_BEZ?: string
  ENTSORGUNG_MAXART?: string
  AX_KUNDENNR?: string
  SCHADENMEMO_BEZ?: string
  KLEINMATERIAL_WERTLK13?: string
  KLEINMAT_MAXWERT?: string
  BESCHAFFUNGSKOSTEN?: string
  ZT_NZEIT?: string
  WHK_WAEHRUNG?: string
  ST_LACK?: string
  AZ_MEEL?: string
  AX_SPERRE?: string
  ORIGINALPARTS_WHK_ID?: string
  KLEINMAT_BART?: string
  ENTSORGUNG_WERT?: string
  WHK_SCHADENURSACHE?: string
  SCHADENDATUM_BEZ?: string
  AZ_NZEIT?: string
  WHK_EREWHKOSTEN?: string
  NZEIT_BEZ?: string
  KLEINMAT_MAXART?: string
  OPTV_BEZ?: string
  REPARATURMEMO?: string
  AUDANET_CASEID?: string
  ALTERNATIVEPARTSCALC?: string
  SS_RFSSID?: string
}

export interface Valuation {
  ErsatzteileOriginal?: string
  Mechaniker?: string
  LackmaterialGesamt?: string
  LohnLackierung?: string
  ErsatzteileGesamt?: string
  WhKostenNetto?: string
  Beschaffungskosten?: string
  Unterboden?: string
  ErsatzteileZubehoer?: string
  Nebenkostenpauschale?: string
  Nebenzeit?: string
  Kleinmaterial?: string
  WhKostenBrutto?: string
  Vermessen?: string
  Entsorgung?: string
  Fremdleistung?: string
  LohnErsatzteile?: string
  WHKOSTENBRUTTO?: string
  ERSATZTEILEGESAMT?: string
  UNTERBODEN?: string
  BESCHAFFUNGSKOSTEN?: string
  ENTSORGUNG?: string
  KLEINMATERIAL?: string
  LOHNERSATZTEILE?: string
  LOHNLACKIERUNG?: string
  LACKMATERIALGESAMT?: string
}

export interface WhkZsf {
  ABLOESE_ART?: string
  UST_ABLOESE?: string
  SCHADENART?: string
  IDENTSTEUERCODE?: string
  WHK_L2E_EXPONENT?: string
  ANZ_WHK?: string
  RECHNUNGSMEMO?: string
  ABLOESE_KNTNR?: string
  SBG_FUEGETECHNIK_ART?: string
  RECHNUNGSDATUM?: string
  NEBENKOSTENPAUSCHALE?: string
  WHKZSF_SUMMEBRUTTO?: string
  REC_STATUS?: string
  SBG_IMPORTEUR?: string
  ETX_LACKSCHICHTEN?: string
  WHK_L2E_KURS?: string
  WHK_ETXMONAT?: string
  REPARATURTAGE?: string
  WHKZSF_SUMMENETTO?: string
  ANMERKUNG_MEMO?: string
  ABLOESE?: string
  MWST_WHKOSTEN?: string
  REPARATURSTUNDEN?: string
  WHK_WAEHRUNG?: string
  REPARATURTAGE2?: string
  WHKZSF_SUMMEUST?: string
  ABLOESE_BANK?: string
  WHKZSF_ANZSCHADEN?: string
  SBG_FUEGETECHNIK_ZUSCHLAG?: string
  WHKZSF_ANZTEILSCHADEN?: string
  ABLOESE_BIC?: string
  ABLOESE_WERT?: string
  KETTE?: string
  WHKZSF_TSUMBRUTTO?: string
  SBG_HAENDLERBETEILIGUNG?: string
  SBG_SCHADENSKATEGORIE?: string
  SBG_SCHADEN_INSTART?: string
  ABLOESE_KNTNAME?: string
  ABLOESE_BLZ?: string
  ANMERKUNG_BEZ?: string
  SBG_VERSCHLEISSGRENZE_ART?: string
  MITTEILUNGSMEMO?: string
  WHK_ETXJAHR?: string
  ABLOESE_IBAN?: string
}

export interface Bes {
  BES_DATUM?: string
  BES_BTGNR?: string
  BES_ANWESENDER?: string
  BES_SVNAME?: string
}

export interface Son {
  SON_PREIS?: string
}

export interface SvdContent {
  ABSENDER?: Sender[]
  ALG?: Order[]
  BEG?: Assessment[]
  BTG?: Participants[]
  FZG?: Fzg[]
  BES?: Bes[]
  FZGBLOB?: SvdAttachmentData[]
  INHALT?: SvdInfo[]
  PKW_AT?: Pkw[]
  SON?: Son[]
  WHK?: Calculation[]
  WHKEVAL?: Valuation[]
  WHKZSF?: WhkZsf[]
}
