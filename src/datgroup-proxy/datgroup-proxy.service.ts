import { HttpService } from '@nestjs/axios'
import { Injectable, Logger, InternalServerErrorException } from '@nestjs/common'
import { lastValueFrom } from 'rxjs'
import { ConfigService } from 'src/config/config.service'
import { HeadersNames } from 'src/common/constants/headers-names'
import { AxiosRequestConfig, isAxiosError } from 'axios'
import { VsInteractiveRequestDto, VsInteractiveResponseDto } from './dto'
import { Response } from 'express'
import { HttpHeaders } from 'src/common/types/common'

@Injectable()
export class DatgroupProxyService {
  private URL: string

  constructor(private httpService: HttpService, private configService: ConfigService) {
    this.URL = this.configService.getDatgroupUrl()
  }

  private async proxy<T>(requestConfig: AxiosRequestConfig) {
    try {
      const res = await lastValueFrom(
        this.httpService.request<T>({
          baseURL: this.URL,
          ...requestConfig
        })
      )

      return res
    } catch (err) {
      Logger.error('DatgroupProxyService Error', err)
      if (isAxiosError(err) && err.response) {
        throw new InternalServerErrorException({
          status: err.response.status,
          error: err.response.data
        })
      }
      throw new InternalServerErrorException({
        status: 500,
        error: 'Internal server error'
      })
    }
  }

  public async executeVehicleSelectionInteractive(res: Response, headers: HttpHeaders, data: VsInteractiveRequestDto) {
    const proxyResponse = await this.proxy({
      method: 'POST',
      url: '/DATECodeSelection/rest/VehicleSelectionService/executeVehicleSelectionInteractive',
      data,
      headers: {
        [HeadersNames.XDatVsToken]: headers[HeadersNames.XDatVsToken],
        [HeadersNames.DatAuthToken]: headers[HeadersNames.DatAuthToken]
      }
    })

    Object.entries(proxyResponse.headers).forEach(([key, value]) => {
      res.setHeader(key, value)
    })

    return res.send(JSON.parse(proxyResponse.data as string) as VsInteractiveResponseDto)
  }
}
