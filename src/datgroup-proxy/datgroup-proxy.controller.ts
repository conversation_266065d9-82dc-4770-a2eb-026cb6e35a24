import { Controller, ValidationPipe, Post, Body, Headers, Res } from '@nestjs/common'
import { DatgroupProxyService } from './datgroup-proxy.service'
import { SwaggerDoc } from 'src/common/decorators/swagger-doc.decorator'
import { datgroupProxyTag } from 'src/common/constants/swagger'
import { ApiTags } from '@nestjs/swagger'
import { HttpHeaders } from 'src/common/types/common'
import { VsInteractiveRequestDto, VsInteractiveResponseDto } from './dto'
import { Response } from 'express'

@ApiTags(datgroupProxyTag)
@Controller(datgroupProxyTag)
export class DatgroupProxyController {
  constructor(private readonly datgroupProxyService: DatgroupProxyService) {}

  @Post('/DATECodeSelection/rest/VehicleSelectionService/executeVehicleSelectionInteractive')
  @SwaggerDoc({
    isAuth: true,
    summary: 'execute vehicle selection interactive',
    typeResponse: VsInteractiveResponseDto
  })
  async executeVehicleSelectionInteractive(
    @Res() res: Response,
    @Headers() headers: HttpHeaders,
    @Body() body: VsInteractiveRequestDto
  ) {
    return this.datgroupProxyService.executeVehicleSelectionInteractive(res, headers, body)
  }
}
