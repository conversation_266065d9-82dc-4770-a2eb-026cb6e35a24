import { ApiProperty } from '@nestjs/swagger'
import { LocaleDto } from 'src/common/dto'
import { ValidateNested, IsObject, IsString, IsNotEmpty, IsArray, IsOptional, IsBoolean } from 'class-validator'
import { Type } from 'class-transformer'

type FilterKeys =
  | 'manufacturer'
  | 'vehicleTypeSummarized'
  | 'mainTypeGroupFb'
  | 'structureType'
  | 'structureTypeHgv'
  | 'mainTypeForMotorcycles'
  | 'subTypeForMotorcycles'
  | 'fuelMethod'
  | 'countOfDoors'
  | 'driveType'
  | 'constructionYearHgv'
  | 'axleCount'
  | 'tonnage'
  | 'emissionClass'
  | 'powerKw'
  | 'constructionYear'
  | 'engine'
  | 'wheelbase'
  | 'drivingCab'
  | 'suspension'
  | 'propulsion'
  | 'gearType'
  | 'countOfGears'
  | 'gearbox'
  | 'mainTypeFb'
  | 'subType'

// REQUEST

class RequestFilterItemDto {
  @IsString()
  @ApiProperty({ example: 'manufacturer', type: String })
  key: FilterKeys

  @IsString()
  @ApiProperty({ example: '130', type: String })
  value: string
}

class VsInteractiveRequestBodyDto {
  @ApiProperty({ type: LocaleDto })
  @IsObject()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => LocaleDto)
  locale: LocaleDto

  @ApiProperty({ example: 'APPRAISAL', type: String })
  @IsString()
  @IsNotEmpty()
  restriction: 'APPRAISAL'

  @IsArray()
  @IsOptional()
  @ApiProperty({ example: [1, 2, 3], type: [Number], required: false })
  allowedVehicleTypes?: number[]

  @IsString()
  @IsOptional()
  @ApiProperty({ example: '2025-01-01', type: String, required: false })
  registrationDate?: string // YYYY-MM-DD

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ example: true, type: Boolean, required: false })
  withEquipmentMatrix?: boolean

  @IsString()
  @IsOptional()
  @ApiProperty({ example: '123456', type: String, required: false })
  datECode?: string

  @IsArray()
  @IsOptional()
  @ApiProperty({ example: ['123456', '789012'], type: [String], required: false })
  datECodes?: string[]

  @IsArray()
  @IsOptional()
  @ApiProperty({ type: [RequestFilterItemDto], required: false })
  filter?: RequestFilterItemDto[]
}

export class VsInteractiveRequestDto {
  @ApiProperty({ type: VsInteractiveRequestBodyDto })
  @IsObject()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => VsInteractiveRequestBodyDto)
  request: VsInteractiveRequestBodyDto
}

// RESPONSE

class ResponseProposalDto {
  @ApiProperty({ example: '20', type: String })
  k: string

  @ApiProperty({ example: 'Fiat', type: String })
  v: string

  @ApiProperty({ example: 'Fiat', type: String, required: false })
  a?: string // ambiguous data
}

class ResponseFilterItemDto {
  @ApiProperty({ example: 'manufacturer', type: String })
  key: FilterKeys

  @ApiProperty({ example: 'Manufacturer', type: String })
  label: string

  @ApiProperty({ type: [ResponseProposalDto], required: false })
  proposals?: ResponseProposalDto[]
}

class VsInteractiveVehicleDto {
  @ApiProperty({ example: 'Pkw, SUV, Kleintransporter', type: String, required: false })
  vehicleTypeName?: string

  @ApiProperty({ example: 'BMW', type: String, required: false })
  manufacturerName?: string

  @ApiProperty({ example: 'Baureihe X5 (F15) (2013->)', type: String, required: false })
  mainTypeName?: string

  @ApiProperty({ example: 'xDrive40e', type: String, required: false })
  subTypeName?: string

  @ApiProperty({ example: 'DE - SUV5 xDrive40e EU6, 2015 - 2018', type: String, required: false })
  containerName?: string

  @ApiProperty({ example: 5480, type: Number, required: false })
  constructionTimeFrom?: number

  @ApiProperty({ example: 5889, type: Number, required: false })
  constructionTimeTo?: number

  @ApiProperty({
    example: {
      '1': 'Hybrid 230 kW (Motor 2,0 Ltr. - 180 kW)',
      '2': 'Karosserie: 5-türig',
      '3': 'Radstand 2933 mm',
      '4': 'Antriebsart: Allradantrieb',
      '11': 'Getriebe Automatik - mit Steptronic (8-Stufen)'
    },
    type: Object,
    required: false
  })
  equipments?: Record<string, string>

  @ApiProperty({
    example: {
      '70410': 'Antriebsart: xDrive (Allrad)',
      '77227': 'Schadstoffarm nach Abgasnorm Euro 6',
      '77601': 'Start\\/Stop-Anlage (Funktion)'
    },
    type: Object,
    required: false
  })
  equipmentsSuper?: Record<string, string>

  @ApiProperty({ example: 57479, type: Number, required: false })
  exFactoryPrice?: number

  @ApiProperty({ example: 'EUR', type: String, required: false })
  currency?: string

  @ApiProperty({ example: '011301500550001DE001', type: String, required: false })
  ecode?: string
}

class VsInteractiveEquipmentDto {
  @ApiProperty({ example: 42170, type: Number })
  av: number

  @ApiProperty({ example: 'Adaptives Fahrwerks-Paket: Comfort', type: String })
  name: string

  @ApiProperty({ example: { '011301500550002DE001': 'S' }, type: Object })
  available: Record<string, string>
}

export class VsInteractiveResponseDto {
  @ApiProperty({ example: 10, type: Number, required: false })
  count?: number

  @ApiProperty({ type: [ResponseFilterItemDto] })
  filters: ResponseFilterItemDto[]

  @ApiProperty({ type: [VsInteractiveVehicleDto], required: false })
  vehicles?: VsInteractiveVehicleDto[]

  @ApiProperty({ type: [VsInteractiveEquipmentDto], required: false })
  equipmentMatrix?: VsInteractiveEquipmentDto[]
}
