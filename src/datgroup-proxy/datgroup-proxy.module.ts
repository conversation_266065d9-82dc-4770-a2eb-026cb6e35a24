import { Module } from '@nestjs/common'
import { DatgroupProxyService } from './datgroup-proxy.service'
import { DatgroupProxyController } from './datgroup-proxy.controller'
import { HttpModule } from '@nestjs/axios'
import { ConfigModule } from 'src/config/config.module'
import { AuthModule } from 'src/auth/auth.module'

@Module({
  imports: [HttpModule.register({}), ConfigModule, AuthModule],
  controllers: [DatgroupProxyController],
  providers: [DatgroupProxyService]
})
export class DatgroupProxyModule {}
