# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@angular-devkit/core@npm:16.0.1":
  version: 16.0.1
  resolution: "@angular-devkit/core@npm:16.0.1"
  dependencies:
    ajv: "npm:8.12.0"
    ajv-formats: "npm:2.1.1"
    jsonc-parser: "npm:3.2.0"
    rxjs: "npm:7.8.1"
    source-map: "npm:0.7.4"
  peerDependencies:
    chokidar: ^3.5.2
  peerDependenciesMeta:
    chokidar:
      optional: true
  checksum: 10c0/061e9248f43dbbe11a74ccebe01152a93f1ebd43463f503d92bdb1bab8369a3568147b0b92ef4f285b8782f07532db62b83b639322479301e28fa606859fbccc
  languageName: node
  linkType: hard

"@angular-devkit/schematics-cli@npm:16.0.1":
  version: 16.0.1
  resolution: "@angular-devkit/schematics-cli@npm:16.0.1"
  dependencies:
    "@angular-devkit/core": "npm:16.0.1"
    "@angular-devkit/schematics": "npm:16.0.1"
    ansi-colors: "npm:4.1.3"
    inquirer: "npm:8.2.4"
    symbol-observable: "npm:4.0.0"
    yargs-parser: "npm:21.1.1"
  bin:
    schematics: bin/schematics.js
  checksum: 10c0/cd151fb25b93d6f3d543dc2c2a4f1bf83f9f0361c327583900e7d64d8583fd75b36b7bab878f9dc3f18ba6ad055713fecddd7711be3a2c5fee0d3d3c2c782b3d
  languageName: node
  linkType: hard

"@angular-devkit/schematics@npm:16.0.1":
  version: 16.0.1
  resolution: "@angular-devkit/schematics@npm:16.0.1"
  dependencies:
    "@angular-devkit/core": "npm:16.0.1"
    jsonc-parser: "npm:3.2.0"
    magic-string: "npm:0.30.0"
    ora: "npm:5.4.1"
    rxjs: "npm:7.8.1"
  checksum: 10c0/26f6ef07ced08273ad9384867c92d4b295a013115355597306e84da1d3445dc41f90f9046dbe7024b7ed568f20dd4e28c070b59f10f0dea8e44cbe9f31825408
  languageName: node
  linkType: hard

"@asamuzakjp/css-color@npm:^3.1.2":
  version: 3.1.4
  resolution: "@asamuzakjp/css-color@npm:3.1.4"
  dependencies:
    "@csstools/css-calc": "npm:^2.1.3"
    "@csstools/css-color-parser": "npm:^3.0.9"
    "@csstools/css-parser-algorithms": "npm:^3.0.4"
    "@csstools/css-tokenizer": "npm:^3.0.3"
    lru-cache: "npm:^10.4.3"
  checksum: 10c0/e511c0be9872e26399a3d6a117cb81d6ced52db1b6e5562a36d3d32cfbd491dba9dbbc6fa6487d7dd79470baebb151a416c10cdb759306ae50a800995674eb2a
  languageName: node
  linkType: hard

"@aws-crypto/crc32@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/crc32@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/eab9581d3363af5ea498ae0e72de792f54d8890360e14a9d8261b7b5c55ebe080279fb2556e07994d785341cdaa99ab0b1ccf137832b53b5904cd6928f2b094b
  languageName: node
  linkType: hard

"@aws-crypto/crc32c@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/crc32c@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/223efac396cdebaf5645568fa9a38cd0c322c960ae1f4276bedfe2e1031d0112e49d7d39225d386354680ecefae29f39af469a84b2ddfa77cb6692036188af77
  languageName: node
  linkType: hard

"@aws-crypto/sha1-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha1-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/supports-web-crypto": "npm:^5.2.0"
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    "@aws-sdk/util-locate-window": "npm:^3.0.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/51fed0bf078c10322d910af179871b7d299dde5b5897873ffbeeb036f427e5d11d23db9794439226544b73901920fd19f4d86bbc103ed73cc0cfdea47a83c6ac
  languageName: node
  linkType: hard

"@aws-crypto/sha256-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/sha256-js": "npm:^5.2.0"
    "@aws-crypto/supports-web-crypto": "npm:^5.2.0"
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    "@aws-sdk/util-locate-window": "npm:^3.0.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/05f6d256794df800fe9aef5f52f2ac7415f7f3117d461f85a6aecaa4e29e91527b6fd503681a17136fa89e9dd3d916e9c7e4cfb5eba222875cb6c077bdc1d00d
  languageName: node
  linkType: hard

"@aws-crypto/sha256-js@npm:5.2.0, @aws-crypto/sha256-js@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-js@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6c48701f8336341bb104dfde3d0050c89c288051f6b5e9bdfeb8091cf3ffc86efcd5c9e6ff2a4a134406b019c07aca9db608128f8d9267c952578a3108db9fd1
  languageName: node
  linkType: hard

"@aws-crypto/supports-web-crypto@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/supports-web-crypto@npm:5.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/4d2118e29d68ca3f5947f1e37ce1fbb3239a0c569cc938cdc8ab8390d595609b5caf51a07c9e0535105b17bf5c52ea256fed705a07e9681118120ab64ee73af2
  languageName: node
  linkType: hard

"@aws-crypto/util@npm:5.2.0, @aws-crypto/util@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/util@npm:5.2.0"
  dependencies:
    "@aws-sdk/types": "npm:^3.222.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/0362d4c197b1fd64b423966945130207d1fe23e1bb2878a18e361f7743c8d339dad3f8729895a29aa34fff6a86c65f281cf5167c4bf253f21627ae80b6dd2951
  languageName: node
  linkType: hard

"@aws-sdk/client-s3@npm:^3.454.0":
  version: 3.741.0
  resolution: "@aws-sdk/client-s3@npm:3.741.0"
  dependencies:
    "@aws-crypto/sha1-browser": "npm:5.2.0"
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/credential-provider-node": "npm:3.741.0"
    "@aws-sdk/middleware-bucket-endpoint": "npm:3.734.0"
    "@aws-sdk/middleware-expect-continue": "npm:3.734.0"
    "@aws-sdk/middleware-flexible-checksums": "npm:3.735.0"
    "@aws-sdk/middleware-host-header": "npm:3.734.0"
    "@aws-sdk/middleware-location-constraint": "npm:3.734.0"
    "@aws-sdk/middleware-logger": "npm:3.734.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.734.0"
    "@aws-sdk/middleware-sdk-s3": "npm:3.740.0"
    "@aws-sdk/middleware-ssec": "npm:3.734.0"
    "@aws-sdk/middleware-user-agent": "npm:3.734.0"
    "@aws-sdk/region-config-resolver": "npm:3.734.0"
    "@aws-sdk/signature-v4-multi-region": "npm:3.740.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@aws-sdk/util-endpoints": "npm:3.734.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.734.0"
    "@aws-sdk/util-user-agent-node": "npm:3.734.0"
    "@aws-sdk/xml-builder": "npm:3.734.0"
    "@smithy/config-resolver": "npm:^4.0.1"
    "@smithy/core": "npm:^3.1.1"
    "@smithy/eventstream-serde-browser": "npm:^4.0.1"
    "@smithy/eventstream-serde-config-resolver": "npm:^4.0.1"
    "@smithy/eventstream-serde-node": "npm:^4.0.1"
    "@smithy/fetch-http-handler": "npm:^5.0.1"
    "@smithy/hash-blob-browser": "npm:^4.0.1"
    "@smithy/hash-node": "npm:^4.0.1"
    "@smithy/hash-stream-node": "npm:^4.0.1"
    "@smithy/invalid-dependency": "npm:^4.0.1"
    "@smithy/md5-js": "npm:^4.0.1"
    "@smithy/middleware-content-length": "npm:^4.0.1"
    "@smithy/middleware-endpoint": "npm:^4.0.2"
    "@smithy/middleware-retry": "npm:^4.0.3"
    "@smithy/middleware-serde": "npm:^4.0.1"
    "@smithy/middleware-stack": "npm:^4.0.1"
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/node-http-handler": "npm:^4.0.2"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/smithy-client": "npm:^4.1.2"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/url-parser": "npm:^4.0.1"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.3"
    "@smithy/util-defaults-mode-node": "npm:^4.0.3"
    "@smithy/util-endpoints": "npm:^3.0.1"
    "@smithy/util-middleware": "npm:^4.0.1"
    "@smithy/util-retry": "npm:^4.0.1"
    "@smithy/util-stream": "npm:^4.0.2"
    "@smithy/util-utf8": "npm:^4.0.0"
    "@smithy/util-waiter": "npm:^4.0.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/a06bd521c43f5750fcdbac3542a49322adc788d1bb9f5e739223e93b21ae2ea15dad35a1dee4690071616952b6998a3a5113e416b72fdd3d9db0ea671e030cfa
  languageName: node
  linkType: hard

"@aws-sdk/client-sso@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/client-sso@npm:3.734.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/middleware-host-header": "npm:3.734.0"
    "@aws-sdk/middleware-logger": "npm:3.734.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.734.0"
    "@aws-sdk/middleware-user-agent": "npm:3.734.0"
    "@aws-sdk/region-config-resolver": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@aws-sdk/util-endpoints": "npm:3.734.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.734.0"
    "@aws-sdk/util-user-agent-node": "npm:3.734.0"
    "@smithy/config-resolver": "npm:^4.0.1"
    "@smithy/core": "npm:^3.1.1"
    "@smithy/fetch-http-handler": "npm:^5.0.1"
    "@smithy/hash-node": "npm:^4.0.1"
    "@smithy/invalid-dependency": "npm:^4.0.1"
    "@smithy/middleware-content-length": "npm:^4.0.1"
    "@smithy/middleware-endpoint": "npm:^4.0.2"
    "@smithy/middleware-retry": "npm:^4.0.3"
    "@smithy/middleware-serde": "npm:^4.0.1"
    "@smithy/middleware-stack": "npm:^4.0.1"
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/node-http-handler": "npm:^4.0.2"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/smithy-client": "npm:^4.1.2"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/url-parser": "npm:^4.0.1"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.3"
    "@smithy/util-defaults-mode-node": "npm:^4.0.3"
    "@smithy/util-endpoints": "npm:^3.0.1"
    "@smithy/util-middleware": "npm:^4.0.1"
    "@smithy/util-retry": "npm:^4.0.1"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/8098f0516c31ee1cb0f7c82932d8bcfd4a6f85f1945c0d022402c72c40c389a04b50888543e7de43a3c8db00203bbd00d3d13a9570f37f5e4fe8253085f72df8
  languageName: node
  linkType: hard

"@aws-sdk/core@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/core@npm:3.734.0"
  dependencies:
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/core": "npm:^3.1.1"
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/signature-v4": "npm:^5.0.1"
    "@smithy/smithy-client": "npm:^4.1.2"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-middleware": "npm:^4.0.1"
    fast-xml-parser: "npm:4.4.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/1f301a3a1fa8172bacf881482bdbf10ac8212d9c6e1b726df66958994a8eaec7202f2d795e8668ae23ec4563067db4e4068ea8496a436426dd38ebd0f76d0f3e
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-env@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/credential-provider-env@npm:3.734.0"
  dependencies:
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/27071ce049fc6c73a65478f2dbbe9de21a5d4558a93d8c9ea4b9101b41323cbde012614ef7f87467e6f05515afa8cf5fc556a579b359ce83ebbf786493ee94fc
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-http@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/credential-provider-http@npm:3.734.0"
  dependencies:
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/fetch-http-handler": "npm:^5.0.1"
    "@smithy/node-http-handler": "npm:^4.0.2"
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/smithy-client": "npm:^4.1.2"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-stream": "npm:^4.0.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/60edc09a92f91049bd61f3b51700ceeaa1c429d1e41e25a39560bbe56f1f0623a3a475577e265d89552f31c6d6388acda5e073f3a111692b27f07c0ad824b613
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-ini@npm:3.741.0":
  version: 3.741.0
  resolution: "@aws-sdk/credential-provider-ini@npm:3.741.0"
  dependencies:
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/credential-provider-env": "npm:3.734.0"
    "@aws-sdk/credential-provider-http": "npm:3.734.0"
    "@aws-sdk/credential-provider-process": "npm:3.734.0"
    "@aws-sdk/credential-provider-sso": "npm:3.734.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.734.0"
    "@aws-sdk/nested-clients": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/credential-provider-imds": "npm:^4.0.1"
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/shared-ini-file-loader": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/7dc88dee6a7495d76660627e5815e7b3eb803b2f9ffc729c4191b48e55272ba0784451f872e3975361a49701c71667120d7d9e668e90c8e149d4f6d616799fed
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-node@npm:3.741.0":
  version: 3.741.0
  resolution: "@aws-sdk/credential-provider-node@npm:3.741.0"
  dependencies:
    "@aws-sdk/credential-provider-env": "npm:3.734.0"
    "@aws-sdk/credential-provider-http": "npm:3.734.0"
    "@aws-sdk/credential-provider-ini": "npm:3.741.0"
    "@aws-sdk/credential-provider-process": "npm:3.734.0"
    "@aws-sdk/credential-provider-sso": "npm:3.734.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/credential-provider-imds": "npm:^4.0.1"
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/shared-ini-file-loader": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/98a9693f4cfbadedc1b707ad72f6a3f5714becfdf21cdc3ace7a1d43947b2b82117a7ac42102583196b1306ed3071c1d4fa284646e106aeee076bc6c68fab386
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-process@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/credential-provider-process@npm:3.734.0"
  dependencies:
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/shared-ini-file-loader": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/059beffaf6c6d880234c57935356918e3456d85348165ca42028c89e5aff86f6e87a8d4ad11b2d5fc04a22178c86daff3a59ffd02a7fdc2bd2ecf0829de981b1
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-sso@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/credential-provider-sso@npm:3.734.0"
  dependencies:
    "@aws-sdk/client-sso": "npm:3.734.0"
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/token-providers": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/shared-ini-file-loader": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/7a09107ef25574ce1f54261e6827a609d538a5d84c00a29e0381ee090fc372b012d288b8b6a074ec95a9557e098778799fbdd4a1bff105099da064041a0e8d39
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-web-identity@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/credential-provider-web-identity@npm:3.734.0"
  dependencies:
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/nested-clients": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6985306744419084580beb22877ef2fbdea4d341d6e1ef1255513b06370f4cde9d6ffc6b71394375a03687db3d7fef8c486250ff0116bbea2eba89cc513fa675
  languageName: node
  linkType: hard

"@aws-sdk/middleware-bucket-endpoint@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/middleware-bucket-endpoint@npm:3.734.0"
  dependencies:
    "@aws-sdk/types": "npm:3.734.0"
    "@aws-sdk/util-arn-parser": "npm:3.723.0"
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-config-provider": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/f0f98bb478ff469ec3aab0ae5b8122cafc26e4d88efbb1d277429dfd21c70a64eaf996d5cbb7360ff93dcc0e985d75bca5bfcb6a814b1d18ab14c5b912c7c5ad
  languageName: node
  linkType: hard

"@aws-sdk/middleware-expect-continue@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/middleware-expect-continue@npm:3.734.0"
  dependencies:
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/5e6fa03e4b4ef8ff52314a5aea6b7c807e39516ad7c817003c8ef22c4d25de98dc469bab30d6f11a56cba7a968bcdf032373c8c1d074a16ff72ac2cd08f1a5e9
  languageName: node
  linkType: hard

"@aws-sdk/middleware-flexible-checksums@npm:3.735.0":
  version: 3.735.0
  resolution: "@aws-sdk/middleware-flexible-checksums@npm:3.735.0"
  dependencies:
    "@aws-crypto/crc32": "npm:5.2.0"
    "@aws-crypto/crc32c": "npm:5.2.0"
    "@aws-crypto/util": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/is-array-buffer": "npm:^4.0.0"
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-middleware": "npm:^4.0.1"
    "@smithy/util-stream": "npm:^4.0.2"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/b9ca77c97528a99c4264a35803d897ace77b1e422ff3b351b2ea84c9b8adef247874f446a75321dc9caee48f8778fc164579753c363aee1dc30839915625b948
  languageName: node
  linkType: hard

"@aws-sdk/middleware-host-header@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/middleware-host-header@npm:3.734.0"
  dependencies:
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/56e8501c3beda2961ebba56f1146849594edafa0d33ce2bdb04b62df9732d1218ffe89882333d87d76079798dc575af1756db4d7270916d8d83f8d9ef7c4798e
  languageName: node
  linkType: hard

"@aws-sdk/middleware-location-constraint@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/middleware-location-constraint@npm:3.734.0"
  dependencies:
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ec6a10d2545dfbda2806e8dd2244a6be76c97d5fdae2068c461cb61753801ce60079518ad45f3eb559a37042f057636da754cccec751d04d0b94b534d423424e
  languageName: node
  linkType: hard

"@aws-sdk/middleware-logger@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/middleware-logger@npm:3.734.0"
  dependencies:
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/dc690e546d0411929ff5888cd2dad56b7583f160ce4339f24d4963b9d11022f06da76d5f96c56d2ff2624493885254200788c763f113c26695875b8a229ee9a1
  languageName: node
  linkType: hard

"@aws-sdk/middleware-recursion-detection@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/middleware-recursion-detection@npm:3.734.0"
  dependencies:
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/e46e5f99895a4370141b3439c58b94670fddd01d18bbda43a621cb0a5f2bb3384db66757f16da49815af52d29f2cfb8c5d12e273853ad34c919f4f71d078572f
  languageName: node
  linkType: hard

"@aws-sdk/middleware-sdk-s3@npm:3.740.0":
  version: 3.740.0
  resolution: "@aws-sdk/middleware-sdk-s3@npm:3.740.0"
  dependencies:
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@aws-sdk/util-arn-parser": "npm:3.723.0"
    "@smithy/core": "npm:^3.1.1"
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/signature-v4": "npm:^5.0.1"
    "@smithy/smithy-client": "npm:^4.1.2"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.1"
    "@smithy/util-stream": "npm:^4.0.2"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/f9490d9993d7f9f59d46cc080d5c7675075d01490f230cc7281832de56905ae8ca081a8389471337cb2880cf927d595150871c3687d8b40a7ac1dc1c19625bf1
  languageName: node
  linkType: hard

"@aws-sdk/middleware-ssec@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/middleware-ssec@npm:3.734.0"
  dependencies:
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ba1d0f202ef0e58d82895bbe71dcb4520f0eaf958ebc37baa3383e42729091fca2f927ec3482478b0ece35ae001c72da9afb71c83504e0aba6df4074a6a2187a
  languageName: node
  linkType: hard

"@aws-sdk/middleware-user-agent@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/middleware-user-agent@npm:3.734.0"
  dependencies:
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@aws-sdk/util-endpoints": "npm:3.734.0"
    "@smithy/core": "npm:^3.1.1"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/aecda461346fc272d440ee9557588bb7379020ee5ffead61ca1e905f1ccdcd009d6aee53b364a6f9278f2a092608ca86c0650f02fb14f28f2ba99a34dd4af136
  languageName: node
  linkType: hard

"@aws-sdk/nested-clients@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/nested-clients@npm:3.734.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.734.0"
    "@aws-sdk/middleware-host-header": "npm:3.734.0"
    "@aws-sdk/middleware-logger": "npm:3.734.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.734.0"
    "@aws-sdk/middleware-user-agent": "npm:3.734.0"
    "@aws-sdk/region-config-resolver": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@aws-sdk/util-endpoints": "npm:3.734.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.734.0"
    "@aws-sdk/util-user-agent-node": "npm:3.734.0"
    "@smithy/config-resolver": "npm:^4.0.1"
    "@smithy/core": "npm:^3.1.1"
    "@smithy/fetch-http-handler": "npm:^5.0.1"
    "@smithy/hash-node": "npm:^4.0.1"
    "@smithy/invalid-dependency": "npm:^4.0.1"
    "@smithy/middleware-content-length": "npm:^4.0.1"
    "@smithy/middleware-endpoint": "npm:^4.0.2"
    "@smithy/middleware-retry": "npm:^4.0.3"
    "@smithy/middleware-serde": "npm:^4.0.1"
    "@smithy/middleware-stack": "npm:^4.0.1"
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/node-http-handler": "npm:^4.0.2"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/smithy-client": "npm:^4.1.2"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/url-parser": "npm:^4.0.1"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.3"
    "@smithy/util-defaults-mode-node": "npm:^4.0.3"
    "@smithy/util-endpoints": "npm:^3.0.1"
    "@smithy/util-middleware": "npm:^4.0.1"
    "@smithy/util-retry": "npm:^4.0.1"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/55877c3f8cac486183c2cfad34a650a4459c85d07ae08c804e9e64ad731d7607cd783156cf5646736c7026f44d3c4e76335bb42cc37fcf91cc98195b273fbd84
  languageName: node
  linkType: hard

"@aws-sdk/region-config-resolver@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/region-config-resolver@npm:3.734.0"
  dependencies:
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/c1e026dcbe9d7529ec5efee979a868d0c868287d68e7e219bd730d887ab1ccf17ef48516477e57325fef55543217496bcfe7ba6d17d9ecad98cf8cf18d5ced63
  languageName: node
  linkType: hard

"@aws-sdk/signature-v4-multi-region@npm:3.740.0":
  version: 3.740.0
  resolution: "@aws-sdk/signature-v4-multi-region@npm:3.740.0"
  dependencies:
    "@aws-sdk/middleware-sdk-s3": "npm:3.740.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/signature-v4": "npm:^5.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/5165dee36e595f2fd471538d65c01101a25e5b4b6c28289be3d003f9f175f19addfb5faf7caed56ccdd47483e4a3fcd730af21ce07db7cb72f67874571ee72a9
  languageName: node
  linkType: hard

"@aws-sdk/token-providers@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/token-providers@npm:3.734.0"
  dependencies:
    "@aws-sdk/nested-clients": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/shared-ini-file-loader": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/65a3696a930229d54a90e971158f399f3760200dfe080d1a4abc0cc6ceb130968036a9f2809be58ed0d35cd82357d32adfdbc391f3ed2ed89c4e0dcd114cb0de
  languageName: node
  linkType: hard

"@aws-sdk/types@npm:3.734.0, @aws-sdk/types@npm:^3.222.0":
  version: 3.734.0
  resolution: "@aws-sdk/types@npm:3.734.0"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/74313849619b8bce9e6a52c70fcdaa212574a443503c78bccdba77cdc7bc66b8cecefe461852e0bab7376cc2ec3e1891730b1a027be63efb47394115c8ddb856
  languageName: node
  linkType: hard

"@aws-sdk/util-arn-parser@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/util-arn-parser@npm:3.723.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/5d2adfded61acaf222ed21bf8e5a8b067fe469dfaab03a6b69c591a090c48d309b1f3c4fd64826f71ef9883390adb77a9bf884667b242615f221236bc5a8b326
  languageName: node
  linkType: hard

"@aws-sdk/util-endpoints@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/util-endpoints@npm:3.734.0"
  dependencies:
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-endpoints": "npm:^3.0.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/655d51da2fc57679be0e7c243cf2876f802c3d10df431cd56c00ec19de584d073c3838f2b917fb4b4d8c4e7d61a49af69c1b7135b8371619ae2339a793117005
  languageName: node
  linkType: hard

"@aws-sdk/util-locate-window@npm:^3.0.0":
  version: 3.723.0
  resolution: "@aws-sdk/util-locate-window@npm:3.723.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/c9c75d3ee06bd1d1edad78bea8324f2d4ad6086803f27731e1f3c25e946bb630c8db2991a5337e4dbeee06507deab9abea80b134ba4e3fbb27471d438a030639
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-browser@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/util-user-agent-browser@npm:3.734.0"
  dependencies:
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/types": "npm:^4.1.0"
    bowser: "npm:^2.11.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/7fc8c5e29f3219f8abf1d0cff73dd6bb34f32a235473843e50f61375b1c05f4c49269cd956c9e4623c87c025e1eeef9fc699ae3389665459721bc11e00c25ead
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-node@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/util-user-agent-node@npm:3.734.0"
  dependencies:
    "@aws-sdk/middleware-user-agent": "npm:3.734.0"
    "@aws-sdk/types": "npm:3.734.0"
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  peerDependencies:
    aws-crt: ">=1.0.0"
  peerDependenciesMeta:
    aws-crt:
      optional: true
  checksum: 10c0/bae227776ede8d0c85193e257ac6e69b07f1ba94481544036fcdbdd633069fd7ebc19a0141c1e168ef58fc2c267da15a511e498552902ca15eac1a5240841f6e
  languageName: node
  linkType: hard

"@aws-sdk/xml-builder@npm:3.734.0":
  version: 3.734.0
  resolution: "@aws-sdk/xml-builder@npm:3.734.0"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/77eb3d603d45a235982a86e5adbc2de727389924cbbd8edb9b13f1a201b15304c57aebb18e00cce909920b3519d0ca71406989b01b6544c87c7b3c4f04d66887
  languageName: node
  linkType: hard

"@azure/abort-controller@npm:^2.0.0":
  version: 2.1.2
  resolution: "@azure/abort-controller@npm:2.1.2"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/3771b6820e33ebb56e79c7c68e2288296b8c2529556fbd29cf4cf2fbff7776e7ce1120072972d8df9f1bf50e2c3224d71a7565362b589595563f710b8c3d7b79
  languageName: node
  linkType: hard

"@azure/core-auth@npm:^1.4.0, @azure/core-auth@npm:^1.8.0, @azure/core-auth@npm:^1.9.0":
  version: 1.9.0
  resolution: "@azure/core-auth@npm:1.9.0"
  dependencies:
    "@azure/abort-controller": "npm:^2.0.0"
    "@azure/core-util": "npm:^1.11.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/b7d8f33b81a8c9a76531acacc7af63d888429f0d763bb1ab8e28e91ddbf1626fc19cf8ca74f79c39b0a3e5acb315bdc4c4276fb979816f315712ea1bd611273d
  languageName: node
  linkType: hard

"@azure/core-client@npm:^1.9.2":
  version: 1.9.4
  resolution: "@azure/core-client@npm:1.9.4"
  dependencies:
    "@azure/abort-controller": "npm:^2.0.0"
    "@azure/core-auth": "npm:^1.4.0"
    "@azure/core-rest-pipeline": "npm:^1.20.0"
    "@azure/core-tracing": "npm:^1.0.0"
    "@azure/core-util": "npm:^1.6.1"
    "@azure/logger": "npm:^1.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/c38c494c0bf085a89720d97c5bfc098cd1a2bbc5b9c41f8c32ecf2f3b81b476af1afe2f0d996d7e23900581415306e59280d507410bf0aa80804e0e411b8a2be
  languageName: node
  linkType: hard

"@azure/core-rest-pipeline@npm:^1.17.0, @azure/core-rest-pipeline@npm:^1.20.0":
  version: 1.20.0
  resolution: "@azure/core-rest-pipeline@npm:1.20.0"
  dependencies:
    "@azure/abort-controller": "npm:^2.0.0"
    "@azure/core-auth": "npm:^1.8.0"
    "@azure/core-tracing": "npm:^1.0.1"
    "@azure/core-util": "npm:^1.11.0"
    "@azure/logger": "npm:^1.0.0"
    "@typespec/ts-http-runtime": "npm:^0.2.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d82094805fad3ef7b5c2646c21e3fc62ff9534806bfc9840a9439244800cdb3ea7e539f40f5077e7c4db2656f882d8a56ea736e81167ee39dd9f426919fe72f2
  languageName: node
  linkType: hard

"@azure/core-tracing@npm:^1.0.0, @azure/core-tracing@npm:^1.0.1":
  version: 1.2.0
  resolution: "@azure/core-tracing@npm:1.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/7cd114b3c11730a1b8b71d89b64f9d033dfe0710f2364ef65645683381e2701173c08ff8625a0b0bc65bb3c3e0de46c80fdb2735e37652425489b65a283f043d
  languageName: node
  linkType: hard

"@azure/core-util@npm:^1.11.0, @azure/core-util@npm:^1.6.1":
  version: 1.12.0
  resolution: "@azure/core-util@npm:1.12.0"
  dependencies:
    "@azure/abort-controller": "npm:^2.0.0"
    "@typespec/ts-http-runtime": "npm:^0.2.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/9335e619078781a14c616840125deaaaef89198d9c99e6c9cd4452e9b18d4f95823246da4b8277ba186647e05ebcd88b419372616d1acad020c0172340ae02e5
  languageName: node
  linkType: hard

"@azure/identity@npm:^4.10.0":
  version: 4.10.0
  resolution: "@azure/identity@npm:4.10.0"
  dependencies:
    "@azure/abort-controller": "npm:^2.0.0"
    "@azure/core-auth": "npm:^1.9.0"
    "@azure/core-client": "npm:^1.9.2"
    "@azure/core-rest-pipeline": "npm:^1.17.0"
    "@azure/core-tracing": "npm:^1.0.0"
    "@azure/core-util": "npm:^1.11.0"
    "@azure/logger": "npm:^1.0.0"
    "@azure/msal-browser": "npm:^4.2.0"
    "@azure/msal-node": "npm:^3.5.0"
    open: "npm:^10.1.0"
    tslib: "npm:^2.2.0"
  checksum: 10c0/2aac47ad7e27d31cc5dc25b011fb5d5000093ee6db35237feb072bbec1167fcc0bd82e99116a10f77ccfe119a208f15711d61018e619b6d3e33c61b9199984f4
  languageName: node
  linkType: hard

"@azure/logger@npm:^1.0.0":
  version: 1.2.0
  resolution: "@azure/logger@npm:1.2.0"
  dependencies:
    "@typespec/ts-http-runtime": "npm:^0.2.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/a04693423143204d25e82bde1c57ddb53456fd00be0dbd91d1b078b0e17a1d564ababfd92467d9e51e201a0a756c6611918d7207b17bc4feb417885b80770ecd
  languageName: node
  linkType: hard

"@azure/msal-browser@npm:^4.2.0":
  version: 4.12.0
  resolution: "@azure/msal-browser@npm:4.12.0"
  dependencies:
    "@azure/msal-common": "npm:15.6.0"
  checksum: 10c0/2aed32908eed923fccbe4947e5b7d450f6d3c25b4dd465af8d27a71151b3e610e8470777b3c53eace39981deffbf27e005296e8571b54217e3a774912b626529
  languageName: node
  linkType: hard

"@azure/msal-common@npm:15.6.0":
  version: 15.6.0
  resolution: "@azure/msal-common@npm:15.6.0"
  checksum: 10c0/2947f64b3ee5eb064c8e35ca4f4bbdd0c38c95d043ab20953acbbbb0b94e47186e88156680accfe7009557b556dae66667a1e71abff66401ab184fc0ea17c6d9
  languageName: node
  linkType: hard

"@azure/msal-node@npm:^3.5.0":
  version: 3.5.3
  resolution: "@azure/msal-node@npm:3.5.3"
  dependencies:
    "@azure/msal-common": "npm:15.6.0"
    jsonwebtoken: "npm:^9.0.0"
    uuid: "npm:^8.3.0"
  checksum: 10c0/65c0fc2d4b2601fb389fef8a00e6c26f2a156892eb498df703d57850bdac4cf754f8373ca7720d27ca95176cfa0015b55b5cf7440f942495b7517267209c0103
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.12.13, @babel/code-frame@npm:^7.16.7, @babel/code-frame@npm:^7.25.9, @babel/code-frame@npm:^7.26.2":
  version: 7.26.2
  resolution: "@babel/code-frame@npm:7.26.2"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.25.9"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.0.0"
  checksum: 10c0/7d79621a6849183c415486af99b1a20b84737e8c11cd55b6544f688c51ce1fd710e6d869c3dd21232023da272a79b91efb3e83b5bc2dc65c1187c5fcd1b72ea8
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.26.5":
  version: 7.26.5
  resolution: "@babel/compat-data@npm:7.26.5"
  checksum: 10c0/9d2b41f0948c3dfc5de44d9f789d2208c2ea1fd7eb896dfbb297fe955e696728d6f363c600cd211e7f58ccbc2d834fe516bb1e4cf883bbabed8a32b038afc1a0
  languageName: node
  linkType: hard

"@babel/core@npm:^7.11.6, @babel/core@npm:^7.12.3, @babel/core@npm:^7.23.9":
  version: 7.26.7
  resolution: "@babel/core@npm:7.26.7"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.26.2"
    "@babel/generator": "npm:^7.26.5"
    "@babel/helper-compilation-targets": "npm:^7.26.5"
    "@babel/helper-module-transforms": "npm:^7.26.0"
    "@babel/helpers": "npm:^7.26.7"
    "@babel/parser": "npm:^7.26.7"
    "@babel/template": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.26.7"
    "@babel/types": "npm:^7.26.7"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/fbd2cd9fc23280bdcaca556e558f715c0a42d940b9913c52582e8e3d24e391d269cb8a9cd6589172593983569021c379e28bba6b19ea2ee08674f6068c210a9d
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.26.5, @babel/generator@npm:^7.7.2":
  version: 7.26.5
  resolution: "@babel/generator@npm:7.26.5"
  dependencies:
    "@babel/parser": "npm:^7.26.5"
    "@babel/types": "npm:^7.26.5"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/3be79e0aa03f38858a465d12ee2e468320b9122dc44fc85984713e32f16f4d77ce34a16a1a9505972782590e0b8d847b6f373621f9c6fafa1906d90f31416cb0
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.26.5":
  version: 7.26.5
  resolution: "@babel/helper-compilation-targets@npm:7.26.5"
  dependencies:
    "@babel/compat-data": "npm:^7.26.5"
    "@babel/helper-validator-option": "npm:^7.25.9"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/9da5c77e5722f1a2fcb3e893049a01d414124522bbf51323bb1a0c9dcd326f15279836450fc36f83c9e8a846f3c40e88be032ed939c5a9840922bed6073edfb4
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-module-imports@npm:7.25.9"
  dependencies:
    "@babel/traverse": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  checksum: 10c0/078d3c2b45d1f97ffe6bb47f61961be4785d2342a4156d8b42c92ee4e1b7b9e365655dd6cb25329e8fe1a675c91eeac7e3d04f0c518b67e417e29d6e27b6aa70
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/helper-module-transforms@npm:7.26.0"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.25.9"
    "@babel/helper-validator-identifier": "npm:^7.25.9"
    "@babel/traverse": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/ee111b68a5933481d76633dad9cdab30c41df4479f0e5e1cc4756dc9447c1afd2c9473b5ba006362e35b17f4ebddd5fca090233bef8dfc84dca9d9127e56ec3a
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.25.9, @babel/helper-plugin-utils@npm:^7.8.0":
  version: 7.26.5
  resolution: "@babel/helper-plugin-utils@npm:7.26.5"
  checksum: 10c0/cdaba71d4b891aa6a8dfbe5bac2f94effb13e5fa4c2c487667fdbaa04eae059b78b28d85a885071f45f7205aeb56d16759e1bed9c118b94b16e4720ef1ab0f65
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-string-parser@npm:7.25.9"
  checksum: 10c0/7244b45d8e65f6b4338a6a68a8556f2cb161b782343e97281a5f2b9b93e420cad0d9f5773a59d79f61d0c448913d06f6a2358a87f2e203cf112e3c5b53522ee6
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-identifier@npm:7.25.9"
  checksum: 10c0/4fc6f830177b7b7e887ad3277ddb3b91d81e6c4a24151540d9d1023e8dc6b1c0505f0f0628ae653601eb4388a8db45c1c14b2c07a9173837aef7e4116456259d
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-option@npm:7.25.9"
  checksum: 10c0/27fb195d14c7dcb07f14e58fe77c44eea19a6a40a74472ec05c441478fa0bb49fa1c32b2d64be7a38870ee48ef6601bdebe98d512f0253aea0b39756c4014f3e
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.26.7":
  version: 7.26.7
  resolution: "@babel/helpers@npm:7.26.7"
  dependencies:
    "@babel/template": "npm:^7.25.9"
    "@babel/types": "npm:^7.26.7"
  checksum: 10c0/37fec398e53a2dbbf24bc2a025c4d571b2556cef18d8116d05d04b153f13ef659cdfbaab96c8eed875e629d39bdf9b3ea5d099ccf80544537de224e2d94f9b11
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.14.7, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.23.9, @babel/parser@npm:^7.25.9, @babel/parser@npm:^7.26.5, @babel/parser@npm:^7.26.7":
  version: 7.26.7
  resolution: "@babel/parser@npm:7.26.7"
  dependencies:
    "@babel/types": "npm:^7.26.7"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/dcb08a4f2878ece33caffefe43b71488d753324bae7ca58d64bca3bc4af34dcfa1b58abdf9972516d76af760fceb25bb9294ca33461d56b31c5059ccfe32001f
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d13efb282838481348c71073b6be6245b35d4f2f964a8f71e4174f235009f929ef7613df25f8d2338e2d3e44bc4265a9f8638c6aaa136d7a61fe95985f9725c8
  languageName: node
  linkType: hard

"@babel/plugin-syntax-bigint@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-bigint@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/686891b81af2bc74c39013655da368a480f17dd237bf9fbc32048e5865cb706d5a8f65438030da535b332b1d6b22feba336da8fa931f663b6b34e13147d12dde
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.12.13"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/95168fa186416195280b1264fb18afcdcdcea780b3515537b766cb90de6ce042d42dd6a204a39002f794ae5845b02afb0fd4861a3308a861204a55e68310a120
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4464bf9115f4a2d02ce1454411baf9cfb665af1da53709c5c56953e5e2913745b0fcce82982a00463d6facbdd93445c691024e310b91431a1e2f024b158f6371
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.24.7":
  version: 7.26.0
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.26.0"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e594c185b12bfe0bbe7ca78dfeebe870e6d569a12128cac86f3164a075fe0ff70e25ddbd97fd0782906b91f65560c9dc6957716b7b4a68aba2516c9b7455e352
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/0b08b5e4c3128523d8e346f8cfc86824f0da2697b1be12d71af50a31aff7a56ceb873ed28779121051475010c28d6146a6bfea8518b150b71eeb4e46190172ee
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e98f31b2ec406c57757d115aac81d0336e8434101c224edd9a5c93cefa53faf63eacc69f3138960c8b25401315af03df37f68d316c151c4b933136716ed6906e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.7.2":
  version: 7.25.9
  resolution: "@babel/plugin-syntax-jsx@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d56597aff4df39d3decda50193b6dfbe596ca53f437ff2934622ce19a743bf7f43492d3fb3308b0289f5cee2b825d99ceb56526a2b9e7b68bf04901546c5618c
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2594cfbe29411ad5bc2ad4058de7b2f6a8c5b86eda525a993959438615479e59c012c14aec979e538d60a584a1a799b60d1b8942c3b18468cb9d99b8fd34cd0b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2024fbb1162899094cfc81152449b12bd0cc7053c6d4bda8ac2852545c87d0a851b1b72ed9560673cbf3ef6248257262c3c04aabf73117215c1b9cc7dd2542ce
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c55a82b3113480942c6aa2fcbe976ff9caa74b7b1109ff4369641dfbc88d1da348aceb3c31b6ed311c84d1e7c479440b961906c735d0ab494f688bf2fd5b9bb9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/ee1eab52ea6437e3101a0a7018b0da698545230015fc8ab129d292980ec6dff94d265e9e90070e8ae5fed42f08f1622c14c94552c77bcac784b37f503a82ff26
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/27e2493ab67a8ea6d693af1287f7e9acec206d1213ff107a928e85e173741e1d594196f99fec50e9dde404b09164f39dec5864c767212154ffe1caa6af0bc5af
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/46edddf2faa6ebf94147b8e8540dfc60a5ab718e2de4d01b2c0bdf250a4d642c2bd47cbcbb739febcb2bf75514dbcefad3c52208787994b8d0f8822490f55e81
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/69822772561706c87f0a65bc92d0772cea74d6bc0911537904a676d5ff496a6d3ac4e05a166d8125fce4a16605bace141afc3611074e170a994e66e5397787f3
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/14bf6e65d5bc1231ffa9def5f0ef30b19b51c218fcecaa78cd1bdf7939dfdf23f90336080b7f5196916368e399934ce5d581492d8292b46a2fb569d8b2da106f
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.7.2":
  version: 7.25.9
  resolution: "@babel/plugin-syntax-typescript@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.25.9"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5192ebe11bd46aea68b7a60fd9555465c59af7e279e71126788e59121b86e00b505816685ab4782abe159232b0f73854e804b54449820b0d950b397ee158caa2
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.21.0":
  version: 7.26.7
  resolution: "@babel/runtime@npm:7.26.7"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 10c0/60199c049f90e5e41c687687430052a370aca60bac7859ff4ee761c5c1739b8ba1604d391d01588c22dc0e93828cbadb8ada742578ad1b1df240746bce98729a
  languageName: node
  linkType: hard

"@babel/template@npm:^7.25.9, @babel/template@npm:^7.3.3":
  version: 7.25.9
  resolution: "@babel/template@npm:7.25.9"
  dependencies:
    "@babel/code-frame": "npm:^7.25.9"
    "@babel/parser": "npm:^7.25.9"
    "@babel/types": "npm:^7.25.9"
  checksum: 10c0/ebe677273f96a36c92cc15b7aa7b11cc8bc8a3bb7a01d55b2125baca8f19cae94ff3ce15f1b1880fb8437f3a690d9f89d4e91f16fc1dc4d3eb66226d128983ab
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.25.9, @babel/traverse@npm:^7.26.7":
  version: 7.26.7
  resolution: "@babel/traverse@npm:7.26.7"
  dependencies:
    "@babel/code-frame": "npm:^7.26.2"
    "@babel/generator": "npm:^7.26.5"
    "@babel/parser": "npm:^7.26.7"
    "@babel/template": "npm:^7.25.9"
    "@babel/types": "npm:^7.26.7"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10c0/b23a36ce40d2e4970741431c45d4f92e3f4c2895c0a421456516b2729bd9e17278846e01ee3d9039b0adf5fc5a071768061c17fcad040e74a5c3e39517449d5b
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.25.9, @babel/types@npm:^7.26.5, @babel/types@npm:^7.26.7, @babel/types@npm:^7.3.3":
  version: 7.26.7
  resolution: "@babel/types@npm:7.26.7"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.25.9"
    "@babel/helper-validator-identifier": "npm:^7.25.9"
  checksum: 10c0/7810a2bca97b13c253f07a0863a628d33dbe76ee3c163367f24be93bfaf4c8c0a325f73208abaaa050a6b36059efc2950c2e4b71fb109c0f07fa62221d8473d4
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^0.2.3":
  version: 0.2.3
  resolution: "@bcoe/v8-coverage@npm:0.2.3"
  checksum: 10c0/6b80ae4cb3db53f486da2dc63b6e190a74c8c3cca16bb2733f234a0b6a9382b09b146488ae08e2b22cf00f6c83e20f3e040a2f7894f05c045c946d6a090b1d52
  languageName: node
  linkType: hard

"@colors/colors@npm:1.5.0":
  version: 1.5.0
  resolution: "@colors/colors@npm:1.5.0"
  checksum: 10c0/eb42729851adca56d19a08e48d5a1e95efd2a32c55ae0323de8119052be0510d4b7a1611f2abcbf28c044a6c11e6b7d38f99fccdad7429300c37a8ea5fb95b44
  languageName: node
  linkType: hard

"@cspotcode/source-map-support@npm:^0.8.0":
  version: 0.8.1
  resolution: "@cspotcode/source-map-support@npm:0.8.1"
  dependencies:
    "@jridgewell/trace-mapping": "npm:0.3.9"
  checksum: 10c0/05c5368c13b662ee4c122c7bfbe5dc0b613416672a829f3e78bc49a357a197e0218d6e74e7c66cfcd04e15a179acab080bd3c69658c9fbefd0e1ccd950a07fc6
  languageName: node
  linkType: hard

"@csstools/color-helpers@npm:^5.0.2":
  version: 5.0.2
  resolution: "@csstools/color-helpers@npm:5.0.2"
  checksum: 10c0/bebaddb28b9eb58b0449edd5d0c0318fa88f3cb079602ee27e88c9118070d666dcc4e09a5aa936aba2fde6ba419922ade07b7b506af97dd7051abd08dfb2959b
  languageName: node
  linkType: hard

"@csstools/css-calc@npm:^2.1.3":
  version: 2.1.3
  resolution: "@csstools/css-calc@npm:2.1.3"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.4
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10c0/85f5b4f96d60f395d5f0108056b0ddee037b22d6deba448d74324b50f1c554de284f84715ebfac7b2888b78e09d20d02a7cd213ee7bdaa71011ea9b4eee3a251
  languageName: node
  linkType: hard

"@csstools/css-color-parser@npm:^3.0.9":
  version: 3.0.9
  resolution: "@csstools/css-color-parser@npm:3.0.9"
  dependencies:
    "@csstools/color-helpers": "npm:^5.0.2"
    "@csstools/css-calc": "npm:^2.1.3"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^3.0.4
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10c0/acc026a6bd6d8c4c641fa5f9b4d77cd5dfa54c57c3278ae52329d96b5837723428dcb93c34db4062bbea2f45a98451119df06eaf39fd196aaf6368c59d799f20
  languageName: node
  linkType: hard

"@csstools/css-parser-algorithms@npm:^3.0.4":
  version: 3.0.4
  resolution: "@csstools/css-parser-algorithms@npm:3.0.4"
  peerDependencies:
    "@csstools/css-tokenizer": ^3.0.3
  checksum: 10c0/d411f07765e14eede17bccc6bd4f90ff303694df09aabfede3fd104b2dfacfd4fe3697cd25ddad14684c850328f3f9420ebfa9f78380892492974db24ae47dbd
  languageName: node
  linkType: hard

"@csstools/css-tokenizer@npm:^3.0.3":
  version: 3.0.3
  resolution: "@csstools/css-tokenizer@npm:3.0.3"
  checksum: 10c0/c31bf410e1244b942e71798e37c54639d040cb59e0121b21712b40015fced2b0fb1ffe588434c5f8923c9cd0017cfc1c1c8f3921abc94c96edf471aac2eba5e5
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.2.0":
  version: 1.3.1
  resolution: "@emnapi/runtime@npm:1.3.1"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/060ffede50f1b619c15083312b80a9e62a5b0c87aa8c1b54854c49766c9d69f8d1d3d87bd963a647071263a320db41b25eaa50b74d6a80dcc763c23dbeaafd6c
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0":
  version: 4.4.1
  resolution: "@eslint-community/eslint-utils@npm:4.4.1"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10c0/2aa0ac2fc50ff3f234408b10900ed4f1a0b19352f21346ad4cc3d83a1271481bdda11097baa45d484dd564c895e0762a27a8240be7a256b3ad47129e96528252
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.4.0, @eslint-community/regexpp@npm:^4.6.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10c0/a03d98c246bcb9109aec2c08e4d10c8d010256538dcb3f56610191607214523d4fb1b00aa81df830b6dffb74c5fa0be03642513a289c567949d3e550ca11cdf6
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^2.1.4":
  version: 2.1.4
  resolution: "@eslint/eslintrc@npm:2.1.4"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^9.6.0"
    globals: "npm:^13.19.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10c0/32f67052b81768ae876c84569ffd562491ec5a5091b0c1e1ca1e0f3c24fb42f804952fdd0a137873bc64303ba368a71ba079a6f691cee25beee9722d94cc8573
  languageName: node
  linkType: hard

"@eslint/js@npm:8.57.1":
  version: 8.57.1
  resolution: "@eslint/js@npm:8.57.1"
  checksum: 10c0/b489c474a3b5b54381c62e82b3f7f65f4b8a5eaaed126546520bf2fede5532a8ed53212919fed1e9048dcf7f37167c8561d58d0ba4492a4244004e7793805223
  languageName: node
  linkType: hard

"@foliojs-fork/fontkit@npm:^1.9.2":
  version: 1.9.2
  resolution: "@foliojs-fork/fontkit@npm:1.9.2"
  dependencies:
    "@foliojs-fork/restructure": "npm:^2.0.2"
    brotli: "npm:^1.2.0"
    clone: "npm:^1.0.4"
    deep-equal: "npm:^1.0.0"
    dfa: "npm:^1.2.0"
    tiny-inflate: "npm:^1.0.2"
    unicode-properties: "npm:^1.2.2"
    unicode-trie: "npm:^2.0.0"
  checksum: 10c0/0855b621942aeaec3a20261154532b3a4653cc530e5429954b9ec2bd61805a484a450a43229d0e836e78d08674ac729d81193ac03347b0e202646d900446ce84
  languageName: node
  linkType: hard

"@foliojs-fork/linebreak@npm:^1.1.1, @foliojs-fork/linebreak@npm:^1.1.2":
  version: 1.1.2
  resolution: "@foliojs-fork/linebreak@npm:1.1.2"
  dependencies:
    base64-js: "npm:1.3.1"
    unicode-trie: "npm:^2.0.0"
  checksum: 10c0/5791eab874ae120bbe7bbd5a70675d3f88869376dfba3c76c61487b42275eeaf07026de22f074c5d4b5fdde900b43cac1289c6c142aca6015ccdb0da1166c3e8
  languageName: node
  linkType: hard

"@foliojs-fork/pdfkit@npm:^0.15.3":
  version: 0.15.3
  resolution: "@foliojs-fork/pdfkit@npm:0.15.3"
  dependencies:
    "@foliojs-fork/fontkit": "npm:^1.9.2"
    "@foliojs-fork/linebreak": "npm:^1.1.1"
    crypto-js: "npm:^4.2.0"
    jpeg-exif: "npm:^1.1.4"
    png-js: "npm:^1.0.0"
  checksum: 10c0/5f3edff0182a6e29d12891e1573be6837035de8d4a4aa446ebf5ee561224b6f11b059cda05dd82a91bbe3de2cedc9676c59980506d5daf209a5d564865a297cb
  languageName: node
  linkType: hard

"@foliojs-fork/restructure@npm:^2.0.2":
  version: 2.0.2
  resolution: "@foliojs-fork/restructure@npm:2.0.2"
  checksum: 10c0/f9e6e94f7377f467a93988ee85cf326f2db3edd3029530791aced6d530fd7862efb27b00212beb8df6f7458cbec4acb6b198b36535d1acee3fb413c1d5ac55ac
  languageName: node
  linkType: hard

"@googlemaps/google-maps-services-js@npm:^3.4.0":
  version: 3.4.0
  resolution: "@googlemaps/google-maps-services-js@npm:3.4.0"
  dependencies:
    "@googlemaps/url-signature": "npm:^1.0.4"
    agentkeepalive: "npm:^4.1.0"
    axios: "npm:^1.5.1"
    query-string: "npm:<8.x"
    retry-axios: "npm:<3.x"
  checksum: 10c0/41fb7cef275db73dc5f10588b5657d8899cfc95ecf1edd3db21cd084a23cb8dc10e36d29d966da4e6af549b8afe2767b5ff027ff7d0ce8f7976ce2bc3b1bf2e9
  languageName: node
  linkType: hard

"@googlemaps/url-signature@npm:^1.0.4":
  version: 1.0.40
  resolution: "@googlemaps/url-signature@npm:1.0.40"
  dependencies:
    crypto-js: "npm:^4.2.0"
  checksum: 10c0/3306644b8a9f1a6305d43390514b5a14dfbc280067067c7ef9228961463fd2a469a8e7b1516551d2ecb3ac7f85bd9b00e5cac56ec5e3bd2d797cc6d0f533516b
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.13.0":
  version: 0.13.0
  resolution: "@humanwhocodes/config-array@npm:0.13.0"
  dependencies:
    "@humanwhocodes/object-schema": "npm:^2.0.3"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.0.5"
  checksum: 10c0/205c99e756b759f92e1f44a3dc6292b37db199beacba8f26c2165d4051fe73a4ae52fdcfd08ffa93e7e5cb63da7c88648f0e84e197d154bbbbe137b2e0dd332e
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10c0/909b69c3b86d482c26b3359db16e46a32e0fb30bd306a3c176b8313b9e7313dba0f37f519de6aa8b0a1921349e505f259d19475e123182416a506d7f87e7f529
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^2.0.3":
  version: 2.0.3
  resolution: "@humanwhocodes/object-schema@npm:2.0.3"
  checksum: 10c0/80520eabbfc2d32fe195a93557cef50dfe8c8905de447f022675aaf66abc33ae54098f5ea78548d925aa671cd4ab7c7daa5ad704fe42358c9b5e7db60f80696c
  languageName: node
  linkType: hard

"@img/sharp-darwin-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-darwin-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-darwin-arm64": "npm:1.0.4"
  dependenciesMeta:
    "@img/sharp-libvips-darwin-arm64":
      optional: true
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-darwin-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-darwin-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-darwin-x64": "npm:1.0.4"
  dependenciesMeta:
    "@img/sharp-libvips-darwin-x64":
      optional: true
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-darwin-arm64@npm:1.0.4"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-darwin-x64@npm:1.0.4"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-arm64@npm:1.0.4"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm@npm:1.0.5":
  version: 1.0.5
  resolution: "@img/sharp-libvips-linux-arm@npm:1.0.5"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-s390x@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-s390x@npm:1.0.4"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-x64@npm:1.0.4"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linuxmusl-arm64@npm:1.0.4"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linuxmusl-x64@npm:1.0.4"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linux-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-arm64": "npm:1.0.4"
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-arm@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-arm@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-arm": "npm:1.0.5"
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm":
      optional: true
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-s390x@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-s390x@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-s390x": "npm:1.0.4"
  dependenciesMeta:
    "@img/sharp-libvips-linux-s390x":
      optional: true
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-x64": "npm:1.0.4"
  dependenciesMeta:
    "@img/sharp-libvips-linux-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linuxmusl-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linuxmusl-arm64": "npm:1.0.4"
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linuxmusl-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linuxmusl-x64": "npm:1.0.4"
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-wasm32@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-wasm32@npm:0.33.5"
  dependencies:
    "@emnapi/runtime": "npm:^1.2.0"
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@img/sharp-win32-ia32@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-win32-ia32@npm:0.33.5"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@img/sharp-win32-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-win32-x64@npm:0.33.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@istanbuljs/load-nyc-config@npm:^1.0.0":
  version: 1.1.0
  resolution: "@istanbuljs/load-nyc-config@npm:1.1.0"
  dependencies:
    camelcase: "npm:^5.3.1"
    find-up: "npm:^4.1.0"
    get-package-type: "npm:^0.1.0"
    js-yaml: "npm:^3.13.1"
    resolve-from: "npm:^5.0.0"
  checksum: 10c0/dd2a8b094887da5a1a2339543a4933d06db2e63cbbc2e288eb6431bd832065df0c099d091b6a67436e71b7d6bf85f01ce7c15f9253b4cbebcc3b9a496165ba42
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2, @istanbuljs/schema@npm:^0.1.3":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 10c0/61c5286771676c9ca3eb2bd8a7310a9c063fb6e0e9712225c8471c582d157392c88f5353581c8c9adbe0dff98892317d2fdfc56c3499aa42e0194405206a963a
  languageName: node
  linkType: hard

"@jest/console@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/console@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    slash: "npm:^3.0.0"
  checksum: 10c0/7be408781d0a6f657e969cbec13b540c329671819c2f57acfad0dae9dbfe2c9be859f38fe99b35dba9ff1536937dc6ddc69fdcd2794812fa3c647a1619797f6c
  languageName: node
  linkType: hard

"@jest/core@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/core@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/reporters": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    ansi-escapes: "npm:^4.2.1"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    exit: "npm:^0.1.2"
    graceful-fs: "npm:^4.2.9"
    jest-changed-files: "npm:^29.7.0"
    jest-config: "npm:^29.7.0"
    jest-haste-map: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-resolve-dependencies: "npm:^29.7.0"
    jest-runner: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    jest-watcher: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.0"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 10c0/934f7bf73190f029ac0f96662c85cd276ec460d407baf6b0dbaec2872e157db4d55a7ee0b1c43b18874602f662b37cb973dda469a4e6d88b4e4845b521adeeb2
  languageName: node
  linkType: hard

"@jest/environment@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/environment@npm:29.7.0"
  dependencies:
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
  checksum: 10c0/c7b1b40c618f8baf4d00609022d2afa086d9c6acc706f303a70bb4b67275868f620ad2e1a9efc5edd418906157337cce50589a627a6400bbdf117d351b91ef86
  languageName: node
  linkType: hard

"@jest/expect-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect-utils@npm:29.7.0"
  dependencies:
    jest-get-type: "npm:^29.6.3"
  checksum: 10c0/60b79d23a5358dc50d9510d726443316253ecda3a7fb8072e1526b3e0d3b14f066ee112db95699b7a43ad3f0b61b750c72e28a5a1cac361d7a2bb34747fa938a
  languageName: node
  linkType: hard

"@jest/expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect@npm:29.7.0"
  dependencies:
    expect: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
  checksum: 10c0/b41f193fb697d3ced134349250aed6ccea075e48c4f803159db102b826a4e473397c68c31118259868fd69a5cba70e97e1c26d2c2ff716ca39dc73a2ccec037e
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/fake-timers@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@sinonjs/fake-timers": "npm:^10.0.2"
    "@types/node": "npm:*"
    jest-message-util: "npm:^29.7.0"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/cf0a8bcda801b28dc2e2b2ba36302200ee8104a45ad7a21e6c234148932f826cb3bc57c8df3b7b815aeea0861d7b6ca6f0d4778f93b9219398ef28749e03595c
  languageName: node
  linkType: hard

"@jest/globals@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/globals@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/expect": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    jest-mock: "npm:^29.7.0"
  checksum: 10c0/a385c99396878fe6e4460c43bd7bb0a5cc52befb462cc6e7f2a3810f9e7bcce7cdeb51908fd530391ee452dc856c98baa2c5f5fa8a5b30b071d31ef7f6955cea
  languageName: node
  linkType: hard

"@jest/reporters@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/reporters@npm:29.7.0"
  dependencies:
    "@bcoe/v8-coverage": "npm:^0.2.3"
    "@jest/console": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    collect-v8-coverage: "npm:^1.0.0"
    exit: "npm:^0.1.2"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    istanbul-lib-coverage: "npm:^3.0.0"
    istanbul-lib-instrument: "npm:^6.0.0"
    istanbul-lib-report: "npm:^3.0.0"
    istanbul-lib-source-maps: "npm:^4.0.0"
    istanbul-reports: "npm:^3.1.3"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    string-length: "npm:^4.0.1"
    strip-ansi: "npm:^6.0.0"
    v8-to-istanbul: "npm:^9.0.1"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 10c0/a754402a799541c6e5aff2c8160562525e2a47e7d568f01ebfc4da66522de39cbb809bbb0a841c7052e4270d79214e70aec3c169e4eae42a03bc1a8a20cb9fa2
  languageName: node
  linkType: hard

"@jest/schemas@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/schemas@npm:29.6.3"
  dependencies:
    "@sinclair/typebox": "npm:^0.27.8"
  checksum: 10c0/b329e89cd5f20b9278ae1233df74016ebf7b385e0d14b9f4c1ad18d096c4c19d1e687aa113a9c976b16ec07f021ae53dea811fb8c1248a50ac34fbe009fdf6be
  languageName: node
  linkType: hard

"@jest/source-map@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/source-map@npm:29.6.3"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    callsites: "npm:^3.0.0"
    graceful-fs: "npm:^4.2.9"
  checksum: 10c0/a2f177081830a2e8ad3f2e29e20b63bd40bade294880b595acf2fc09ec74b6a9dd98f126a2baa2bf4941acd89b13a4ade5351b3885c224107083a0059b60a219
  languageName: node
  linkType: hard

"@jest/test-result@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-result@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    collect-v8-coverage: "npm:^1.0.0"
  checksum: 10c0/7de54090e54a674ca173470b55dc1afdee994f2d70d185c80236003efd3fa2b753fff51ffcdda8e2890244c411fd2267529d42c4a50a8303755041ee493e6a04
  languageName: node
  linkType: hard

"@jest/test-sequencer@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-sequencer@npm:29.7.0"
  dependencies:
    "@jest/test-result": "npm:^29.7.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    slash: "npm:^3.0.0"
  checksum: 10c0/593a8c4272797bb5628984486080cbf57aed09c7cfdc0a634e8c06c38c6bef329c46c0016e84555ee55d1cd1f381518cf1890990ff845524c1123720c8c1481b
  languageName: node
  linkType: hard

"@jest/transform@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/transform@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@jest/types": "npm:^29.6.3"
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    babel-plugin-istanbul: "npm:^6.1.1"
    chalk: "npm:^4.0.0"
    convert-source-map: "npm:^2.0.0"
    fast-json-stable-stringify: "npm:^2.1.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    pirates: "npm:^4.0.4"
    slash: "npm:^3.0.0"
    write-file-atomic: "npm:^4.0.2"
  checksum: 10c0/7f4a7f73dcf45dfdf280c7aa283cbac7b6e5a904813c3a93ead7e55873761fc20d5c4f0191d2019004fac6f55f061c82eb3249c2901164ad80e362e7a7ede5a6
  languageName: node
  linkType: hard

"@jest/types@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/types@npm:29.6.3"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    "@types/istanbul-reports": "npm:^3.0.0"
    "@types/node": "npm:*"
    "@types/yargs": "npm:^17.0.8"
    chalk: "npm:^4.0.0"
  checksum: 10c0/ea4e493dd3fb47933b8ccab201ae573dcc451f951dc44ed2a86123cd8541b82aa9d2b1031caf9b1080d6673c517e2dcc25a44b2dc4f3fbc37bfc965d444888c0
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/c668feaf86c501d7c804904a61c23c67447b2137b813b9ce03eca82cb9d65ac7006d766c218685d76e3d72828279b6ee26c347aa1119dab23fbaf36aed51585a
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.0.3, @jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10c0/2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
  checksum: 10c0/6a4ecc713ed246ff8e5bdcc1ef7c49aaa93f7463d948ba5054dda18b02dcc6a055e2828c577bcceee058f302ce1fc95595713d44f5c45e43d459f88d267f2f04
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.13, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10c0/2eb864f276eb1096c3c11da3e9bb518f6d9fc0023c78344cdc037abadc725172c70314bdb360f2d4b7bffec7f5d657ce006816bc5d4ecb35e61b66132db00c18
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:0.3.9":
  version: 0.3.9
  resolution: "@jridgewell/trace-mapping@npm:0.3.9"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.0.3"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
  checksum: 10c0/fa425b606d7c7ee5bfa6a31a7b050dd5814b4082f318e0e4190f991902181b4330f43f4805db1dd4f2433fd0ed9cc7a7b9c2683f1deeab1df1b0a98b1e24055b
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.12, @jridgewell/trace-mapping@npm:^0.3.18, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@lukeed/csprng@npm:^1.0.0":
  version: 1.1.0
  resolution: "@lukeed/csprng@npm:1.1.0"
  checksum: 10c0/5d6dcf478af732972083ab2889c294b57f1028fa13c2c240d7a4aaa079c2c75df7ef0dcbdda5419147fc6704b4adf96b2de92f1a9a72ac21c6350c4014fffe6c
  languageName: node
  linkType: hard

"@mapbox/node-pre-gyp@npm:^1.0.11":
  version: 1.0.11
  resolution: "@mapbox/node-pre-gyp@npm:1.0.11"
  dependencies:
    detect-libc: "npm:^2.0.0"
    https-proxy-agent: "npm:^5.0.0"
    make-dir: "npm:^3.1.0"
    node-fetch: "npm:^2.6.7"
    nopt: "npm:^5.0.0"
    npmlog: "npm:^5.0.1"
    rimraf: "npm:^3.0.2"
    semver: "npm:^7.3.5"
    tar: "npm:^6.1.11"
  bin:
    node-pre-gyp: bin/node-pre-gyp
  checksum: 10c0/2b24b93c31beca1c91336fa3b3769fda98e202fb7f9771f0f4062588d36dcc30fcf8118c36aa747fa7f7610d8cf601872bdaaf62ce7822bb08b545d1bbe086cc
  languageName: node
  linkType: hard

"@nestjs/axios@npm:^2.0.0":
  version: 2.0.0
  resolution: "@nestjs/axios@npm:2.0.0"
  peerDependencies:
    "@nestjs/common": ^7.0.0 || ^8.0.0 || ^9.0.0
    axios: ^1.3.1
    reflect-metadata: ^0.1.12
    rxjs: ^6.0.0 || ^7.0.0
  checksum: 10c0/d191c907876dd3b01c63eeebc742b983b9a8f747306982cf882b71be78b90b2c09942ec3a851cc9e5d5e3f82ffad5308df573cff6808e791ac59427048a73d43
  languageName: node
  linkType: hard

"@nestjs/cache-manager@npm:^2.2.2":
  version: 2.3.0
  resolution: "@nestjs/cache-manager@npm:2.3.0"
  peerDependencies:
    "@nestjs/common": ^9.0.0 || ^10.0.0
    "@nestjs/core": ^9.0.0 || ^10.0.0
    cache-manager: <=5
    rxjs: ^7.0.0
  checksum: 10c0/5f9a698024098aa4cf565dbf0bfdf8d334e99e11c44e5d2b6cd786f51f1ee658dc2ec67a2b7846a53a9cccb8d4c0ab866b08dc518c8fc75253881a355c6df5c9
  languageName: node
  linkType: hard

"@nestjs/cli@npm:^9.2.0":
  version: 9.5.0
  resolution: "@nestjs/cli@npm:9.5.0"
  dependencies:
    "@angular-devkit/core": "npm:16.0.1"
    "@angular-devkit/schematics": "npm:16.0.1"
    "@angular-devkit/schematics-cli": "npm:16.0.1"
    "@nestjs/schematics": "npm:^9.0.4"
    chalk: "npm:4.1.2"
    chokidar: "npm:3.5.3"
    cli-table3: "npm:0.6.3"
    commander: "npm:4.1.1"
    fork-ts-checker-webpack-plugin: "npm:8.0.0"
    inquirer: "npm:8.2.5"
    node-emoji: "npm:1.11.0"
    ora: "npm:5.4.1"
    os-name: "npm:4.0.1"
    rimraf: "npm:4.4.1"
    shelljs: "npm:0.8.5"
    source-map-support: "npm:0.5.21"
    tree-kill: "npm:1.2.2"
    tsconfig-paths: "npm:4.2.0"
    tsconfig-paths-webpack-plugin: "npm:4.0.1"
    typescript: "npm:4.9.5"
    webpack: "npm:5.82.1"
    webpack-node-externals: "npm:3.0.0"
  bin:
    nest: bin/nest.js
  checksum: 10c0/31047dff93d8ed9044432fde639b5e9d665d788b43ac3c3f385348722e1b2d8a1ec5ee43a7e5270e5c17fb60a61474621a7dbcc39e9f02d6ccccf785694c8e76
  languageName: node
  linkType: hard

"@nestjs/common@npm:^9.3.9":
  version: 9.4.3
  resolution: "@nestjs/common@npm:9.4.3"
  dependencies:
    iterare: "npm:1.2.1"
    tslib: "npm:2.5.3"
    uid: "npm:2.0.2"
  peerDependencies:
    cache-manager: <=5
    class-transformer: "*"
    class-validator: "*"
    reflect-metadata: ^0.1.12
    rxjs: ^7.1.0
  peerDependenciesMeta:
    cache-manager:
      optional: true
    class-transformer:
      optional: true
    class-validator:
      optional: true
  checksum: 10c0/4776643f768ed32eff4b72f1d8ed2103a3e689e04d63b17d307776aaedc4ea022ba3edfd97a8d59812633f53b12bcb4f45d7b09af8c5df532eaead355e5dfec3
  languageName: node
  linkType: hard

"@nestjs/core@npm:^9.3.9":
  version: 9.4.3
  resolution: "@nestjs/core@npm:9.4.3"
  dependencies:
    "@nuxtjs/opencollective": "npm:0.3.2"
    fast-safe-stringify: "npm:2.1.1"
    iterare: "npm:1.2.1"
    path-to-regexp: "npm:3.2.0"
    tslib: "npm:2.5.3"
    uid: "npm:2.0.2"
  peerDependencies:
    "@nestjs/common": ^9.0.0
    "@nestjs/microservices": ^9.0.0
    "@nestjs/platform-express": ^9.0.0
    "@nestjs/websockets": ^9.0.0
    reflect-metadata: ^0.1.12
    rxjs: ^7.1.0
  peerDependenciesMeta:
    "@nestjs/microservices":
      optional: true
    "@nestjs/platform-express":
      optional: true
    "@nestjs/websockets":
      optional: true
  checksum: 10c0/595bfd46378afb6f5142d7ab4d4ba7f406ea02d08263f41bc1e36d6da637b483b4c32181622cfbaa1cc39ef00d47235ed944aad0e0186b3b191073c6ebd3647c
  languageName: node
  linkType: hard

"@nestjs/jwt@npm:^10.1.0":
  version: 10.2.0
  resolution: "@nestjs/jwt@npm:10.2.0"
  dependencies:
    "@types/jsonwebtoken": "npm:9.0.5"
    jsonwebtoken: "npm:9.0.2"
  peerDependencies:
    "@nestjs/common": ^8.0.0 || ^9.0.0 || ^10.0.0
  checksum: 10c0/81c5cbcb459122b175ad6b50dad83aab7d5dc3beb6122a56c7f985cc1c7838cd1c5eae9d630e95550b95a03e183502a183029e36ba51879c638bd0bad086c056
  languageName: node
  linkType: hard

"@nestjs/mapped-types@npm:1.2.2":
  version: 1.2.2
  resolution: "@nestjs/mapped-types@npm:1.2.2"
  peerDependencies:
    "@nestjs/common": ^7.0.8 || ^8.0.0 || ^9.0.0
    class-transformer: ^0.2.0 || ^0.3.0 || ^0.4.0 || ^0.5.0
    class-validator: ^0.11.1 || ^0.12.0 || ^0.13.0 || ^0.14.0
    reflect-metadata: ^0.1.12
  peerDependenciesMeta:
    class-transformer:
      optional: true
    class-validator:
      optional: true
  checksum: 10c0/e80a08787a6ac2a27199f8d475a338e85a7113a413b6347c700b3ec3e2ee45c59220929c4a79b7a61e05bf9d146fa8c7ba8438a93709542148d7281aae7544e8
  languageName: node
  linkType: hard

"@nestjs/platform-express@npm:^9.3.9":
  version: 9.4.3
  resolution: "@nestjs/platform-express@npm:9.4.3"
  dependencies:
    body-parser: "npm:1.20.2"
    cors: "npm:2.8.5"
    express: "npm:4.18.2"
    multer: "npm:1.4.4-lts.1"
    tslib: "npm:2.5.3"
  peerDependencies:
    "@nestjs/common": ^9.0.0
    "@nestjs/core": ^9.0.0
  checksum: 10c0/7444872a5a6b9c2722561ff9c3722987f9654122098ef3f976d93bd55316be2b865be9c19756e529327346a032ff3ddbba0d9bd11bfc3526c8a5ffbe57173b43
  languageName: node
  linkType: hard

"@nestjs/schedule@npm:^2.1.0":
  version: 2.2.3
  resolution: "@nestjs/schedule@npm:2.2.3"
  dependencies:
    cron: "npm:2.3.1"
    uuid: "npm:9.0.0"
  peerDependencies:
    "@nestjs/common": ^7.0.0 || ^8.0.0 || ^9.0.0
    "@nestjs/core": ^7.0.0 || ^8.0.0 || ^9.0.0
    reflect-metadata: ^0.1.12
  checksum: 10c0/884421a6c2397d932a7d103fd884286b59b67c0848840099339ea835163b5d07b108895fd897f40a97444125b95629e7bc6dd68cba90cce37db486cdd6cb61e4
  languageName: node
  linkType: hard

"@nestjs/schematics@npm:^9.0.4":
  version: 9.2.0
  resolution: "@nestjs/schematics@npm:9.2.0"
  dependencies:
    "@angular-devkit/core": "npm:16.0.1"
    "@angular-devkit/schematics": "npm:16.0.1"
    jsonc-parser: "npm:3.2.0"
    pluralize: "npm:8.0.0"
  peerDependencies:
    typescript: ">=4.3.5"
  checksum: 10c0/8ce2de63f5d2d8ecd5fd5f761e1d718c0921423b620aeed2ac521d61d362aea83995f0081bd8d9af1796fa1f7d14751c5783142b9783aed36ac66c53b19106e0
  languageName: node
  linkType: hard

"@nestjs/swagger@npm:^6.2.1":
  version: 6.3.0
  resolution: "@nestjs/swagger@npm:6.3.0"
  dependencies:
    "@nestjs/mapped-types": "npm:1.2.2"
    js-yaml: "npm:4.1.0"
    lodash: "npm:4.17.21"
    path-to-regexp: "npm:3.2.0"
    swagger-ui-dist: "npm:4.18.2"
  peerDependencies:
    "@fastify/static": ^6.0.0
    "@nestjs/common": ^9.0.0
    "@nestjs/core": ^9.0.0
    class-transformer: "*"
    class-validator: "*"
    reflect-metadata: ^0.1.12
  peerDependenciesMeta:
    "@fastify/static":
      optional: true
    class-transformer:
      optional: true
    class-validator:
      optional: true
  checksum: 10c0/545a50611249b559ef52cd1219ec4514836c03f9351a8a9e3490b7e56eb2ee4e59cc7bac3fe0a0ebc9c60cc14d0f90942793a277cb1bb44d95d12cede18c7c65
  languageName: node
  linkType: hard

"@nestjs/terminus@npm:10":
  version: 10.3.0
  resolution: "@nestjs/terminus@npm:10.3.0"
  dependencies:
    boxen: "npm:5.1.2"
    check-disk-space: "npm:3.4.0"
  peerDependencies:
    "@grpc/grpc-js": "*"
    "@grpc/proto-loader": "*"
    "@mikro-orm/core": "*"
    "@mikro-orm/nestjs": "*"
    "@nestjs/axios": ^1.0.0 || ^2.0.0 || ^3.0.0
    "@nestjs/common": ^9.0.0 || ^10.0.0
    "@nestjs/core": ^9.0.0 || ^10.0.0
    "@nestjs/microservices": ^9.0.0 || ^10.0.0
    "@nestjs/mongoose": ^9.0.0 || ^10.0.0
    "@nestjs/sequelize": ^9.0.0 || ^10.0.0
    "@nestjs/typeorm": ^9.0.0 || ^10.0.0
    "@prisma/client": "*"
    mongoose: "*"
    reflect-metadata: 0.1.x || 0.2.x
    rxjs: 7.x
    sequelize: "*"
    typeorm: "*"
  peerDependenciesMeta:
    "@grpc/grpc-js":
      optional: true
    "@grpc/proto-loader":
      optional: true
    "@mikro-orm/core":
      optional: true
    "@mikro-orm/nestjs":
      optional: true
    "@nestjs/axios":
      optional: true
    "@nestjs/microservices":
      optional: true
    "@nestjs/mongoose":
      optional: true
    "@nestjs/sequelize":
      optional: true
    "@nestjs/typeorm":
      optional: true
    "@prisma/client":
      optional: true
    mongoose:
      optional: true
    sequelize:
      optional: true
    typeorm:
      optional: true
  checksum: 10c0/234b06c3d835905960fc6c277bb889051c7a67c1c8491937349eb03c9a86f53cb76e2c357876a17ad367b96d8cb50df3f6fd140e2b3bde12a3c831ef5a386cd2
  languageName: node
  linkType: hard

"@nestjs/testing@npm:^9.3.9":
  version: 9.4.3
  resolution: "@nestjs/testing@npm:9.4.3"
  dependencies:
    tslib: "npm:2.5.3"
  peerDependencies:
    "@nestjs/common": ^9.0.0
    "@nestjs/core": ^9.0.0
    "@nestjs/microservices": ^9.0.0
    "@nestjs/platform-express": ^9.0.0
  peerDependenciesMeta:
    "@nestjs/microservices":
      optional: true
    "@nestjs/platform-express":
      optional: true
  checksum: 10c0/120ddc30a74171f41b4195360c7e6e8409035c762f87f51873469a6f0a52b6738c0ed882d4bfafdcf0b1bd28734cb1b0bd150ed1ace1034c41cda80b516e2f48
  languageName: node
  linkType: hard

"@nestjs/typeorm@npm:^9.0.1":
  version: 9.0.1
  resolution: "@nestjs/typeorm@npm:9.0.1"
  dependencies:
    uuid: "npm:8.3.2"
  peerDependencies:
    "@nestjs/common": ^8.0.0 || ^9.0.0
    "@nestjs/core": ^8.0.0 || ^9.0.0
    reflect-metadata: ^0.1.13
    rxjs: ^7.2.0
    typeorm: ^0.3.0
  checksum: 10c0/fe4d408dcd3ee22b3710db07d841daed15448a2cd43f61e7634cb2a05c76b9641d085775a5c6dbf876e960139a864d15a684c5534ca8ec1c129b07a2d4e6eca0
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@nuxtjs/opencollective@npm:0.3.2":
  version: 0.3.2
  resolution: "@nuxtjs/opencollective@npm:0.3.2"
  dependencies:
    chalk: "npm:^4.1.0"
    consola: "npm:^2.15.0"
    node-fetch: "npm:^2.6.1"
  bin:
    opencollective: bin/opencollective.js
  checksum: 10c0/540268687af3289ff107585484d42201b404cdbb98b3a512487c12a6b180a8f0e1df0d701df47d3d9e0d5c0f6eb3252d80535562aedca9edf52cf7fd17ae4601
  languageName: node
  linkType: hard

"@ogma/common@npm:^1.1.1":
  version: 1.2.0
  resolution: "@ogma/common@npm:1.2.0"
  checksum: 10c0/a83b2100e1b10664741addee1b17438e6995e60ce002b18d5128738f406f927abcb5b96cc5f491db27c1f2e14dfc5ca23a50c3b9920df3f4fe593c55f90a56cb
  languageName: node
  linkType: hard

"@ogma/styler@npm:^1.0.0":
  version: 1.1.0
  resolution: "@ogma/styler@npm:1.1.0"
  dependencies:
    "@ogma/common": "npm:^1.1.1"
  checksum: 10c0/531ce204bfa7118cea6d555c5e00780540c0cb9e2f9369b4cb0f814a073d662ecc11b2cf54476336d4b742a980ce4bbdd920d6ecda14230f8453ad79e42c3230
  languageName: node
  linkType: hard

"@opentelemetry/api@npm:^1.4.0":
  version: 1.9.0
  resolution: "@opentelemetry/api@npm:1.9.0"
  checksum: 10c0/9aae2fe6e8a3a3eeb6c1fdef78e1939cf05a0f37f8a4fae4d6bf2e09eb1e06f966ece85805626e01ba5fab48072b94f19b835449e58b6d26720ee19a58298add
  languageName: node
  linkType: hard

"@pdf-lib/standard-fonts@npm:^1.0.0":
  version: 1.0.0
  resolution: "@pdf-lib/standard-fonts@npm:1.0.0"
  dependencies:
    pako: "npm:^1.0.6"
  checksum: 10c0/c683adfb764cd235a8370a0c1d5a8d7e90e3499ad33cdecfb92e4d48b0d36cfd038e3a875ebd0937a5646ee1578d793ab98f9c374be360c9a05d2699c1caedf4
  languageName: node
  linkType: hard

"@pdf-lib/upng@npm:^1.0.1":
  version: 1.0.1
  resolution: "@pdf-lib/upng@npm:1.0.1"
  dependencies:
    pako: "npm:^1.0.10"
  checksum: 10c0/9c300c513c1089e561c0cccac01f396a24efb9b0e9c922a39248cb09dfced70c05b9facdfce11a7f22cbedb4129593630a18111b90a57ef34ea4c3df98f2ac1d
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@rtsao/scc@npm:^1.1.0":
  version: 1.1.0
  resolution: "@rtsao/scc@npm:1.1.0"
  checksum: 10c0/b5bcfb0d87f7d1c1c7c0f7693f53b07866ed9fec4c34a97a8c948fb9a7c0082e416ce4d3b60beb4f5e167cbe04cdeefbf6771320f3ede059b9ce91188c409a5b
  languageName: node
  linkType: hard

"@scarf/scarf@npm:=1.4.0":
  version: 1.4.0
  resolution: "@scarf/scarf@npm:1.4.0"
  checksum: 10c0/332118bb488e7a70eaad068fb1a33f016d30442fb0498b37a80cb425c1e741853a5de1a04dce03526ed6265481ecf744aa6e13f072178d19e6b94b19f623ae1c
  languageName: node
  linkType: hard

"@selderee/plugin-htmlparser2@npm:^0.11.0":
  version: 0.11.0
  resolution: "@selderee/plugin-htmlparser2@npm:0.11.0"
  dependencies:
    domhandler: "npm:^5.0.3"
    selderee: "npm:^0.11.0"
  checksum: 10c0/e938ba9aeb31a9cf30dcb2977ef41685c598bf744bedc88c57aa9e8b7e71b51781695cf99c08aac50773fd7714eba670bd2a079e46db0788abe40c6d220084eb
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.27.8":
  version: 0.27.8
  resolution: "@sinclair/typebox@npm:0.27.8"
  checksum: 10c0/ef6351ae073c45c2ac89494dbb3e1f87cc60a93ce4cde797b782812b6f97da0d620ae81973f104b43c9b7eaa789ad20ba4f6a1359f1cc62f63729a55a7d22d4e
  languageName: node
  linkType: hard

"@sinonjs/commons@npm:^3.0.0":
  version: 3.0.1
  resolution: "@sinonjs/commons@npm:3.0.1"
  dependencies:
    type-detect: "npm:4.0.8"
  checksum: 10c0/1227a7b5bd6c6f9584274db996d7f8cee2c8c350534b9d0141fc662eaf1f292ea0ae3ed19e5e5271c8fd390d27e492ca2803acd31a1978be2cdc6be0da711403
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^10.0.2":
  version: 10.3.0
  resolution: "@sinonjs/fake-timers@npm:10.3.0"
  dependencies:
    "@sinonjs/commons": "npm:^3.0.0"
  checksum: 10c0/2e2fb6cc57f227912814085b7b01fede050cd4746ea8d49a1e44d5a0e56a804663b0340ae2f11af7559ea9bf4d087a11f2f646197a660ea3cb04e19efc04aa63
  languageName: node
  linkType: hard

"@smithy/abort-controller@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/abort-controller@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/1ecd5c3454ced008463e6de826c294f31f6073ba91e22e443e0269ee0854d9376f73ea756b3acf77aa806a9a98e8b2568ce2e7f15ddf0a7816c99b7deefeef57
  languageName: node
  linkType: hard

"@smithy/chunked-blob-reader-native@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/chunked-blob-reader-native@npm:4.0.0"
  dependencies:
    "@smithy/util-base64": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/4387f4e8841f20c1c4e689078141de7e6f239e7883be3a02810a023aa30939b15576ee00227b991972d2c5a2f3b6152bcaeca0975c9fa8d3669354c647bd532a
  languageName: node
  linkType: hard

"@smithy/chunked-blob-reader@npm:^5.0.0":
  version: 5.0.0
  resolution: "@smithy/chunked-blob-reader@npm:5.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/55ba0fe366ddaa3f93e1faf8a70df0b67efedbd0008922295efe215df09b68df0ba3043293e65b17e7d1be71448d074c2bfc54e5eb6bd18f59b425822c2b9e9a
  languageName: node
  linkType: hard

"@smithy/config-resolver@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/config-resolver@npm:4.0.1"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/4ec3486deb3017607ed1b9a42b4b806b78e2c7a00f6dd51b98ccb82d9f7506b206bd9412ec0d2a05e95bc2ac3fbbafe55b1ffce9faccc4086f837645f3f7e64d
  languageName: node
  linkType: hard

"@smithy/core@npm:^3.1.1, @smithy/core@npm:^3.1.2":
  version: 3.1.2
  resolution: "@smithy/core@npm:3.1.2"
  dependencies:
    "@smithy/middleware-serde": "npm:^4.0.2"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.1"
    "@smithy/util-stream": "npm:^4.0.2"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/971f6459041a088a9f571f5264e958c6b252f9d56aee210a2a4d4e6a2932a1f8754e48c37ef7c04c2c5e4073465cd6a2be255240c1bd45c30c7ff0669250f382
  languageName: node
  linkType: hard

"@smithy/credential-provider-imds@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/credential-provider-imds@npm:4.0.1"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/url-parser": "npm:^4.0.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/76b5d82dfd2924f2b7a701fa159af54d3e9b16a644a210e3a74e5a3776bb28c2ffbdd342ed3f2bb1d2adf401e8144e84614523b1fad245b43e319e1d01fa1652
  languageName: node
  linkType: hard

"@smithy/eventstream-codec@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/eventstream-codec@npm:4.0.1"
  dependencies:
    "@aws-crypto/crc32": "npm:5.2.0"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/439262fddae863cadad83cc468418294d1d998134619dd67e2836cc93bbfa5b01448e852516046f03b62d0edcd558014b755b1fb0d71b9317268d5c3a5e55bbd
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-browser@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/eventstream-serde-browser@npm:4.0.1"
  dependencies:
    "@smithy/eventstream-serde-universal": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/4766a8a735085dea1ed9aad486fa70cb04908a31843d4e698a28accc373a6dc80bc8abe9834d390f347326458c03424afbd7f7f9e59a66970b839de3d44940e1
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-config-resolver@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/eventstream-serde-config-resolver@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/4ba8bba39392025389c610ce984b612adfe0ed2b37f926e6ce2acafaf178d04aec395924ff37d2ad9534a28652fc64c4938b66b4bd1d2ff695ac8fcdcc4d356e
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-node@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/eventstream-serde-node@npm:4.0.1"
  dependencies:
    "@smithy/eventstream-serde-universal": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ed451ed4483ca62cb450a7540e43ba99b816e32da7bd306d14ea49dd3ceb8a37f791578a0e5d21caf9b9f75c36c69e025c7add117cf8b0510ad3ef32ac38b08c
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-universal@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/eventstream-serde-universal@npm:4.0.1"
  dependencies:
    "@smithy/eventstream-codec": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/8a1261fca8df7559bf78234f961903281b8602ffdbe0ff25f506cba25f013e4bb93bd8380703224fe63aeaf66e13bfebbdaf8083f38628750fc5f3c4ee07dff8
  languageName: node
  linkType: hard

"@smithy/fetch-http-handler@npm:^5.0.1":
  version: 5.0.1
  resolution: "@smithy/fetch-http-handler@npm:5.0.1"
  dependencies:
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/querystring-builder": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-base64": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/5123f6119de50d4c992ebf29b769382d7000db4ed8f564680c5727e2a8beb71664198eb2eaf7cb6152ab777f654d54cf9bff5a4658e1cfdeef2987eeea7f1149
  languageName: node
  linkType: hard

"@smithy/hash-blob-browser@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/hash-blob-browser@npm:4.0.1"
  dependencies:
    "@smithy/chunked-blob-reader": "npm:^5.0.0"
    "@smithy/chunked-blob-reader-native": "npm:^4.0.0"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/16c61fe0ff52074aa374a439955f0ea0a6c6fb64744b55c840f29db1da05cefb340a6d1d4b2a7708ca6f447e972015a95bdfef4fc5361d0bc7c2c3b5cd4c1ca8
  languageName: node
  linkType: hard

"@smithy/hash-node@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/hash-node@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d84be63a2c8a4aafa3b9f23ae76c9cf92a31fa7c49c85930424da1335259b29f6333c5c82d2e7bf689549290ffd0d995043c9ea6f05b0b2a8dfad1f649eac43f
  languageName: node
  linkType: hard

"@smithy/hash-stream-node@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/hash-stream-node@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/c214460da504008905dff7c654cc8b49dfcb060fedef77e63fc36e3c71972be39b018e4a5618e3efb654a6b63a604975521c161ae4614d2580a4c821dfb6e1d5
  languageName: node
  linkType: hard

"@smithy/invalid-dependency@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/invalid-dependency@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/74bebdffb6845f6060eed482ad6e921df66af90d2f8c63f39a3bb334fa68a3e3aa8bd5cd7aa5f65628857e235e113895433895db910ba290633daa0df5725eb7
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/is-array-buffer@npm:2.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/2f2523cd8cc4538131e408eb31664983fecb0c8724956788b015aaf3ab85a0c976b50f4f09b176f1ed7bbe79f3edf80743be7a80a11f22cd9ce1285d77161aaf
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/is-array-buffer@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/ae393fbd5944d710443cd5dd225d1178ef7fb5d6259c14f3e1316ec75e401bda6cf86f7eb98bfd38e5ed76e664b810426a5756b916702cbd418f0933e15e7a3b
  languageName: node
  linkType: hard

"@smithy/md5-js@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/md5-js@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/b5e3fa1d31832535b3a35d0a52ebf983da7cf1a1658b6a7f8bcc948cde808eb361696575d78e5e5df92f3c9b9569b5a1f2d1dff7b465d0a803fa901e0286599d
  languageName: node
  linkType: hard

"@smithy/middleware-content-length@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/middleware-content-length@npm:4.0.1"
  dependencies:
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/3dfbfe658cc8636e9e923a10151a32c6234897c4a86856e55fe4fadc322b3f3e977e50d15553afcb34cadb213de2d95a82af9c8f735e758f4dc21a031e8ecb17
  languageName: node
  linkType: hard

"@smithy/middleware-endpoint@npm:^4.0.2, @smithy/middleware-endpoint@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/middleware-endpoint@npm:4.0.3"
  dependencies:
    "@smithy/core": "npm:^3.1.2"
    "@smithy/middleware-serde": "npm:^4.0.2"
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/shared-ini-file-loader": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/url-parser": "npm:^4.0.1"
    "@smithy/util-middleware": "npm:^4.0.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/9248c2faedb2249c9bd70cedd3fb07be6b739b3ac544a87a9be22c9e61795fcff420f53b81f223d7b0d83156dad2acfe10e614a3d92bffebf57bd93252533d31
  languageName: node
  linkType: hard

"@smithy/middleware-retry@npm:^4.0.3":
  version: 4.0.4
  resolution: "@smithy/middleware-retry@npm:4.0.4"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/service-error-classification": "npm:^4.0.1"
    "@smithy/smithy-client": "npm:^4.1.3"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-middleware": "npm:^4.0.1"
    "@smithy/util-retry": "npm:^4.0.1"
    tslib: "npm:^2.6.2"
    uuid: "npm:^9.0.1"
  checksum: 10c0/d15fecaca5758f0877cecd7de8f9434450850ada42e1e4ac871a181b90e4186dc6d6a912e5e7a4778bf637673823b24922de11cd4e3bbfb75036eef8152bb918
  languageName: node
  linkType: hard

"@smithy/middleware-serde@npm:^4.0.1, @smithy/middleware-serde@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/middleware-serde@npm:4.0.2"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/b1efee86ecc37a063bdfdb89cf691c9b9627502473f2caa0c964c0648f7b550b7a49755a9b13cdfc11aebf1641cf3ae6f8b5f1895a20241960504936da9b3138
  languageName: node
  linkType: hard

"@smithy/middleware-stack@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/middleware-stack@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/b7f710e263e37a8c80c8d31c7d8fe5f66dec2955cde412054eefcc8df53905e1e2e53a01fd7930eb82c82a3a28eadd00e69f07dfc6e793b1d9272db58a982e9b
  languageName: node
  linkType: hard

"@smithy/node-config-provider@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/node-config-provider@npm:4.0.1"
  dependencies:
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/shared-ini-file-loader": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/f8d3b1fe91eeba41426ec57d62cfbeaed027650b5549fb2ba5bc889c1cfb7880d4fdb5a484d231b3fb2a9c9023c1f4e8907a5d18d75b3787481cde9f87c4d9cb
  languageName: node
  linkType: hard

"@smithy/node-http-handler@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/node-http-handler@npm:4.0.2"
  dependencies:
    "@smithy/abort-controller": "npm:^4.0.1"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/querystring-builder": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6a3446dcf3bf006cf55b065edfbe7636f2aa13073f2937e224890902de44b191a5214dce4cb61e98b1ad53889bdbb35386e8810a338bc75ea3743f8d4550a2ad
  languageName: node
  linkType: hard

"@smithy/property-provider@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/property-provider@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/43960a6bdf25944e1cc9d4ee83bf45ab5641f7e2068c46d5015166c0f035b1752e03847d7c15d3c013f5f0467441c9c5a8d6a0428f5401988035867709e4dea3
  languageName: node
  linkType: hard

"@smithy/protocol-http@npm:^5.0.1":
  version: 5.0.1
  resolution: "@smithy/protocol-http@npm:5.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/87b157cc86c23f7199acad237e5e0cc309b18a2a4162dfd8f99609f6cca403f832b645535e58173e2933b4d96ec71f2df16d04e1bdcf52b7b0fcbdbc0067de93
  languageName: node
  linkType: hard

"@smithy/querystring-builder@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/querystring-builder@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-uri-escape": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/21f39e3a79458d343f3dec76b38598c49a34a3c4d1d3c23b6c8895eae2b610fb3c704f995a1730599ef7a881216ea064a25bb7dc8abe5bb1ee50dc6078ad97a4
  languageName: node
  linkType: hard

"@smithy/querystring-parser@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/querystring-parser@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/10e5aba13fbb9a602299fb92f02142e291ab5c7cd221e0ca542981414533e081abdd7442de335f2267ee4a9ff8eba4d7ba848455df50d2771f0ddb8b7d8f9d8b
  languageName: node
  linkType: hard

"@smithy/service-error-classification@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/service-error-classification@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
  checksum: 10c0/de015fd140bf4e97da34a2283ce73971eb3b3aae53a257000dce0c99b8974a5e76bae9e517545ef58bd00ca8094c813cd1bcf0696c2c51e731418e2a769c744f
  languageName: node
  linkType: hard

"@smithy/shared-ini-file-loader@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/shared-ini-file-loader@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/0f0173dbe61c8dac6847cc2c5115db5f1292c956c7f0559ce7bc8e5ed196a4b102977445ee1adb72206a15226a1098cdea01e92aa8ce19f4343f1135e7d37bcf
  languageName: node
  linkType: hard

"@smithy/signature-v4@npm:^5.0.1":
  version: 5.0.1
  resolution: "@smithy/signature-v4@npm:5.0.1"
  dependencies:
    "@smithy/is-array-buffer": "npm:^4.0.0"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.1"
    "@smithy/util-uri-escape": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/a7f118642c9641f813098faad355fc5b54ae215fec589fb238d72d44149248c02e32dcfe034000f151ab665450542df88c70d269f9a3233e01a905ec03512514
  languageName: node
  linkType: hard

"@smithy/smithy-client@npm:^4.1.2, @smithy/smithy-client@npm:^4.1.3":
  version: 4.1.3
  resolution: "@smithy/smithy-client@npm:4.1.3"
  dependencies:
    "@smithy/core": "npm:^3.1.2"
    "@smithy/middleware-endpoint": "npm:^4.0.3"
    "@smithy/middleware-stack": "npm:^4.0.1"
    "@smithy/protocol-http": "npm:^5.0.1"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-stream": "npm:^4.0.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d02044c4ff9e5e6d4c9fbc04b18c4718b1394c72ea5a926e2b6ea47da10a69b87dc27cd48da6c1a0272cc3f4c797591b4016d01bbba1b26397aab404231eda6c
  languageName: node
  linkType: hard

"@smithy/types@npm:^4.1.0":
  version: 4.1.0
  resolution: "@smithy/types@npm:4.1.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/d8817145ea043c5b29783df747ed47c3a1c584fd9d02bbdb609d38b7cb4dded1197ac214ae112744c86abe0537a314dae0edbc0e752bb639ef2d9fb84c67a9d9
  languageName: node
  linkType: hard

"@smithy/url-parser@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/url-parser@npm:4.0.1"
  dependencies:
    "@smithy/querystring-parser": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/fc969b55857b3bcdc920f54bbb9b0c88b5c7695ac7100bea1c7038fd4c9a09ebe0fbb38c4839d39acea28da0d8cb4fea71ffbf362d8aec295acbb94c1b45fc86
  languageName: node
  linkType: hard

"@smithy/util-base64@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-base64@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ad18ec66cc357c189eef358d96876b114faf7086b13e47e009b265d0ff80cec046052500489c183957b3a036768409acdd1a373e01074cc002ca6983f780cffc
  languageName: node
  linkType: hard

"@smithy/util-body-length-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-browser@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/574a10934024a86556e9dcde1a9776170284326c3dfcc034afa128cc5a33c1c8179fca9cfb622ef8be5f2004316cc3f427badccceb943e829105536ec26306d9
  languageName: node
  linkType: hard

"@smithy/util-body-length-node@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-node@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/e91fd3816767606c5f786166ada26440457fceb60f96653b3d624dcf762a8c650e513c275ff3f647cb081c63c283cc178853a7ed9aa224abc8ece4eeeef7a1dd
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/util-buffer-from@npm:2.2.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^2.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/223d6a508b52ff236eea01cddc062b7652d859dd01d457a4e50365af3de1e24a05f756e19433f6ccf1538544076b4215469e21a4ea83dc1d58d829725b0dbc5a
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-buffer-from@npm:4.0.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/be7cd33b6cb91503982b297716251e67cdca02819a15797632091cadab2dc0b4a147fff0709a0aa9bbc0b82a2644a7ed7c8afdd2194d5093cee2e9605b3a9f6f
  languageName: node
  linkType: hard

"@smithy/util-config-provider@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-config-provider@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/cd9498d5f77a73aadd575084bcb22d2bb5945bac4605d605d36f2efe3f165f2b60f4dc88b7a62c2ed082ffa4b2c2f19621d0859f18399edbc2b5988d92e4649f
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-browser@npm:^4.0.3":
  version: 4.0.4
  resolution: "@smithy/util-defaults-mode-browser@npm:4.0.4"
  dependencies:
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/smithy-client": "npm:^4.1.3"
    "@smithy/types": "npm:^4.1.0"
    bowser: "npm:^2.11.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/20c23f94a50d807abaa7dc00e5ec6adb3179672fc967018075e88b5c725ae8d87d8569c9987108b022b856428d55a7abb667d478f8756ad57159d7e65651d3b6
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-node@npm:^4.0.3":
  version: 4.0.4
  resolution: "@smithy/util-defaults-mode-node@npm:4.0.4"
  dependencies:
    "@smithy/config-resolver": "npm:^4.0.1"
    "@smithy/credential-provider-imds": "npm:^4.0.1"
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/property-provider": "npm:^4.0.1"
    "@smithy/smithy-client": "npm:^4.1.3"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/90c213b09c694f1f2d71b147dbbd398be7283a30b0071e85ec968713073e4d5a4cac283426682ee2c09ee50a279a9a6f7f738c45887ba8005eb3a0d4f33d2b11
  languageName: node
  linkType: hard

"@smithy/util-endpoints@npm:^3.0.1":
  version: 3.0.1
  resolution: "@smithy/util-endpoints@npm:3.0.1"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/fed80f300e6a6e69873e613cdd12f640d33a19fc09a41e3afd536f7ea36f7785edd96fbd0402b6980a0e5dfc9bcb8b37f503d522b4ef317f31f4fd0100c466ff
  languageName: node
  linkType: hard

"@smithy/util-hex-encoding@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-hex-encoding@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/70dbb3aa1a79aff3329d07a66411ff26398df338bdd8a6d077b438231afe3dc86d9a7022204baddecd8bc633f059d5c841fa916d81dd7447ea79b64148f386d2
  languageName: node
  linkType: hard

"@smithy/util-middleware@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/util-middleware@npm:4.0.1"
  dependencies:
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/1dd2b058f392fb6788809f14c2c1d53411f79f6e9f88b515ffd36792f9f5939fe4af96fb5b0486a3d0cd30181783b7a5393dce2e8b83ba62db7c6d3af6572eff
  languageName: node
  linkType: hard

"@smithy/util-retry@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/util-retry@npm:4.0.1"
  dependencies:
    "@smithy/service-error-classification": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/93ef89572651b8a30b9a648292660ae9532508ec6d2577afc62e1d9125fe6d14086e0f70a2981bf9f12256b41a57152368b5ed839cdd2df47ba78dd005615173
  languageName: node
  linkType: hard

"@smithy/util-stream@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/util-stream@npm:4.0.2"
  dependencies:
    "@smithy/fetch-http-handler": "npm:^5.0.1"
    "@smithy/node-http-handler": "npm:^4.0.2"
    "@smithy/types": "npm:^4.1.0"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/f9c9afc51189e4d3d33e119fd639970e7abbb598c50ce20f493a2656a469177be4e8a52aa9b8c42ce1f86dd5d71333364a18d179e515e6cc7d28d636cc985f55
  languageName: node
  linkType: hard

"@smithy/util-uri-escape@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-uri-escape@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/23984624060756adba8aa4ab1693fe6b387ee5064d8ec4dfd39bb5908c4ee8b9c3f2dc755da9b07505d8e3ce1338c1867abfa74158931e4728bf3cfcf2c05c3d
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^2.0.0":
  version: 2.3.0
  resolution: "@smithy/util-utf8@npm:2.3.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^2.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/e18840c58cc507ca57fdd624302aefd13337ee982754c9aa688463ffcae598c08461e8620e9852a424d662ffa948fc64919e852508028d09e89ced459bd506ab
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-utf8@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/28a5a5372cbf0b3d2e32dd16f79b04c2aec6f704cf13789db922e9686fde38dde0171491cfa4c2c201595d54752a319faaeeed3c325329610887694431e28c98
  languageName: node
  linkType: hard

"@smithy/util-waiter@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/util-waiter@npm:4.0.2"
  dependencies:
    "@smithy/abort-controller": "npm:^4.0.1"
    "@smithy/types": "npm:^4.1.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/36ee71b41923ae58d9246745e3b7497fe45577dbb97f6e15dd07b4fddb4f82f32e0b7604c7b388fc92d5cbe49d9499998eda979a77a4a770c1b25686a5aed4ce
  languageName: node
  linkType: hard

"@sqltools/formatter@npm:^1.2.5":
  version: 1.2.5
  resolution: "@sqltools/formatter@npm:1.2.5"
  checksum: 10c0/4b4fa62b8cd4880784b71cc5edd4a13da04fda0a915c14282765a8ec1a900a495e69b322704413e2052d221b5646d9fb0e20e87911f9a8f438f33180eecb11a4
  languageName: node
  linkType: hard

"@swc/helpers@npm:^0.3.13":
  version: 0.3.17
  resolution: "@swc/helpers@npm:0.3.17"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/4fc621dedf82cb1eb4aed01f446376162e5eec38c58bab3dbd8cb318145170e18cd6693e83e121038e319d7405aed6cb1064e994edb7a89ca536a57ee34c306d
  languageName: node
  linkType: hard

"@tsconfig/node10@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node10@npm:1.0.11"
  checksum: 10c0/28a0710e5d039e0de484bdf85fee883bfd3f6a8980601f4d44066b0a6bcd821d31c4e231d1117731c4e24268bd4cf2a788a6787c12fc7f8d11014c07d582783c
  languageName: node
  linkType: hard

"@tsconfig/node12@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node12@npm:1.0.11"
  checksum: 10c0/dddca2b553e2bee1308a056705103fc8304e42bb2d2cbd797b84403a223b25c78f2c683ec3e24a095e82cd435387c877239bffcb15a590ba817cd3f6b9a99fd9
  languageName: node
  linkType: hard

"@tsconfig/node14@npm:^1.0.0":
  version: 1.0.3
  resolution: "@tsconfig/node14@npm:1.0.3"
  checksum: 10c0/67c1316d065fdaa32525bc9449ff82c197c4c19092b9663b23213c8cbbf8d88b6ed6a17898e0cbc2711950fbfaf40388938c1c748a2ee89f7234fc9e7fe2bf44
  languageName: node
  linkType: hard

"@tsconfig/node16@npm:^1.0.2":
  version: 1.0.4
  resolution: "@tsconfig/node16@npm:1.0.4"
  checksum: 10c0/05f8f2734e266fb1839eb1d57290df1664fe2aa3b0fdd685a9035806daa635f7519bf6d5d9b33f6e69dd545b8c46bd6e2b5c79acb2b1f146e885f7f11a42a5bb
  languageName: node
  linkType: hard

"@types/adm-zip@npm:^0.5.0":
  version: 0.5.7
  resolution: "@types/adm-zip@npm:0.5.7"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/6ba62bd8f4a6e7ffdad08d951c65c4f69161c2b96cc34249dcf3c448dca85749409407b297472d5c66b711325de89012e8607b74c87d99ec23e9b7a44b6c71c6
  languageName: node
  linkType: hard

"@types/archiver@npm:^6.0.2":
  version: 6.0.3
  resolution: "@types/archiver@npm:6.0.3"
  dependencies:
    "@types/readdir-glob": "npm:*"
  checksum: 10c0/59c6be3d81da240a35f7e4a70348012ee462a74c56fb37c86add2e11562fae46a8e243dcca29a34ed2dc401239b6f3dd9623584570ca5b07c2aa61ee7822f91f
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.1.14":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": "npm:^7.20.7"
    "@babel/types": "npm:^7.20.7"
    "@types/babel__generator": "npm:*"
    "@types/babel__template": "npm:*"
    "@types/babel__traverse": "npm:*"
  checksum: 10c0/bdee3bb69951e833a4b811b8ee9356b69a61ed5b7a23e1a081ec9249769117fa83aaaf023bb06562a038eb5845155ff663e2d5c75dd95c1d5ccc91db012868ff
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.6.8
  resolution: "@types/babel__generator@npm:7.6.8"
  dependencies:
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/f0ba105e7d2296bf367d6e055bb22996886c114261e2cb70bf9359556d0076c7a57239d019dee42bb063f565bade5ccb46009bce2044b2952d964bf9a454d6d2
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": "npm:^7.1.0"
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/cc84f6c6ab1eab1427e90dd2b76ccee65ce940b778a9a67be2c8c39e1994e6f5bbc8efa309f6cea8dc6754994524cd4d2896558df76d92e7a1f46ecffee7112b
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*, @types/babel__traverse@npm:^7.0.6":
  version: 7.20.6
  resolution: "@types/babel__traverse@npm:7.20.6"
  dependencies:
    "@babel/types": "npm:^7.20.7"
  checksum: 10c0/7ba7db61a53e28cac955aa99af280d2600f15a8c056619c05b6fc911cbe02c61aa4f2823299221b23ce0cce00b294c0e5f618ec772aa3f247523c2e48cf7b888
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.5
  resolution: "@types/body-parser@npm:1.19.5"
  dependencies:
    "@types/connect": "npm:*"
    "@types/node": "npm:*"
  checksum: 10c0/aebeb200f25e8818d8cf39cd0209026750d77c9b85381cdd8deeb50913e4d18a1ebe4b74ca9b0b4d21952511eeaba5e9fbbf739b52731a2061e206ec60d568df
  languageName: node
  linkType: hard

"@types/cache-manager@npm:^4.0.2":
  version: 4.0.6
  resolution: "@types/cache-manager@npm:4.0.6"
  checksum: 10c0/3cf78d7ec98a03f651ceb31cc7fbd56f25476c90892d3b19a60c645e8fdd6fe7df99b14afde3fcaec407ab8bb5c8e77332dded77b335cad0fc4b06fbf2df5d62
  languageName: node
  linkType: hard

"@types/cheerio@npm:^0.22.35":
  version: 0.22.35
  resolution: "@types/cheerio@npm:0.22.35"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/648552e6b981185220b356634374aacdee31815ee55c9a6776bcbf7f8c217e54a3a3951d9929b403ca637190ba748f884e6126c730238c5704bf4488a47dedae
  languageName: node
  linkType: hard

"@types/compression@npm:^1":
  version: 1.7.5
  resolution: "@types/compression@npm:1.7.5"
  dependencies:
    "@types/express": "npm:*"
  checksum: 10c0/3818f3d10cede38a835b40b80c341eae162aef1691f2e8f81178a77dbc109f04234cf760b6066eaa06ecbb1da143433c00db2fd9999198b76cd5a193e1d09675
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/2e1cdba2c410f25649e77856505cd60223250fa12dff7a503e492208dbfdd25f62859918f28aba95315251fd1f5e1ffbfca1e25e73037189ab85dd3f8d0a148c
  languageName: node
  linkType: hard

"@types/cookiejar@npm:^2.1.5":
  version: 2.1.5
  resolution: "@types/cookiejar@npm:2.1.5"
  checksum: 10c0/af38c3d84aebb3ccc6e46fb6afeeaac80fb26e63a487dd4db5a8b87e6ad3d4b845ba1116b2ae90d6f886290a36200fa433d8b1f6fe19c47da6b81872ce9a2764
  languageName: node
  linkType: hard

"@types/eslint-scope@npm:^3.7.3":
  version: 3.7.7
  resolution: "@types/eslint-scope@npm:3.7.7"
  dependencies:
    "@types/eslint": "npm:*"
    "@types/estree": "npm:*"
  checksum: 10c0/a0ecbdf2f03912679440550817ff77ef39a30fa8bfdacaf6372b88b1f931828aec392f52283240f0d648cf3055c5ddc564544a626bcf245f3d09fcb099ebe3cc
  languageName: node
  linkType: hard

"@types/eslint@npm:*":
  version: 9.6.1
  resolution: "@types/eslint@npm:9.6.1"
  dependencies:
    "@types/estree": "npm:*"
    "@types/json-schema": "npm:*"
  checksum: 10c0/69ba24fee600d1e4c5abe0df086c1a4d798abf13792d8cfab912d76817fe1a894359a1518557d21237fbaf6eda93c5ab9309143dee4c59ef54336d1b3570420e
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.0":
  version: 1.0.6
  resolution: "@types/estree@npm:1.0.6"
  checksum: 10c0/cdfd751f6f9065442cd40957c07fd80361c962869aa853c1c2fd03e101af8b9389d8ff4955a43a6fcfa223dd387a089937f95be0f3eec21ca527039fd2d9859a
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^4.17.33":
  version: 4.19.6
  resolution: "@types/express-serve-static-core@npm:4.19.6"
  dependencies:
    "@types/node": "npm:*"
    "@types/qs": "npm:*"
    "@types/range-parser": "npm:*"
    "@types/send": "npm:*"
  checksum: 10c0/4281f4ead71723f376b3ddf64868ae26244d434d9906c101cf8d436d4b5c779d01bd046e4ea0ed1a394d3e402216fabfa22b1fa4dba501061cd7c81c54045983
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^5.0.0":
  version: 5.0.6
  resolution: "@types/express-serve-static-core@npm:5.0.6"
  dependencies:
    "@types/node": "npm:*"
    "@types/qs": "npm:*"
    "@types/range-parser": "npm:*"
    "@types/send": "npm:*"
  checksum: 10c0/aced8cc88c1718adbbd1fc488756b0f22d763368d9eff2ae21b350698fab4a77d8d13c3699056dc662a887e43a8b67a3e8f6289ff76102ecc6bad4a7710d31a6
  languageName: node
  linkType: hard

"@types/express@npm:*":
  version: 5.0.0
  resolution: "@types/express@npm:5.0.0"
  dependencies:
    "@types/body-parser": "npm:*"
    "@types/express-serve-static-core": "npm:^5.0.0"
    "@types/qs": "npm:*"
    "@types/serve-static": "npm:*"
  checksum: 10c0/0d74b53aefa69c3b3817ee9b5145fd50d7dbac52a8986afc2d7500085c446656d0b6dc13158c04e2d9f18f4324d4d93b0452337c5ff73dd086dca3e4ff11f47b
  languageName: node
  linkType: hard

"@types/express@npm:^4.17.21":
  version: 4.17.21
  resolution: "@types/express@npm:4.17.21"
  dependencies:
    "@types/body-parser": "npm:*"
    "@types/express-serve-static-core": "npm:^4.17.33"
    "@types/qs": "npm:*"
    "@types/serve-static": "npm:*"
  checksum: 10c0/12e562c4571da50c7d239e117e688dc434db1bac8be55613294762f84fd77fbd0658ccd553c7d3ab02408f385bc93980992369dd30e2ecd2c68c358e6af8fabf
  languageName: node
  linkType: hard

"@types/graceful-fs@npm:^4.1.3":
  version: 4.1.9
  resolution: "@types/graceful-fs@npm:4.1.9"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/235d2fc69741448e853333b7c3d1180a966dd2b8972c8cbcd6b2a0c6cd7f8d582ab2b8e58219dbc62cce8f1b40aa317ff78ea2201cdd8249da5025adebed6f0b
  languageName: node
  linkType: hard

"@types/handlebars@npm:^4.1.0":
  version: 4.1.0
  resolution: "@types/handlebars@npm:4.1.0"
  dependencies:
    handlebars: "npm:*"
  checksum: 10c0/50e85efb4a9306c8b2278dd1570714e9021f201ef317a259ce2d63406057e6575f3066b862bed3d192828d1cc32b80d3a0d2e18d0cd9cda98ec5f1f69038e342
  languageName: node
  linkType: hard

"@types/hjson@npm:^2.4.3":
  version: 2.4.6
  resolution: "@types/hjson@npm:2.4.6"
  checksum: 10c0/529525f5ebfc60aef7d59f1cd68373ccee11c1c1a1d76b79fef2c2b1353725bf471736d9c18bee81f2ca714c373c3f47a0f20355f63539cf0c7bb32fb79ff9e1
  languageName: node
  linkType: hard

"@types/html-to-pdfmake@npm:^2.4.4":
  version: 2.4.4
  resolution: "@types/html-to-pdfmake@npm:2.4.4"
  dependencies:
    "@types/jsdom": "npm:*"
    "@types/pdfmake": "npm:*"
  checksum: 10c0/8ffa346b8b9a3609d2d530f56e5c5eb1fac27fec458c4784b18b797b642150e0b095472af811acefdfe7f82067641523c4d7425053a4c9b7650c976af76b9ce2
  languageName: node
  linkType: hard

"@types/html-to-text@npm:^9":
  version: 9.0.4
  resolution: "@types/html-to-text@npm:9.0.4"
  checksum: 10c0/8e0e548f280d1c1107f70db01ff6331a295f3097f535f4d07c30ced271818fc3803a8f03bb2117c1207c46a3d40da256a03a17bd05fc9f5f1b4821c5bcbf98d8
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.4
  resolution: "@types/http-errors@npm:2.0.4"
  checksum: 10c0/494670a57ad4062fee6c575047ad5782506dd35a6b9ed3894cea65830a94367bd84ba302eb3dde331871f6d70ca287bfedb1b2cf658e6132cd2cbd427ab56836
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0, @types/istanbul-lib-coverage@npm:^2.0.1":
  version: 2.0.6
  resolution: "@types/istanbul-lib-coverage@npm:2.0.6"
  checksum: 10c0/3948088654f3eeb45363f1db158354fb013b362dba2a5c2c18c559484d5eb9f6fd85b23d66c0a7c2fcfab7308d0a585b14dadaca6cc8bf89ebfdc7f8f5102fb7
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.3
  resolution: "@types/istanbul-lib-report@npm:3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:*"
  checksum: 10c0/247e477bbc1a77248f3c6de5dadaae85ff86ac2d76c5fc6ab1776f54512a745ff2a5f791d22b942e3990ddbd40f3ef5289317c4fca5741bedfaa4f01df89051c
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/istanbul-reports@npm:3.0.4"
  dependencies:
    "@types/istanbul-lib-report": "npm:*"
  checksum: 10c0/1647fd402aced5b6edac87274af14ebd6b3a85447ef9ad11853a70fd92a98d35f81a5d3ea9fcb5dbb5834e800c6e35b64475e33fcae6bfa9acc70d61497c54ee
  languageName: node
  linkType: hard

"@types/jest@npm:^29.5.12":
  version: 29.5.14
  resolution: "@types/jest@npm:29.5.14"
  dependencies:
    expect: "npm:^29.0.0"
    pretty-format: "npm:^29.0.0"
  checksum: 10c0/18e0712d818890db8a8dab3d91e9ea9f7f19e3f83c2e50b312f557017dc81466207a71f3ed79cf4428e813ba939954fa26ffa0a9a7f153181ba174581b1c2aed
  languageName: node
  linkType: hard

"@types/jsdom@npm:*, @types/jsdom@npm:^21.1.7":
  version: 21.1.7
  resolution: "@types/jsdom@npm:21.1.7"
  dependencies:
    "@types/node": "npm:*"
    "@types/tough-cookie": "npm:*"
    parse5: "npm:^7.0.0"
  checksum: 10c0/c0c0025adc2b193e85453eeeea168bb909f0ebad08d6552be7474a407e9c163db8f696dcf1e3cbe8cb9c9d970ba45f4386171794509c1a0fe5d1fed72c91679d
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.8, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: 10c0/6bf5337bc447b706bb5b4431d37686aa2ea6d07cfd6f79cc31de80170d6ff9b1c7384a9c0ccbc45b3f512bae9e9f75c2e12109806a15331dc94e8a8db6dbb4ac
  languageName: node
  linkType: hard

"@types/jsonwebtoken@npm:9.0.5":
  version: 9.0.5
  resolution: "@types/jsonwebtoken@npm:9.0.5"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/c582b8420586f3b9550f7e34992cb32be300bc953636f3b087ed9c180ce7ea5c2e4b35090be2d57f0d3168cc3ca1074932907caa2afe09f4e9c84cf5c0daefa8
  languageName: node
  linkType: hard

"@types/jsonwebtoken@npm:^9.0.1":
  version: 9.0.8
  resolution: "@types/jsonwebtoken@npm:9.0.8"
  dependencies:
    "@types/ms": "npm:*"
    "@types/node": "npm:*"
  checksum: 10c0/dd3ccea05115ad9c4458d0c2a487179b1abf8886eaa8cc77d64031a8ef47abd3ff038cdde26e76893372238c8612e4f5fd578b3400e525b65c0e8d3abefe5282
  languageName: node
  linkType: hard

"@types/lodash@npm:^4.14.167":
  version: 4.17.15
  resolution: "@types/lodash@npm:4.17.15"
  checksum: 10c0/2eb2dc6d231f5fb4603d176c08c8d7af688f574d09af47466a179cd7812d9f64144ba74bb32ca014570ffdc544eedc51b7a5657212bad083b6eecbd72223f9bb
  languageName: node
  linkType: hard

"@types/luxon@npm:^3":
  version: 3.4.2
  resolution: "@types/luxon@npm:3.4.2"
  checksum: 10c0/d835467de3daf7e17ba78b50bb5a14efd94272439ca067990d71332a54b311544459c69623eddd243b511b28d70194c9591a9ee8cf9c038962c965f991affd7e
  languageName: node
  linkType: hard

"@types/methods@npm:^1.1.4":
  version: 1.1.4
  resolution: "@types/methods@npm:1.1.4"
  checksum: 10c0/a78534d79c300718298bfff92facd07bf38429c66191f640c1db4c9cff1e36f819304298a96f7536b6512bfc398e5c3e6b831405e138cd774b88ad7be78d682a
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.5
  resolution: "@types/mime@npm:1.3.5"
  checksum: 10c0/c2ee31cd9b993804df33a694d5aa3fa536511a49f2e06eeab0b484fef59b4483777dbb9e42a4198a0809ffbf698081fdbca1e5c2218b82b91603dfab10a10fbc
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 2.1.0
  resolution: "@types/ms@npm:2.1.0"
  checksum: 10c0/5ce692ffe1549e1b827d99ef8ff71187457e0eb44adbae38fdf7b9a74bae8d20642ee963c14516db1d35fa2652e65f47680fdf679dcbde52bbfadd021f497225
  languageName: node
  linkType: hard

"@types/multer@npm:^1.4.11":
  version: 1.4.12
  resolution: "@types/multer@npm:1.4.12"
  dependencies:
    "@types/express": "npm:*"
  checksum: 10c0/1250a32a66b7c5929cc5b005dae4ad0768ded9b6b52ba6f90931ff39b4ed775b09a0fd9d880f0836bcac16a9a7febed6a479c6bf108a52a244c4c53badb82a7b
  languageName: node
  linkType: hard

"@types/node-fetch@npm:^2.6.4":
  version: 2.6.12
  resolution: "@types/node-fetch@npm:2.6.12"
  dependencies:
    "@types/node": "npm:*"
    form-data: "npm:^4.0.0"
  checksum: 10c0/7693acad5499b7df2d1727d46cff092a63896dc04645f36b973dd6dd754a59a7faba76fcb777bdaa35d80625c6a9dd7257cca9c401a4bab03b04480cda7fd1af
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 22.13.1
  resolution: "@types/node@npm:22.13.1"
  dependencies:
    undici-types: "npm:~6.20.0"
  checksum: 10c0/d4e56d41d8bd53de93da2651c0a0234e330bd7b1b6d071b1a94bd3b5ee2d9f387519e739c52a15c1faa4fb9d97e825b848421af4b2e50e6518011e7adb4a34b7
  languageName: node
  linkType: hard

"@types/node@npm:^18.11.18, @types/node@npm:^18.15.0":
  version: 18.19.75
  resolution: "@types/node@npm:18.19.75"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: 10c0/6a78833071d23dcd4010507d0a232da1cb6e939eb5b62023a01ab5f91eecb90223bda3e34aa536f02cd5c3bdf7962c754b7e2a051a8224aed5886788fce88fbf
  languageName: node
  linkType: hard

"@types/papaparse@npm:^5.3.2":
  version: 5.3.15
  resolution: "@types/papaparse@npm:5.3.15"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/9333e980b9ed4102f80b1791b6dfefb23fb4d27252b2b6ee6084b2cd847cbe802cfb838d46b4f6b4cb035f9225e0323034c956ee21bbcbcb4cbdb663302d5eeb
  languageName: node
  linkType: hard

"@types/parse-json@npm:^4.0.0":
  version: 4.0.2
  resolution: "@types/parse-json@npm:4.0.2"
  checksum: 10c0/b1b863ac34a2c2172fbe0807a1ec4d5cb684e48d422d15ec95980b81475fac4fdb3768a8b13eef39130203a7c04340fc167bae057c7ebcafd7dec9fe6c36aeb1
  languageName: node
  linkType: hard

"@types/pdfkit@npm:*, @types/pdfkit@npm:^0.13.4":
  version: 0.13.9
  resolution: "@types/pdfkit@npm:0.13.9"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/e467fbbc4f4e5a487887c9f27784aa6262e5a45ab8f5676ee4662eb8862e1ef9a18a8f463fbc3150f2d50fe83902c5877714ac141acd8ccb780e18c88999cc29
  languageName: node
  linkType: hard

"@types/pdfmake@npm:*, @types/pdfmake@npm:^0":
  version: 0.2.11
  resolution: "@types/pdfmake@npm:0.2.11"
  dependencies:
    "@types/node": "npm:*"
    "@types/pdfkit": "npm:*"
  checksum: 10c0/751ea63f2a5aaacbf008543ec58d01f80a11948b7fe18ee531668f97bbed3b0622d3fb1946e3d6b02af9c7ef7396ec858e6f5f5c551b746832b94a6ac33ec453
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.9.18
  resolution: "@types/qs@npm:6.9.18"
  checksum: 10c0/790b9091348e06dde2c8e4118b5771ab386a8c22a952139a2eb0675360a2070d0b155663bf6f75b23f258fd0a1f7ffc0ba0f059d99a719332c03c40d9e9cd63b
  languageName: node
  linkType: hard

"@types/ramda@npm:^0.28.23":
  version: 0.28.25
  resolution: "@types/ramda@npm:0.28.25"
  dependencies:
    ts-toolbelt: "npm:^6.15.1"
  checksum: 10c0/4abb7c8539eadb11a62ddd80072363870bb33b189096a1dc71d2351ccba9796c26a43b79435312b8e1f3335ac7d5a77fc4deb2f5b747217bfdf262f519c9caf3
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.7
  resolution: "@types/range-parser@npm:1.2.7"
  checksum: 10c0/361bb3e964ec5133fa40644a0b942279ed5df1949f21321d77de79f48b728d39253e5ce0408c9c17e4e0fd95ca7899da36841686393b9f7a1e209916e9381a3c
  languageName: node
  linkType: hard

"@types/readdir-glob@npm:*":
  version: 1.1.5
  resolution: "@types/readdir-glob@npm:1.1.5"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/46849136a3b5246105bca0303aab80552a9ff67e024e77ef1845a806a24c1a621dfcba0e4ee5a00ebad17f51edb80928f2dd6dc510a1d9897f3bc22ed64e5cbd
  languageName: node
  linkType: hard

"@types/semver@npm:^7.3.12":
  version: 7.5.8
  resolution: "@types/semver@npm:7.5.8"
  checksum: 10c0/8663ff927234d1c5fcc04b33062cb2b9fcfbe0f5f351ed26c4d1e1581657deebd506b41ff7fdf89e787e3d33ce05854bc01686379b89e9c49b564c4cfa988efa
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.4
  resolution: "@types/send@npm:0.17.4"
  dependencies:
    "@types/mime": "npm:^1"
    "@types/node": "npm:*"
  checksum: 10c0/7f17fa696cb83be0a104b04b424fdedc7eaba1c9a34b06027239aba513b398a0e2b7279778af521f516a397ced417c96960e5f50fcfce40c4bc4509fb1a5883c
  languageName: node
  linkType: hard

"@types/serve-static@npm:*":
  version: 1.15.7
  resolution: "@types/serve-static@npm:1.15.7"
  dependencies:
    "@types/http-errors": "npm:*"
    "@types/node": "npm:*"
    "@types/send": "npm:*"
  checksum: 10c0/26ec864d3a626ea627f8b09c122b623499d2221bbf2f470127f4c9ebfe92bd8a6bb5157001372d4c4bd0dd37a1691620217d9dc4df5aa8f779f3fd996b1c60ae
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^2.0.0":
  version: 2.0.3
  resolution: "@types/stack-utils@npm:2.0.3"
  checksum: 10c0/1f4658385ae936330581bcb8aa3a066df03867d90281cdf89cc356d404bd6579be0f11902304e1f775d92df22c6dd761d4451c804b0a4fba973e06211e9bd77c
  languageName: node
  linkType: hard

"@types/superagent@npm:*":
  version: 8.1.9
  resolution: "@types/superagent@npm:8.1.9"
  dependencies:
    "@types/cookiejar": "npm:^2.1.5"
    "@types/methods": "npm:^1.1.4"
    "@types/node": "npm:*"
    form-data: "npm:^4.0.0"
  checksum: 10c0/12631f1d8b3a62e1f435bc885f6d64d1a2d1ae82b80f0c6d63d4d6372c40b6f1fee6b3da59ac18bb86250b1eb73583bf2d4b1f7882048c32468791c560c69b7c
  languageName: node
  linkType: hard

"@types/supertest@npm:^2.0.12":
  version: 2.0.16
  resolution: "@types/supertest@npm:2.0.16"
  dependencies:
    "@types/superagent": "npm:*"
  checksum: 10c0/e1b4a4d788c19cd92a3f2e6d0979fb0f679c49aefae2011895a4d9c35aa960d43463aca8783a0b3382bbf0b4eb7ceaf8752d7dc80b8f5a9644fa14e1b1bdbc90
  languageName: node
  linkType: hard

"@types/tough-cookie@npm:*":
  version: 4.0.5
  resolution: "@types/tough-cookie@npm:4.0.5"
  checksum: 10c0/68c6921721a3dcb40451543db2174a145ef915bc8bcbe7ad4e59194a0238e776e782b896c7a59f4b93ac6acefca9161fccb31d1ce3b3445cb6faa467297fb473
  languageName: node
  linkType: hard

"@types/uuid@npm:^9.0.1":
  version: 9.0.8
  resolution: "@types/uuid@npm:9.0.8"
  checksum: 10c0/b411b93054cb1d4361919579ef3508a1f12bf15b5fdd97337d3d351bece6c921b52b6daeef89b62340fd73fd60da407878432a1af777f40648cbe53a01723489
  languageName: node
  linkType: hard

"@types/validator@npm:^13.11.8":
  version: 13.12.2
  resolution: "@types/validator@npm:13.12.2"
  checksum: 10c0/64f1326c768947d756ab5bcd73f3f11a6f07dc76292aea83890d0390a9b9acb374f8df6b24af2c783271f276d3d613b78fc79491fe87edee62108d54be2e3c31
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.3
  resolution: "@types/yargs-parser@npm:21.0.3"
  checksum: 10c0/e71c3bd9d0b73ca82e10bee2064c384ab70f61034bbfb78e74f5206283fc16a6d85267b606b5c22cb2a3338373586786fed595b2009825d6a9115afba36560a0
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.8":
  version: 17.0.33
  resolution: "@types/yargs@npm:17.0.33"
  dependencies:
    "@types/yargs-parser": "npm:*"
  checksum: 10c0/d16937d7ac30dff697801c3d6f235be2166df42e4a88bf730fa6dc09201de3727c0a9500c59a672122313341de5f24e45ee0ff579c08ce91928e519090b7906b
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^5.54.1":
  version: 5.62.0
  resolution: "@typescript-eslint/eslint-plugin@npm:5.62.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.4.0"
    "@typescript-eslint/scope-manager": "npm:5.62.0"
    "@typescript-eslint/type-utils": "npm:5.62.0"
    "@typescript-eslint/utils": "npm:5.62.0"
    debug: "npm:^4.3.4"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.2.0"
    natural-compare-lite: "npm:^1.4.0"
    semver: "npm:^7.3.7"
    tsutils: "npm:^3.21.0"
  peerDependencies:
    "@typescript-eslint/parser": ^5.0.0
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/3f40cb6bab5a2833c3544e4621b9fdacd8ea53420cadc1c63fac3b89cdf5c62be1e6b7bcf56976dede5db4c43830de298ced3db60b5494a3b961ca1b4bff9f2a
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^5.54.1":
  version: 5.62.0
  resolution: "@typescript-eslint/parser@npm:5.62.0"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:5.62.0"
    "@typescript-eslint/types": "npm:5.62.0"
    "@typescript-eslint/typescript-estree": "npm:5.62.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/315194b3bf39beb9bd16c190956c46beec64b8371e18d6bb72002108b250983eb1e186a01d34b77eb4045f4941acbb243b16155fbb46881105f65e37dc9e24d4
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/scope-manager@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": "npm:5.62.0"
    "@typescript-eslint/visitor-keys": "npm:5.62.0"
  checksum: 10c0/861253235576c1c5c1772d23cdce1418c2da2618a479a7de4f6114a12a7ca853011a1e530525d0931c355a8fd237b9cd828fac560f85f9623e24054fd024726f
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/type-utils@npm:5.62.0"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:5.62.0"
    "@typescript-eslint/utils": "npm:5.62.0"
    debug: "npm:^4.3.4"
    tsutils: "npm:^3.21.0"
  peerDependencies:
    eslint: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/93112e34026069a48f0484b98caca1c89d9707842afe14e08e7390af51cdde87378df29d213d3bbd10a7cfe6f91b228031b56218515ce077bdb62ddea9d9f474
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/types@npm:5.62.0"
  checksum: 10c0/7febd3a7f0701c0b927e094f02e82d8ee2cada2b186fcb938bc2b94ff6fbad88237afc304cbaf33e82797078bbbb1baf91475f6400912f8b64c89be79bfa4ddf
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/typescript-estree@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": "npm:5.62.0"
    "@typescript-eslint/visitor-keys": "npm:5.62.0"
    debug: "npm:^4.3.4"
    globby: "npm:^11.1.0"
    is-glob: "npm:^4.0.3"
    semver: "npm:^7.3.7"
    tsutils: "npm:^3.21.0"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/d7984a3e9d56897b2481940ec803cb8e7ead03df8d9cfd9797350be82ff765dfcf3cfec04e7355e1779e948da8f02bc5e11719d07a596eb1cb995c48a95e38cf
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/utils@npm:5.62.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@types/json-schema": "npm:^7.0.9"
    "@types/semver": "npm:^7.3.12"
    "@typescript-eslint/scope-manager": "npm:5.62.0"
    "@typescript-eslint/types": "npm:5.62.0"
    "@typescript-eslint/typescript-estree": "npm:5.62.0"
    eslint-scope: "npm:^5.1.1"
    semver: "npm:^7.3.7"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/f09b7d9952e4a205eb1ced31d7684dd55cee40bf8c2d78e923aa8a255318d97279825733902742c09d8690f37a50243f4c4d383ab16bd7aefaf9c4b438f785e1
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/visitor-keys@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": "npm:5.62.0"
    eslint-visitor-keys: "npm:^3.3.0"
  checksum: 10c0/7c3b8e4148e9b94d9b7162a596a1260d7a3efc4e65199693b8025c71c4652b8042501c0bc9f57654c1e2943c26da98c0f77884a746c6ae81389fcb0b513d995d
  languageName: node
  linkType: hard

"@typespec/ts-http-runtime@npm:^0.2.2":
  version: 0.2.2
  resolution: "@typespec/ts-http-runtime@npm:0.2.2"
  dependencies:
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/0f7d633f9885bd7fb065b205887eb6a85639c37ef2d4b50a1b55cee3ef7ad270dcf4757db0882a39157624e8888c6f1f29aaf4c163403c91e75a4b646d362c49
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.2.0":
  version: 1.3.0
  resolution: "@ungap/structured-clone@npm:1.3.0"
  checksum: 10c0/0fc3097c2540ada1fc340ee56d58d96b5b536a2a0dab6e3ec17d4bfc8c4c86db345f61a375a8185f9da96f01c69678f836a2b57eeaa9e4b8eeafd26428e57b0a
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.14.1, @webassemblyjs/ast@npm:^1.11.5":
  version: 1.14.1
  resolution: "@webassemblyjs/ast@npm:1.14.1"
  dependencies:
    "@webassemblyjs/helper-numbers": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
  checksum: 10c0/67a59be8ed50ddd33fbb2e09daa5193ac215bf7f40a9371be9a0d9797a114d0d1196316d2f3943efdb923a3d809175e1563a3cb80c814fb8edccd1e77494972b
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.13.2"
  checksum: 10c0/0e88bdb8b50507d9938be64df0867f00396b55eba9df7d3546eb5dc0ca64d62e06f8d881ec4a6153f2127d0f4c11d102b6e7d17aec2f26bb5ff95a5e60652412
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-api-error@npm:1.13.2"
  checksum: 10c0/31be497f996ed30aae4c08cac3cce50c8dcd5b29660383c0155fce1753804fc55d47fcba74e10141c7dd2899033164e117b3bcfcda23a6b043e4ded4f1003dfb
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-buffer@npm:1.14.1"
  checksum: 10c0/0d54105dc373c0fe6287f1091e41e3a02e36cdc05e8cf8533cdc16c59ff05a646355415893449d3768cda588af451c274f13263300a251dc11a575bc4c9bd210
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-numbers@npm:1.13.2"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": "npm:1.13.2"
    "@webassemblyjs/helper-api-error": "npm:1.13.2"
    "@xtuc/long": "npm:4.2.2"
  checksum: 10c0/9c46852f31b234a8fb5a5a9d3f027bc542392a0d4de32f1a9c0075d5e8684aa073cb5929b56df565500b3f9cc0a2ab983b650314295b9bf208d1a1651bfc825a
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.13.2"
  checksum: 10c0/c4355d14f369b30cf3cbdd3acfafc7d0488e086be6d578e3c9780bd1b512932352246be96e034e2a7fcfba4f540ec813352f312bfcbbfe5bcfbf694f82ccc682
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
  checksum: 10c0/1f9b33731c3c6dbac3a9c483269562fa00d1b6a4e7133217f40e83e975e636fd0f8736e53abd9a47b06b66082ecc976c7384391ab0a68e12d509ea4e4b948d64
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/ieee754@npm:1.13.2"
  dependencies:
    "@xtuc/ieee754": "npm:^1.2.0"
  checksum: 10c0/2e732ca78c6fbae3c9b112f4915d85caecdab285c0b337954b180460290ccd0fb00d2b1dc4bb69df3504abead5191e0d28d0d17dfd6c9d2f30acac8c4961c8a7
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/leb128@npm:1.13.2"
  dependencies:
    "@xtuc/long": "npm:4.2.2"
  checksum: 10c0/dad5ef9e383c8ab523ce432dfd80098384bf01c45f70eb179d594f85ce5db2f80fa8c9cba03adafd85684e6d6310f0d3969a882538975989919329ac4c984659
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/utf8@npm:1.13.2"
  checksum: 10c0/d3fac9130b0e3e5a1a7f2886124a278e9323827c87a2b971e6d0da22a2ba1278ac9f66a4f2e363ecd9fac8da42e6941b22df061a119e5c0335f81006de9ee799
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:^1.11.5":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-edit@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-section": "npm:1.14.1"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
    "@webassemblyjs/wasm-opt": "npm:1.14.1"
    "@webassemblyjs/wasm-parser": "npm:1.14.1"
    "@webassemblyjs/wast-printer": "npm:1.14.1"
  checksum: 10c0/5ac4781086a2ca4b320bdbfd965a209655fe8a208ca38d89197148f8597e587c9a2c94fb6bd6f1a7dbd4527c49c6844fcdc2af981f8d793a97bf63a016aa86d2
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-gen@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/ieee754": "npm:1.13.2"
    "@webassemblyjs/leb128": "npm:1.13.2"
    "@webassemblyjs/utf8": "npm:1.13.2"
  checksum: 10c0/d678810d7f3f8fecb2e2bdadfb9afad2ec1d2bc79f59e4711ab49c81cec578371e22732d4966f59067abe5fba8e9c54923b57060a729d28d408e608beef67b10
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-opt@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
    "@webassemblyjs/wasm-parser": "npm:1.14.1"
  checksum: 10c0/515bfb15277ee99ba6b11d2232ddbf22aed32aad6d0956fe8a0a0a004a1b5a3a277a71d9a3a38365d0538ac40d1b7b7243b1a244ad6cd6dece1c1bb2eb5de7ee
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.14.1, @webassemblyjs/wasm-parser@npm:^1.11.5":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-parser@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-api-error": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/ieee754": "npm:1.13.2"
    "@webassemblyjs/leb128": "npm:1.13.2"
    "@webassemblyjs/utf8": "npm:1.13.2"
  checksum: 10c0/95427b9e5addbd0f647939bd28e3e06b8deefdbdadcf892385b5edc70091bf9b92fa5faac3fce8333554437c5d85835afef8c8a7d9d27ab6ba01ffab954db8c6
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wast-printer@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@xtuc/long": "npm:4.2.2"
  checksum: 10c0/8d7768608996a052545251e896eac079c98e0401842af8dd4de78fba8d90bd505efb6c537e909cd6dae96e09db3fa2e765a6f26492553a675da56e2db51f9d24
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: 10c0/a8565d29d135039bd99ae4b2220d3e167d22cf53f867e491ed479b3f84f895742d0097f935b19aab90265a23d5d46711e4204f14c479ae3637fbf06c4666882f
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 10c0/8582cbc69c79ad2d31568c412129bf23d2b1210a1dfb60c82d5a1df93334da4ee51f3057051658569e2c196d8dc33bc05ae6b974a711d0d16e801e1d0647ccd1
  languageName: node
  linkType: hard

"abbrev@npm:1":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: 10c0/3f762677702acb24f65e813070e306c61fafe25d4b2583f9dfc935131f774863f3addd5741572ed576bd69cabe473c5af18e1e108b829cb7b6b4747884f726e6
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.0
  resolution: "abbrev@npm:3.0.0"
  checksum: 10c0/049704186396f571650eb7b22ed3627b77a5aedf98bb83caf2eac81ca2a3e25e795394b0464cfb2d6076df3db6a5312139eac5b6a126ca296ac53c5008069c28
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10c0/90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10c0/3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn-import-assertions@npm:^1.7.6":
  version: 1.9.0
  resolution: "acorn-import-assertions@npm:1.9.0"
  peerDependencies:
    acorn: ^8
  checksum: 10c0/3b4a194e128efdc9b86c2b1544f623aba4c1aa70d638f8ab7dc3971a5b4aa4c57bd62f99af6e5325bb5973c55863b4112e708a6f408bad7a138647ca72283afe
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.1.1":
  version: 8.3.4
  resolution: "acorn-walk@npm:8.3.4"
  dependencies:
    acorn: "npm:^8.11.0"
  checksum: 10c0/76537ac5fb2c37a64560feaf3342023dadc086c46da57da363e64c6148dc21b57d49ace26f949e225063acb6fb441eabffd89f7a3066de5ad37ab3e328927c62
  languageName: node
  linkType: hard

"acorn@npm:^8.11.0, acorn@npm:^8.4.1, acorn@npm:^8.7.1, acorn@npm:^8.8.2, acorn@npm:^8.9.0":
  version: 8.14.0
  resolution: "acorn@npm:8.14.0"
  bin:
    acorn: bin/acorn
  checksum: 10c0/6d4ee461a7734b2f48836ee0fbb752903606e576cc100eb49340295129ca0b452f3ba91ddd4424a1d4406a98adfb2ebb6bd0ff4c49d7a0930c10e462719bbfd7
  languageName: node
  linkType: hard

"adm-zip@npm:^0.5.16":
  version: 0.5.16
  resolution: "adm-zip@npm:0.5.16"
  checksum: 10c0/6f10119d4570c7ba76dcf428abb8d3f69e63f92e51f700a542b43d4c0130373dd2ddfc8f85059f12d4a843703a90c3970cfd17876844b4f3f48bf042bfa6b49f
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: "npm:4"
  checksum: 10c0/dc4f757e40b5f3e3d674bc9beb4f1048f4ee83af189bae39be99f57bf1f48dde166a8b0a5342a84b5944ee8e6ed1e5a9d801858f4ad44764e84957122fe46261
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10c0/6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.1.0, agentkeepalive@npm:^4.2.1":
  version: 4.6.0
  resolution: "agentkeepalive@npm:4.6.0"
  dependencies:
    humanize-ms: "npm:^1.2.1"
  checksum: 10c0/235c182432f75046835b05f239708107138a40103deee23b6a08caee5136873709155753b394ec212e49e60e94a378189562cb01347765515cff61b692c69187
  languageName: node
  linkType: hard

"ajv-formats@npm:2.1.1, ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: "npm:^8.0.0"
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 10c0/e43ba22e91b6a48d96224b83d260d3a3a561b42d391f8d3c6d2c1559f9aa5b253bfb306bc94bbeca1d967c014e15a6efe9a207309e95b3eaae07fcbcdc2af662
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 10c0/0c57a47cbd656e8cdfd99d7c2264de5868918ffa207c8d7a72a7f63379d4333254b2ba03d69e3c035e996a3fd3eb6d5725d7a1597cca10694296e32510546360
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.1.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
  peerDependencies:
    ajv: ^8.8.2
  checksum: 10c0/18bec51f0171b83123ba1d8883c126e60c6f420cef885250898bf77a8d3e65e3bfb9e8564f497e30bdbe762a83e0d144a36931328616a973ee669dc74d4a9590
  languageName: node
  linkType: hard

"ajv@npm:8.12.0":
  version: 8.12.0
  resolution: "ajv@npm:8.12.0"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/ac4f72adf727ee425e049bc9d8b31d4a57e1c90da8d28bcd23d60781b12fcd6fc3d68db5df16994c57b78b94eed7988f5a6b482fd376dc5b084125e20a0a622e
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4, ajv@npm:^6.12.5":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.9.0":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    fast-uri: "npm:^3.0.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
  checksum: 10c0/ec3ba10a573c6b60f94639ffc53526275917a2df6810e4ab5a6b959d87459f9ef3f00d5e7865b82677cb7d21590355b34da14d1d0b9c32d75f95a187e76fff35
  languageName: node
  linkType: hard

"ansi-align@npm:^3.0.0":
  version: 3.0.1
  resolution: "ansi-align@npm:3.0.1"
  dependencies:
    string-width: "npm:^4.1.0"
  checksum: 10c0/ad8b755a253a1bc8234eb341e0cec68a857ab18bf97ba2bda529e86f6e30460416523e0ec58c32e5c21f0ca470d779503244892873a5895dbd0c39c788e82467
  languageName: node
  linkType: hard

"ansi-colors@npm:4.1.3":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: 10c0/ec87a2f59902f74e61eada7f6e6fe20094a628dab765cfdbd03c3477599368768cffccdb5d3bb19a1b6c99126783a143b1fee31aab729b31ffe5836c7e5e28b9
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10c0/da917be01871525a3dfcf925ae2977bc59e8c513d4423368645634bf5d4ceba5401574eb705c1e92b79f7292af5a656f78c5725a4b0e1cec97c4b413705c1d50
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 10c0/9c4ca80eb3c2fb7b33841c210d2f20807f40865d27008d7c3f707b7f95cab7d67462a565e2388ac3285b71cb3d9bb2173de8da37c57692a362885ec34d6e27df
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 10c0/60f0298ed34c74fef50daab88e8dab786036ed5a7fad02e012ab57e376e0a0b4b29e83b95ea9b5e7d89df762f5f25119b83e00706ecaccb22cfbacee98d74889
  languageName: node
  linkType: hard

"anymatch@npm:^3.0.3, anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"api2dat5@npm:^1.2.2":
  version: 1.5.3
  resolution: "api2dat5@npm:1.5.3"
  peerDependencies:
    axios: ^1.6.0
    fast-xml-parser: ^3.17.6
    hash.js: ^1.1.7
  checksum: 10c0/75c78289ea6d514d6922407475468ee447da32794a754f0858b7015d8e34219ef37c37bda4d7efb395f0e88462a2f9231ed2f6526bf18617b75f7894a9068788
  languageName: node
  linkType: hard

"app-root-path@npm:^3.1.0":
  version: 3.1.0
  resolution: "app-root-path@npm:3.1.0"
  checksum: 10c0/4a0fd976de1bffcdb18a5e1f8050091f15d0780e0582bca99aaa9d52de71f0e08e5185355fcffc781180bfb898499e787a2f5ed79b9c448b942b31dc947acaa9
  languageName: node
  linkType: hard

"append-field@npm:^1.0.0":
  version: 1.0.0
  resolution: "append-field@npm:1.0.0"
  checksum: 10c0/1b5abcc227e5179936a9e4f7e2af4769fa1f00eda85bbaed907f7964b0fd1f7d61f0f332b35337f391389ff13dd5310c2546ba670f8e5a743b23ec85185c73ef
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 10c0/d06e26384a8f6245d8c8896e138c0388824e259a329e0c9f196b4fa533c82502a6fd449586e3604950a0c42921832a458bb3aa0aa9f0ba449cfd4f50fd0d09b5
  languageName: node
  linkType: hard

"archiver-utils@npm:^4.0.1":
  version: 4.0.1
  resolution: "archiver-utils@npm:4.0.1"
  dependencies:
    glob: "npm:^8.0.0"
    graceful-fs: "npm:^4.2.0"
    lazystream: "npm:^1.0.0"
    lodash: "npm:^4.17.15"
    normalize-path: "npm:^3.0.0"
    readable-stream: "npm:^3.6.0"
  checksum: 10c0/fc646fe1f8e3650383b6f79384e1c8f69caf7685c705221e23393a674ee1d67331e246250a72b03ec2fbdb2cfe30adc2d4287f6357684d6843d604738bf2c870
  languageName: node
  linkType: hard

"archiver@npm:^6.0.1":
  version: 6.0.2
  resolution: "archiver@npm:6.0.2"
  dependencies:
    archiver-utils: "npm:^4.0.1"
    async: "npm:^3.2.4"
    buffer-crc32: "npm:^0.2.1"
    readable-stream: "npm:^3.6.0"
    readdir-glob: "npm:^1.1.2"
    tar-stream: "npm:^3.0.0"
    zip-stream: "npm:^5.0.1"
  checksum: 10c0/23a470d468c01cd40fc13b6bd3dbc6d04c4f7b770785dcc7e1e4af256c3d79c4ffd7f7e0e84ae320437e5b8d0a2117aecfca0586b8c0fbd6edc3e04977c438cc
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^2.0.0":
  version: 2.0.0
  resolution: "are-we-there-yet@npm:2.0.0"
  dependencies:
    delegates: "npm:^1.0.0"
    readable-stream: "npm:^3.6.0"
  checksum: 10c0/375f753c10329153c8d66dc95e8f8b6c7cc2aa66e05cb0960bd69092b10dae22900cacc7d653ad11d26b3ecbdbfe1e8bfb6ccf0265ba8077a7d979970f16b99c
  languageName: node
  linkType: hard

"arg@npm:^4.1.0":
  version: 4.1.3
  resolution: "arg@npm:4.1.3"
  checksum: 10c0/070ff801a9d236a6caa647507bdcc7034530604844d64408149a26b9e87c2f97650055c0f049abd1efc024b334635c01f29e0b632b371ac3f26130f4cf65997a
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10c0/b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.0, array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 10c0/74e1d2d996941c7a1badda9cabb7caab8c449db9086407cad8a1b71d2604cc8abf105db8ca4e02c04579ec58b7be40279ddb09aea4784832984485499f48432d
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.8":
  version: 3.1.8
  resolution: "array-includes@npm:3.1.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    is-string: "npm:^1.0.7"
  checksum: 10c0/5b1004d203e85873b96ddc493f090c9672fd6c80d7a60b798da8a14bff8a670ff95db5aafc9abc14a211943f05220dacf8ea17638ae0af1a6a47b8c0b48ce370
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 10c0/429897e68110374f39b771ec47a7161fc6a8fc33e196857c0a396dc75df0b5f65e4d046674db764330b6bb66b39ef48dd7c53b6a2ee75cfb0681e0c1a7033962
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlastindex@npm:1.2.5"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/962189487728b034f3134802b421b5f39e42ee2356d13b42d2ddb0e52057ffdcc170b9524867f4f0611a6f638f4c19b31e14606e8bcbda67799e26685b195aa3
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.2":
  version: 1.3.3
  resolution: "array.prototype.flat@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/d90e04dfbc43bb96b3d2248576753d1fb2298d2d972e29ca7ad5ec621f0d9e16ff8074dae647eac4f31f4fb7d3f561a7ac005fb01a71f51705a13b5af06a7d8a
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.2":
  version: 1.3.3
  resolution: "array.prototype.flatmap@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/ba899ea22b9dc9bf276e773e98ac84638ed5e0236de06f13d63a90b18ca9e0ec7c97d622d899796e3773930b946cd2413d098656c0c5d8cc58c6f25c21e6bd54
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 10c0/2f2459caa06ae0f7f615003f9104b01f6435cc803e11bd2a655107d52a1781dc040532dc44d93026b694cc18793993246237423e13a5337e86b43ed604932c06
  languageName: node
  linkType: hard

"asap@npm:^2.0.0":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: 10c0/c6d5e39fe1f15e4b87677460bd66b66050cd14c772269cee6688824c1410a08ab20254bb6784f9afb75af9144a9f9a7692d49547f4d19d715aeb7c0318f3136d
  languageName: node
  linkType: hard

"astronomia@npm:^4.1.0, astronomia@npm:^4.1.1":
  version: 4.1.1
  resolution: "astronomia@npm:4.1.1"
  checksum: 10c0/99f7d7dbe85d2afb7b48ca06c8e292e444912bbacc30a413e166faa4701e998e5ab16e800c9206102a89754f2550e2fd4eb5f36dc787fe18ed21ea12cbc01edb
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 10c0/669a32c2cb7e45091330c680e92eaeb791bc1d4132d827591e499cd1f776ff5a873e77e5f92d0ce795a8d60f10761dec9ddfe7225a5de680f5d357f67b1aac73
  languageName: node
  linkType: hard

"async@npm:^3.2.3, async@npm:^3.2.4":
  version: 3.2.6
  resolution: "async@npm:3.2.6"
  checksum: 10c0/36484bb15ceddf07078688d95e27076379cc2f87b10c03b6dd8a83e89475a3c8df5848859dd06a4c95af1e4c16fc973de0171a77f18ea00be899aca2a4f85e70
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: 10c0/d07226ef4f87daa01bd0fe80f8f310982e345f372926da2e5296aecc25c41cab440916bbaa4c5e1034b453af3392f67df5961124e4b586df1e99793a1374bdb2
  languageName: node
  linkType: hard

"aws-sdk@npm:^2.1333.0":
  version: 2.1692.0
  resolution: "aws-sdk@npm:2.1692.0"
  dependencies:
    buffer: "npm:4.9.2"
    events: "npm:1.1.1"
    ieee754: "npm:1.1.13"
    jmespath: "npm:0.16.0"
    querystring: "npm:0.2.0"
    sax: "npm:1.2.1"
    url: "npm:0.10.3"
    util: "npm:^0.12.4"
    uuid: "npm:8.0.0"
    xml2js: "npm:0.6.2"
  checksum: 10c0/5123174cf9c7952f9f072789f2a95f1cb346a676652425a8c73dcda195181f8a8d947f4edea0056552a315bbd5126ed8bb71d0a38b16f337d168bf7bf63a5b0a
  languageName: node
  linkType: hard

"axios@npm:^1.3.4, axios@npm:^1.5.1":
  version: 1.7.9
  resolution: "axios@npm:1.7.9"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10c0/b7a41e24b59fee5f0f26c1fc844b45b17442832eb3a0fb42dd4f1430eb4abc571fe168e67913e8a1d91c993232bd1d1ab03e20e4d1fee8c6147649b576fc1b0b
  languageName: node
  linkType: hard

"b4a@npm:^1.6.4":
  version: 1.6.7
  resolution: "b4a@npm:1.6.7"
  checksum: 10c0/ec2f004d1daae04be8c5a1f8aeb7fea213c34025e279db4958eb0b82c1729ee25f7c6e89f92a5f65c8a9cf2d017ce27e3dda912403341d1781bd74528a4849d4
  languageName: node
  linkType: hard

"babel-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "babel-jest@npm:29.7.0"
  dependencies:
    "@jest/transform": "npm:^29.7.0"
    "@types/babel__core": "npm:^7.1.14"
    babel-plugin-istanbul: "npm:^6.1.1"
    babel-preset-jest: "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    slash: "npm:^3.0.0"
  peerDependencies:
    "@babel/core": ^7.8.0
  checksum: 10c0/2eda9c1391e51936ca573dd1aedfee07b14c59b33dbe16ef347873ddd777bcf6e2fc739681e9e9661ab54ef84a3109a03725be2ac32cd2124c07ea4401cbe8c1
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^6.1.1":
  version: 6.1.1
  resolution: "babel-plugin-istanbul@npm:6.1.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@istanbuljs/load-nyc-config": "npm:^1.0.0"
    "@istanbuljs/schema": "npm:^0.1.2"
    istanbul-lib-instrument: "npm:^5.0.4"
    test-exclude: "npm:^6.0.0"
  checksum: 10c0/1075657feb705e00fd9463b329921856d3775d9867c5054b449317d39153f8fbcebd3e02ebf00432824e647faff3683a9ca0a941325ef1afe9b3c4dd51b24beb
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-plugin-jest-hoist@npm:29.6.3"
  dependencies:
    "@babel/template": "npm:^7.3.3"
    "@babel/types": "npm:^7.3.3"
    "@types/babel__core": "npm:^7.1.14"
    "@types/babel__traverse": "npm:^7.0.6"
  checksum: 10c0/7e6451caaf7dce33d010b8aafb970e62f1b0c0b57f4978c37b0d457bbcf0874d75a395a102daf0bae0bd14eafb9f6e9a165ee5e899c0a4f1f3bb2e07b304ed2e
  languageName: node
  linkType: hard

"babel-preset-current-node-syntax@npm:^1.0.0":
  version: 1.1.0
  resolution: "babel-preset-current-node-syntax@npm:1.1.0"
  dependencies:
    "@babel/plugin-syntax-async-generators": "npm:^7.8.4"
    "@babel/plugin-syntax-bigint": "npm:^7.8.3"
    "@babel/plugin-syntax-class-properties": "npm:^7.12.13"
    "@babel/plugin-syntax-class-static-block": "npm:^7.14.5"
    "@babel/plugin-syntax-import-attributes": "npm:^7.24.7"
    "@babel/plugin-syntax-import-meta": "npm:^7.10.4"
    "@babel/plugin-syntax-json-strings": "npm:^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators": "npm:^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator": "npm:^7.8.3"
    "@babel/plugin-syntax-numeric-separator": "npm:^7.10.4"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-chaining": "npm:^7.8.3"
    "@babel/plugin-syntax-private-property-in-object": "npm:^7.14.5"
    "@babel/plugin-syntax-top-level-await": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/0b838d4412e3322cb4436f246e24e9c00bebcedfd8f00a2f51489db683bd35406bbd55a700759c28d26959c6e03f84dd6a1426f576f440267c1d7a73c5717281
  languageName: node
  linkType: hard

"babel-preset-jest@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-preset-jest@npm:29.6.3"
  dependencies:
    babel-plugin-jest-hoist: "npm:^29.6.3"
    babel-preset-current-node-syntax: "npm:^1.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/ec5fd0276b5630b05f0c14bb97cc3815c6b31600c683ebb51372e54dcb776cff790bdeeabd5b8d01ede375a040337ccbf6a3ccd68d3a34219125945e167ad943
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"bare-events@npm:^2.2.0":
  version: 2.5.4
  resolution: "bare-events@npm:2.5.4"
  checksum: 10c0/877a9cea73d545e2588cdbd6fd01653e27dac48ad6b44985cdbae73e1f57f292d4ba52e25d1fba53674c1053c463d159f3d5c7bc36a2e6e192e389b499ddd627
  languageName: node
  linkType: hard

"base64-js@npm:0.0.8":
  version: 0.0.8
  resolution: "base64-js@npm:0.0.8"
  checksum: 10c0/60f02a9fdbbbb251c0a1064834d4062f5a3c4237edd9f0313282d75980a80ce303316795f7a80c8e240e524169644d88445ec0697b03f81ab9f4458090979375
  languageName: node
  linkType: hard

"base64-js@npm:1.3.1":
  version: 1.3.1
  resolution: "base64-js@npm:1.3.1"
  checksum: 10c0/f111a2c2b105eb9ee3818c30154e047fb00370699a03c2130d0944f10914697677ceb5faec64ea851e00710b80539afb03e9aa098e19c183a5e7a1cdc18abb29
  languageName: node
  linkType: hard

"base64-js@npm:^1.0.2, base64-js@npm:^1.1.2, base64-js@npm:^1.3.0, base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"bcrypt@npm:^5.1.1":
  version: 5.1.1
  resolution: "bcrypt@npm:5.1.1"
  dependencies:
    "@mapbox/node-pre-gyp": "npm:^1.0.11"
    node-addon-api: "npm:^5.0.0"
  checksum: 10c0/743231158c866bddc46f25eb8e9617fe38bc1a6f5f3052aba35e361d349b7f8fb80e96b45c48a4c23c45c29967ccd11c81cf31166454fc0ab019801c336cab40
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10c0/75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"bintrees@npm:1.0.2":
  version: 1.0.2
  resolution: "bintrees@npm:1.0.2"
  checksum: 10c0/132944b20c93c1a8f97bf8aa25980a76c6eb4291b7f2df2dbcd01cb5b417c287d3ee0847c7260c9f05f3d5a4233aaa03dec95114e97f308abe9cc3f72bed4a44
  languageName: node
  linkType: hard

"bl@npm:^4.1.0":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: "npm:^5.5.0"
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^3.4.0"
  checksum: 10c0/02847e1d2cb089c9dc6958add42e3cdeaf07d13f575973963335ac0fdece563a50ac770ac4c8fa06492d2dd276f6cc3b7f08c7cd9c7a7ad0f8d388b2a28def5f
  languageName: node
  linkType: hard

"body-parser@npm:1.20.1":
  version: 1.20.1
  resolution: "body-parser@npm:1.20.1"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.4"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.11.0"
    raw-body: "npm:2.5.1"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 10c0/a202d493e2c10a33fb7413dac7d2f713be579c4b88343cd814b6df7a38e5af1901fc31044e04de176db56b16d9772aa25a7723f64478c20f4d91b1ac223bf3b8
  languageName: node
  linkType: hard

"body-parser@npm:1.20.2":
  version: 1.20.2
  resolution: "body-parser@npm:1.20.2"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.11.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 10c0/06f1438fff388a2e2354c96aa3ea8147b79bfcb1262dfcc2aae68ec13723d01d5781680657b74e9f83c808266d5baf52804032fbde2b7382b89bd8cdb273ace9
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10c0/e4b53deb4f2b85c52be0e21a273f2045c7b6a6ea002b0e139c744cb6f95e9ec044439a52883b0d74dedd1ff3da55ed140cfdddfed7fb0cccbed373de5dce1bcf
  languageName: node
  linkType: hard

"bowser@npm:^2.11.0":
  version: 2.11.0
  resolution: "bowser@npm:2.11.0"
  checksum: 10c0/04efeecc7927a9ec33c667fa0965dea19f4ac60b3fea60793c2e6cf06c1dcd2f7ae1dbc656f450c5f50783b1c75cf9dc173ba6f3b7db2feee01f8c4b793e1bd3
  languageName: node
  linkType: hard

"boxen@npm:5.1.2":
  version: 5.1.2
  resolution: "boxen@npm:5.1.2"
  dependencies:
    ansi-align: "npm:^3.0.0"
    camelcase: "npm:^6.2.0"
    chalk: "npm:^4.1.0"
    cli-boxes: "npm:^2.2.1"
    string-width: "npm:^4.2.2"
    type-fest: "npm:^0.20.2"
    widest-line: "npm:^3.1.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/71f31c2eb3dcacd5fce524ae509e0cc90421752e0bfbd0281fd3352871d106c462a0f810c85f2fdb02f3a9fab2d7a84e9718b4999384d651b76104ebe5d2c024
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10c0/7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"brotli@npm:^1.2.0, brotli@npm:^1.3.2":
  version: 1.3.3
  resolution: "brotli@npm:1.3.3"
  dependencies:
    base64-js: "npm:^1.1.2"
  checksum: 10c0/9d24e24f8b7eabf44af034ed5f7d5530008b835f09a107a84ac060723e86dd43c6aa68958691fe5df524f59473b35f5ce2e0854aa1152c0a254d1010f51bcf22
  languageName: node
  linkType: hard

"browserslist@npm:^4.14.5, browserslist@npm:^4.24.0":
  version: 4.24.4
  resolution: "browserslist@npm:4.24.4"
  dependencies:
    caniuse-lite: "npm:^1.0.30001688"
    electron-to-chromium: "npm:^1.5.73"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.1"
  bin:
    browserslist: cli.js
  checksum: 10c0/db7ebc1733cf471e0b490b4f47e3e2ea2947ce417192c9246644e92c667dd56a71406cc58f62ca7587caf828364892e9952904a02b7aead752bc65b62a37cfe9
  languageName: node
  linkType: hard

"bs-logger@npm:^0.2.6":
  version: 0.2.6
  resolution: "bs-logger@npm:0.2.6"
  dependencies:
    fast-json-stable-stringify: "npm:2.x"
  checksum: 10c0/80e89aaaed4b68e3374ce936f2eb097456a0dddbf11f75238dbd53140b1e39259f0d248a5089ed456f1158984f22191c3658d54a713982f676709fbe1a6fa5a0
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: "npm:^0.4.0"
  checksum: 10c0/24d8dfb7b6d457d73f32744e678a60cc553e4ec0e9e1a01cf614b44d85c3c87e188d3cc78ef0442ce5032ee6818de20a0162ba1074725c0d08908f62ea979227
  languageName: node
  linkType: hard

"buffer-crc32@npm:^0.2.1":
  version: 0.2.13
  resolution: "buffer-crc32@npm:0.2.13"
  checksum: 10c0/cb0a8ddf5cf4f766466db63279e47761eb825693eeba6a5a95ee4ec8cb8f81ede70aa7f9d8aeec083e781d47154290eb5d4d26b3f7a465ec57fb9e7d59c47150
  languageName: node
  linkType: hard

"buffer-equal-constant-time@npm:1.0.1":
  version: 1.0.1
  resolution: "buffer-equal-constant-time@npm:1.0.1"
  checksum: 10c0/fb2294e64d23c573d0dd1f1e7a466c3e978fe94a4e0f8183937912ca374619773bef8e2aceb854129d2efecbbc515bbd0cc78d2734a3e3031edb0888531bbc8e
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10c0/124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer-shims@npm:^1.0.0":
  version: 1.0.0
  resolution: "buffer-shims@npm:1.0.0"
  checksum: 10c0/f93dfc71dd29877ed10ae19dfa2436665bcf385bb2053b9804a4a9e5ae5274578ee02c79aad32de9c93034e57ae724917dc975a37ba73fddd0137eabe2d7dd33
  languageName: node
  linkType: hard

"buffer@npm:4.9.2":
  version: 4.9.2
  resolution: "buffer@npm:4.9.2"
  dependencies:
    base64-js: "npm:^1.0.2"
    ieee754: "npm:^1.1.4"
    isarray: "npm:^1.0.0"
  checksum: 10c0/dc443d7e7caab23816b58aacdde710b72f525ad6eecd7d738fcaa29f6d6c12e8d9c13fed7219fd502be51ecf0615f5c077d4bdc6f9308dde2e53f8e5393c5b21
  languageName: node
  linkType: hard

"buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.1.13"
  checksum: 10c0/27cac81cff434ed2876058d72e7c4789d11ff1120ef32c9de48f59eab58179b66710c488987d295ae89a228f835fc66d088652dffeb8e3ba8659f80eb091d55e
  languageName: node
  linkType: hard

"buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.2.1"
  checksum: 10c0/2a905fbbcde73cc5d8bd18d1caa23715d5f83a5935867c2329f0ac06104204ba7947be098fe1317fbd8830e26090ff8e764f08cd14fefc977bb248c3487bcbd0
  languageName: node
  linkType: hard

"bundle-name@npm:^4.1.0":
  version: 4.1.0
  resolution: "bundle-name@npm:4.1.0"
  dependencies:
    run-applescript: "npm:^7.0.0"
  checksum: 10c0/8e575981e79c2bcf14d8b1c027a3775c095d362d1382312f444a7c861b0e21513c0bd8db5bd2b16e50ba0709fa622d4eab6b53192d222120305e68359daece29
  languageName: node
  linkType: hard

"busboy@npm:^1.0.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: "npm:^1.1.0"
  checksum: 10c0/fa7e836a2b82699b6e074393428b91ae579d4f9e21f5ac468e1b459a244341d722d2d22d10920cdd849743dbece6dca11d72de939fb75a7448825cf2babfba1f
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10c0/76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"cache-manager@npm:^5.1.7":
  version: 5.7.6
  resolution: "cache-manager@npm:5.7.6"
  dependencies:
    eventemitter3: "npm:^5.0.1"
    lodash.clonedeep: "npm:^4.5.0"
    lru-cache: "npm:^10.2.2"
    promise-coalesce: "npm:^1.1.2"
  checksum: 10c0/fdf99c4cb3682187fd57f5b7053573dae91800f57f785c7644e5b923d4146833ab600759678eb1a5a94be74db9301850a0ff4391fc97cffd89a0f13ae4358175
  languageName: node
  linkType: hard

"caldate@npm:^2.0.5":
  version: 2.0.5
  resolution: "caldate@npm:2.0.5"
  dependencies:
    moment-timezone: "npm:^0.5.43"
  checksum: 10c0/82743439c538982a9751be3ddf153784e765a709391c3342d6328b7f86aee230f7c50cda295020d209434fc3e8c57e0ea9f1b53832b77a9909c2c428b51613a5
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1":
  version: 1.0.1
  resolution: "call-bind-apply-helpers@npm:1.0.1"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/acb2ab68bf2718e68a3e895f0d0b73ccc9e45b9b6f210f163512ba76f91dab409eb8792f6dae188356f9095747512a3101646b3dea9d37fb8c7c6bf37796d18c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.2, call-bind@npm:^1.0.5, call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10c0/a13819be0681d915144467741b69875ae5f4eba8961eb0bf322aab63ec87f8250eb6d6b0dcbb2e1349876412a56129ca338592b3829ef4343527f5f18a0752d4
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3":
  version: 1.0.3
  resolution: "call-bound@npm:1.0.3"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/45257b8e7621067304b30dbd638e856cac913d31e8e00a80d6cf172911acd057846572d0b256b45e652d515db6601e2974a1b1a040e91b4fc36fb3dd86fa69cf
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10c0/f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 10c0/92ff9b443bfe8abb15f2b1513ca182d16126359ad4f955ebc83dc4ddcc4ef3fdd2c078bc223f2673dc223488e75c99b16cc4d056624374b799e6a1555cf61b23
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10c0/0d701658219bd3116d12da3eab31acddb3f9440790c0792e0d398f0a520a6a4058018e546862b6fba89d7ae990efaeb97da71e1913e9ebf5a8b5621a3d55c710
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001688":
  version: 1.0.30001697
  resolution: "caniuse-lite@npm:1.0.30001697"
  checksum: 10c0/c114045be1bb5e610ca14c619157472910401bf59cc4d3050cc64ecd6887345ae8f685a5474c126755fabbd6e02f899b3b743218ab46dcbd5b6880607b67e18e
  languageName: node
  linkType: hard

"chalk@npm:4.1.2, chalk@npm:^4.0.0, chalk@npm:^4.0.2, chalk@npm:^4.1.0, chalk@npm:^4.1.1, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"char-regex@npm:^1.0.2":
  version: 1.0.2
  resolution: "char-regex@npm:1.0.2"
  checksum: 10c0/57a09a86371331e0be35d9083ba429e86c4f4648ecbe27455dbfb343037c16ee6fdc7f6b61f433a57cc5ded5561d71c56a150e018f40c2ffb7bc93a26dae341e
  languageName: node
  linkType: hard

"chardet@npm:^0.7.0":
  version: 0.7.0
  resolution: "chardet@npm:0.7.0"
  checksum: 10c0/96e4731b9ec8050cbb56ab684e8c48d6c33f7826b755802d14e3ebfdc51c57afeece3ea39bc6b09acc359e4363525388b915e16640c1378053820f5e70d0f27d
  languageName: node
  linkType: hard

"check-disk-space@npm:3.4.0":
  version: 3.4.0
  resolution: "check-disk-space@npm:3.4.0"
  checksum: 10c0/cc39c91e1337e974fb5069c2fbd9eb92aceca6e35f3da6863a4eada58f15c1bf6970055bffed1e41c15cde1fd0ad2580bb99bef8275791ed56d69947f8657aa5
  languageName: node
  linkType: hard

"cheerio-select@npm:^2.1.0":
  version: 2.1.0
  resolution: "cheerio-select@npm:2.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-select: "npm:^5.1.0"
    css-what: "npm:^6.1.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.0.1"
  checksum: 10c0/2242097e593919dba4aacb97d7b8275def8b9ec70b00aa1f43335456870cfc9e284eae2080bdc832ed232dabb9eefcf56c722d152da4a154813fb8814a55d282
  languageName: node
  linkType: hard

"cheerio@npm:^1.0.0-rc.12":
  version: 1.0.0
  resolution: "cheerio@npm:1.0.0"
  dependencies:
    cheerio-select: "npm:^2.1.0"
    dom-serializer: "npm:^2.0.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.1.0"
    encoding-sniffer: "npm:^0.2.0"
    htmlparser2: "npm:^9.1.0"
    parse5: "npm:^7.1.2"
    parse5-htmlparser2-tree-adapter: "npm:^7.0.0"
    parse5-parser-stream: "npm:^7.1.2"
    undici: "npm:^6.19.5"
    whatwg-mimetype: "npm:^4.0.0"
  checksum: 10c0/d0e16925d9c36c879edfaef1c0244c866375a4c7b8d6ccd7ae0ad42da7d26263ea1a3c17b9a1aa5965918deeff2d40ac2e7223824f8e6eca972df3b81316a09f
  languageName: node
  linkType: hard

"chokidar@npm:3.5.3":
  version: 3.5.3
  resolution: "chokidar@npm:3.5.3"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/1076953093e0707c882a92c66c0f56ba6187831aa51bb4de878c1fec59ae611a3bf02898f190efec8e77a086b8df61c2b2a3ea324642a0558bdf8ee6c5dc9ca1
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.3":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/8361dcd013f2ddbe260eacb1f3cb2f2c6f2b0ad118708a343a5ed8158941a39cb8fb1d272e0f389712e74ee90ce8ba864eece9e0e62b9705cb468a2f6d917462
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: 10c0/594754e1303672171cc04e50f6c398ae16128eb134a88f801bf5354fd96f205320f23536a045d9abd8b51024a149696e51231565891d4efdab8846021ecf88e6
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.4
  resolution: "chrome-trace-event@npm:1.0.4"
  checksum: 10c0/3058da7a5f4934b87cf6a90ef5fb68ebc5f7d06f143ed5a4650208e5d7acae47bc03ec844b29fbf5ba7e46e8daa6acecc878f7983a4f4bb7271593da91e61ff5
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0":
  version: 3.9.0
  resolution: "ci-info@npm:3.9.0"
  checksum: 10c0/6f0109e36e111684291d46123d491bc4e7b7a1934c3a20dea28cba89f1d4a03acd892f5f6a81ed3855c38647e285a150e3c9ba062e38943bef57fee6c1554c3a
  languageName: node
  linkType: hard

"cjs-module-lexer@npm:^1.0.0":
  version: 1.4.3
  resolution: "cjs-module-lexer@npm:1.4.3"
  checksum: 10c0/076b3af85adc4d65dbdab1b5b240fe5b45d44fcf0ef9d429044dd94d19be5589376805c44fb2d4b3e684e5fe6a9b7cf3e426476a6507c45283c5fc6ff95240be
  languageName: node
  linkType: hard

"class-transformer@npm:^0.5.1":
  version: 0.5.1
  resolution: "class-transformer@npm:0.5.1"
  checksum: 10c0/19809914e51c6db42c036166839906420bb60367df14e15f49c45c8c1231bf25ae661ebe94736ee29cc688b77101ef851a8acca299375cc52fc141b64acde18a
  languageName: node
  linkType: hard

"class-validator@npm:^0.14.0":
  version: 0.14.1
  resolution: "class-validator@npm:0.14.1"
  dependencies:
    "@types/validator": "npm:^13.11.8"
    libphonenumber-js: "npm:^1.10.53"
    validator: "npm:^13.9.0"
  checksum: 10c0/946e914e47548b5081449c720ea6a4877bac63dc960e14fca4b990b56e64efe3802d12f07ec22d6420c290245b72ea2d646939239f2a3b597794e6c4c2a4f2ae
  languageName: node
  linkType: hard

"cli-boxes@npm:^2.2.1":
  version: 2.2.1
  resolution: "cli-boxes@npm:2.2.1"
  checksum: 10c0/6111352edbb2f62dbc7bfd58f2d534de507afed7f189f13fa894ce5a48badd94b2aa502fda28f1d7dd5f1eb456e7d4033d09a76660013ef50c7f66e7a034f050
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: "npm:^3.1.0"
  checksum: 10c0/92a2f98ff9037d09be3dfe1f0d749664797fb674bf388375a2207a1203b69d41847abf16434203e0089212479e47a358b13a0222ab9fccfe8e2644a7ccebd111
  languageName: node
  linkType: hard

"cli-highlight@npm:^2.1.11":
  version: 2.1.11
  resolution: "cli-highlight@npm:2.1.11"
  dependencies:
    chalk: "npm:^4.0.0"
    highlight.js: "npm:^10.7.1"
    mz: "npm:^2.4.0"
    parse5: "npm:^5.1.1"
    parse5-htmlparser2-tree-adapter: "npm:^6.0.0"
    yargs: "npm:^16.0.0"
  bin:
    highlight: bin/highlight
  checksum: 10c0/b5b4af3b968aa9df77eee449a400fbb659cf47c4b03a395370bd98d5554a00afaa5819b41a9a8a1ca0d37b0b896a94e57c65289b37359a25b700b1f56eb04852
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.5.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 10c0/907a1c227ddf0d7a101e7ab8b300affc742ead4b4ebe920a5bf1bc6d45dce2958fcd195eb28fa25275062fe6fa9b109b93b63bc8033396ed3bcb50297008b3a3
  languageName: node
  linkType: hard

"cli-table3@npm:0.6.3":
  version: 0.6.3
  resolution: "cli-table3@npm:0.6.3"
  dependencies:
    "@colors/colors": "npm:1.5.0"
    string-width: "npm:^4.2.0"
  dependenciesMeta:
    "@colors/colors":
      optional: true
  checksum: 10c0/39e580cb346c2eaf1bd8f4ff055ae644e902b8303c164a1b8894c0dc95941f92e001db51f49649011be987e708d9fa3183ccc2289a4d376a057769664048cc0c
  languageName: node
  linkType: hard

"cli-width@npm:^3.0.0":
  version: 3.0.0
  resolution: "cli-width@npm:3.0.0"
  checksum: 10c0/125a62810e59a2564268c80fdff56c23159a7690c003e34aeb2e68497dccff26911998ff49c33916fcfdf71e824322cc3953e3f7b48b27267c7a062c81348a9a
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/6035f5daf7383470cef82b3d3db00bec70afb3423538c50394386ffbbab135e26c3689c41791f911fa71b62d13d3863c712fdd70f0fbdffd938a1e6fd09aac00
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/4bda0f09c340cbb6dfdc1ed508b3ca080f12992c18d68c6be4d9cf51756033d5266e61ec57529e610dacbf4da1c634423b0c1b11037709cc6b09045cbd815df5
  languageName: node
  linkType: hard

"clone@npm:^1.0.2, clone@npm:^1.0.4":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: 10c0/2176952b3649293473999a95d7bebfc9dc96410f6cbd3d2595cf12fd401f63a4bf41a7adbfd3ab2ff09ed60cb9870c58c6acdd18b87767366fabfc163700f13b
  languageName: node
  linkType: hard

"clone@npm:^2.1.2":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: 10c0/ed0601cd0b1606bc7d82ee7175b97e68d1dd9b91fd1250a3617b38d34a095f8ee0431d40a1a611122dcccb4f93295b4fdb94942aa763392b5fe44effa50c2d5e
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: 10c0/c0e85ea0ca8bf0a50cbdca82efc5af0301240ca88ebe3644a6ffb8ffe911f34d40f8fbcf8f1d52c5ddd66706abd4d3bfcd64259f1e8e2371d4f47573b0dc8c28
  languageName: node
  linkType: hard

"collect-v8-coverage@npm:^1.0.0":
  version: 1.0.2
  resolution: "collect-v8-coverage@npm:1.0.2"
  checksum: 10c0/ed7008e2e8b6852c5483b444a3ae6e976e088d4335a85aa0a9db2861c5f1d31bd2d7ff97a60469b3388deeba661a619753afbe201279fb159b4b9548ab8269a1
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: "npm:^1.0.0"
    simple-swizzle: "npm:^0.2.2"
  checksum: 10c0/b0bfd74c03b1f837f543898b512f5ea353f71630ccdd0d66f83028d1f0924a7d4272deb278b9aef376cacf1289b522ac3fb175e99895283645a2dc3a33af2404
  languageName: node
  linkType: hard

"color-support@npm:^1.1.2":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 10c0/8ffeaa270a784dc382f62d9be0a98581db43e11eee301af14734a6d089bd456478b1a8b3e7db7ca7dc5b18a75f828f775c44074020b51c05fc00e6d0992b1cc6
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: "npm:^2.0.1"
    color-string: "npm:^1.9.0"
  checksum: 10c0/7fbe7cfb811054c808349de19fb380252e5e34e61d7d168ec3353e9e9aacb1802674bddc657682e4e9730c2786592a4de6f8283e7e0d3870b829bb0b7b2f6118
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"commander@npm:4.1.1":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: 10c0/84a76c08fe6cc08c9c93f62ac573d2907d8e79138999312c92d4155bc2325d487d64d13f669b2000c9f8caf70493c1be2dac74fec3c51d5a04f8bc3ae1830bab
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10c0/74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"component-emitter@npm:^1.3.0":
  version: 1.3.1
  resolution: "component-emitter@npm:1.3.1"
  checksum: 10c0/e4900b1b790b5e76b8d71b328da41482118c0f3523a516a41be598dc2785a07fd721098d9bf6e22d89b19f4fa4e1025160dc00317ea111633a3e4f75c2b86032
  languageName: node
  linkType: hard

"compress-commons@npm:^5.0.1":
  version: 5.0.3
  resolution: "compress-commons@npm:5.0.3"
  dependencies:
    crc-32: "npm:^1.2.0"
    crc32-stream: "npm:^5.0.0"
    normalize-path: "npm:^3.0.0"
    readable-stream: "npm:^3.6.0"
  checksum: 10c0/ca7fe7ec4feb2854876df928192fc9b2bece15690e171d771a23a8e54a97ef78c057791d0fadc5c6c6703831687facd1f2428bb0dff3187caa2d631d92be69fc
  languageName: node
  linkType: hard

"compressible@npm:~2.0.18":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: "npm:>= 1.43.0 < 2"
  checksum: 10c0/8a03712bc9f5b9fe530cc5a79e164e665550d5171a64575d7dcf3e0395d7b4afa2d79ab176c61b5b596e28228b350dd07c1a2a6ead12fd81d1b6cd632af2fef7
  languageName: node
  linkType: hard

"compression@npm:^1.8.0":
  version: 1.8.0
  resolution: "compression@npm:1.8.0"
  dependencies:
    bytes: "npm:3.1.2"
    compressible: "npm:~2.0.18"
    debug: "npm:2.6.9"
    negotiator: "npm:~0.6.4"
    on-headers: "npm:~1.0.2"
    safe-buffer: "npm:5.2.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/804d3c8430939f4fd88e5128333f311b4035f6425a7f2959d74cfb5c98ef3a3e3e18143208f3f9d0fcae4cd3bcf3d2fbe525e0fcb955e6e146e070936f025a24
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"concat-stream@npm:^1.5.2":
  version: 1.6.2
  resolution: "concat-stream@npm:1.6.2"
  dependencies:
    buffer-from: "npm:^1.0.0"
    inherits: "npm:^2.0.3"
    readable-stream: "npm:^2.2.2"
    typedarray: "npm:^0.0.6"
  checksum: 10c0/2e9864e18282946dabbccb212c5c7cec0702745e3671679eb8291812ca7fd12023f7d8cb36493942a62f770ac96a7f90009dc5c82ad69893438371720fa92617
  languageName: node
  linkType: hard

"consola@npm:^2.15.0":
  version: 2.15.3
  resolution: "consola@npm:2.15.3"
  checksum: 10c0/34a337e6b4a1349ee4d7b4c568484344418da8fdb829d7d71bfefcd724f608f273987633b6eef465e8de510929907a092e13cb7a28a5d3acb3be446fcc79fd5e
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.0.0, console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 10c0/7ab51d30b52d461412cd467721bb82afe695da78fff8f29fe6f6b9cbaac9a2328e27a22a966014df9532100f6dd85370460be8130b9c677891ba36d96a343f50
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10c0/bac0316ebfeacb8f381b38285dc691c9939bf0a78b0b7c2d5758acadad242d04783cee5337ba7d12a565a19075af1b3c11c728e1e4946de73c6ff7ce45f3f1bb
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 10c0/b76ebed15c000aee4678c3707e0860cb6abd4e680a598c0a26e17f0bfae723ec9cc2802f0ff1bc6e4d80603719010431d2231018373d4dde10f9ccff9dadf5af
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: 10c0/b36fd0d4e3fef8456915fcf7742e58fbfcc12a17a018e0eb9501c9d5ef6893b596466f03b0564b81af29ff2538fd0aa4b9d54fe5ccbfb4c90ea50ad29fe2d221
  languageName: node
  linkType: hard

"cookie@npm:0.5.0":
  version: 0.5.0
  resolution: "cookie@npm:0.5.0"
  checksum: 10c0/c01ca3ef8d7b8187bae434434582288681273b5a9ed27521d4d7f9f7928fe0c920df0decd9f9d3bbd2d14ac432b8c8cf42b98b3bdd5bfe0e6edddeebebe8b61d
  languageName: node
  linkType: hard

"cookiejar@npm:^2.1.4":
  version: 2.1.4
  resolution: "cookiejar@npm:2.1.4"
  checksum: 10c0/2dae55611c6e1678f34d93984cbd4bda58f4fe3e5247cc4993f4a305cd19c913bbaf325086ed952e892108115073a747596453d3dc1c34947f47f731818b8ad1
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cors@npm:2.8.5":
  version: 2.8.5
  resolution: "cors@npm:2.8.5"
  dependencies:
    object-assign: "npm:^4"
    vary: "npm:^1"
  checksum: 10c0/373702b7999409922da80de4a61938aabba6929aea5b6fd9096fefb9e8342f626c0ebd7507b0e8b0b311380744cc985f27edebc0a26e0ddb784b54e1085de761
  languageName: node
  linkType: hard

"cosmiconfig@npm:^7.0.1":
  version: 7.1.0
  resolution: "cosmiconfig@npm:7.1.0"
  dependencies:
    "@types/parse-json": "npm:^4.0.0"
    import-fresh: "npm:^3.2.1"
    parse-json: "npm:^5.0.0"
    path-type: "npm:^4.0.0"
    yaml: "npm:^1.10.0"
  checksum: 10c0/b923ff6af581638128e5f074a5450ba12c0300b71302398ea38dbeabd33bbcaa0245ca9adbedfcf284a07da50f99ede5658c80bb3e39e2ce770a99d28a21ef03
  languageName: node
  linkType: hard

"crc-32@npm:^1.2.0":
  version: 1.2.2
  resolution: "crc-32@npm:1.2.2"
  bin:
    crc32: bin/crc32.njs
  checksum: 10c0/11dcf4a2e77ee793835d49f2c028838eae58b44f50d1ff08394a610bfd817523f105d6ae4d9b5bef0aad45510f633eb23c903e9902e4409bed1ce70cb82b9bf0
  languageName: node
  linkType: hard

"crc32-stream@npm:^5.0.0":
  version: 5.0.1
  resolution: "crc32-stream@npm:5.0.1"
  dependencies:
    crc-32: "npm:^1.2.0"
    readable-stream: "npm:^3.4.0"
  checksum: 10c0/32fdffdd6e80f08ffef03a120a23fad7fdd04bd9c386dd8b9c8d27f58b32b78f6a1f43a327812858a0237aec72d55b77e33f5229cbbc0ee4856a71ea010c6aa8
  languageName: node
  linkType: hard

"create-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "create-jest@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    exit: "npm:^0.1.2"
    graceful-fs: "npm:^4.2.9"
    jest-config: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    prompts: "npm:^2.0.1"
  bin:
    create-jest: bin/create-jest.js
  checksum: 10c0/e7e54c280692470d3398f62a6238fd396327e01c6a0757002833f06d00afc62dd7bfe04ff2b9cd145264460e6b4d1eb8386f2925b7e567f97939843b7b0e812f
  languageName: node
  linkType: hard

"create-require@npm:^1.1.0":
  version: 1.1.1
  resolution: "create-require@npm:1.1.1"
  checksum: 10c0/157cbc59b2430ae9a90034a5f3a1b398b6738bf510f713edc4d4e45e169bc514d3d99dd34d8d01ca7ae7830b5b8b537e46ae8f3c8f932371b0875c0151d7ec91
  languageName: node
  linkType: hard

"cron@npm:2.3.1":
  version: 2.3.1
  resolution: "cron@npm:2.3.1"
  dependencies:
    luxon: "npm:^3.2.1"
  checksum: 10c0/5b287cdbc42356173295fbfd755ac1095e1b5330a28913b2d5d43b7e99e9236c05237a21e3201bf72748c9722d88324724d1f2403b8e2701875ff8612004a82f
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.3":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"crypto-js@npm:^4.2.0":
  version: 4.2.0
  resolution: "crypto-js@npm:4.2.0"
  checksum: 10c0/8fbdf9d56f47aea0794ab87b0eb9833baf80b01a7c5c1b0edc7faf25f662fb69ab18dc2199e2afcac54670ff0cd9607a9045a3f7a80336cccd18d77a55b9fdf0
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-select@npm:5.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.1.0"
    domhandler: "npm:^5.0.2"
    domutils: "npm:^3.0.1"
    nth-check: "npm:^2.0.1"
  checksum: 10c0/551c60dba5b54054741032c1793b5734f6ba45e23ae9e82761a3c0ed1acbb8cfedfa443aaba3a3c1a54cac12b456d2012a09d2cd5f0e82e430454c1b9d84d500
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: 10c0/a09f5a6b14ba8dcf57ae9a59474722e80f20406c53a61e9aedb0eedc693b135113ffe2983f4efc4b5065ae639442e9ae88df24941ef159c218b231011d733746
  languageName: node
  linkType: hard

"cssstyle@npm:^4.2.1":
  version: 4.3.1
  resolution: "cssstyle@npm:4.3.1"
  dependencies:
    "@asamuzakjp/css-color": "npm:^3.1.2"
    rrweb-cssom: "npm:^0.8.0"
  checksum: 10c0/89d73252d5f9930cf67f5c576de8030a9d960aae4c8bdd42d60464b2f67c8d809601fb7e620b43d4c84e03472016da77528df9a21a21393387ed256610ca0ab4
  languageName: node
  linkType: hard

"csv-parser@npm:^3.0.0":
  version: 3.2.0
  resolution: "csv-parser@npm:3.2.0"
  bin:
    csv-parser: bin/csv-parser
  checksum: 10c0/650769916607dae9679187803c71ef70d3b724cfb18e4adff187167b282faf18ac7c14a318c4ea6e92ae3483d3ed4b8d92ff213e77979e3049a5c0029034ac65
  languageName: node
  linkType: hard

"dat-backend@workspace:.":
  version: 0.0.0-use.local
  resolution: "dat-backend@workspace:."
  dependencies:
    "@aws-sdk/client-s3": "npm:^3.454.0"
    "@azure/identity": "npm:^4.10.0"
    "@googlemaps/google-maps-services-js": "npm:^3.4.0"
    "@nestjs/axios": "npm:^2.0.0"
    "@nestjs/cache-manager": "npm:^2.2.2"
    "@nestjs/cli": "npm:^9.2.0"
    "@nestjs/common": "npm:^9.3.9"
    "@nestjs/core": "npm:^9.3.9"
    "@nestjs/jwt": "npm:^10.1.0"
    "@nestjs/platform-express": "npm:^9.3.9"
    "@nestjs/schedule": "npm:^2.1.0"
    "@nestjs/schematics": "npm:^9.0.4"
    "@nestjs/swagger": "npm:^6.2.1"
    "@nestjs/terminus": "npm:10"
    "@nestjs/testing": "npm:^9.3.9"
    "@nestjs/typeorm": "npm:^9.0.1"
    "@types/adm-zip": "npm:^0.5.0"
    "@types/archiver": "npm:^6.0.2"
    "@types/cache-manager": "npm:^4.0.2"
    "@types/cheerio": "npm:^0.22.35"
    "@types/compression": "npm:^1"
    "@types/express": "npm:^4.17.21"
    "@types/handlebars": "npm:^4.1.0"
    "@types/hjson": "npm:^2.4.3"
    "@types/html-to-pdfmake": "npm:^2.4.4"
    "@types/html-to-text": "npm:^9"
    "@types/jest": "npm:^29.5.12"
    "@types/jsdom": "npm:^21.1.7"
    "@types/jsonwebtoken": "npm:^9.0.1"
    "@types/lodash": "npm:^4.14.167"
    "@types/luxon": "npm:^3"
    "@types/multer": "npm:^1.4.11"
    "@types/node": "npm:^18.15.0"
    "@types/papaparse": "npm:^5.3.2"
    "@types/pdfkit": "npm:^0.13.4"
    "@types/pdfmake": "npm:^0"
    "@types/ramda": "npm:^0.28.23"
    "@types/supertest": "npm:^2.0.12"
    "@types/uuid": "npm:^9.0.1"
    "@typescript-eslint/eslint-plugin": "npm:^5.54.1"
    "@typescript-eslint/parser": "npm:^5.54.1"
    adm-zip: "npm:^0.5.16"
    api2dat5: "npm:^1.2.2"
    archiver: "npm:^6.0.1"
    aws-sdk: "npm:^2.1333.0"
    axios: "npm:^1.3.4"
    bcrypt: "npm:^5.1.1"
    cache-manager: "npm:^5.1.7"
    cheerio: "npm:^1.0.0-rc.12"
    class-transformer: "npm:^0.5.1"
    class-validator: "npm:^0.14.0"
    compression: "npm:^1.8.0"
    csv-parser: "npm:^3.0.0"
    date-fns: "npm:^2.29.3"
    date-holidays: "npm:^3.24.3"
    deep-object-diff: "npm:^1.1.9"
    dotenv: "npm:^16.0.0"
    eslint: "npm:^8.35.0"
    eslint-config-prettier: "npm:^8.7.0"
    eslint-plugin-import: "npm:^2.27.5"
    eslint-plugin-prettier: "npm:^4.2.1"
    fast-xml-parser: "npm:^3.17.6"
    handlebars: "npm:^4.7.8"
    hjson: "npm:^3.2.2"
    html-to-pdfmake: "npm:^2.5.23"
    html-to-text: "npm:^9.0.5"
    image-size: "npm:^1.1.1"
    jest: "npm:^29.5.0"
    jsdom: "npm:^26.1.0"
    jsonwebtoken: "npm:^9.0.0"
    jszip: "npm:^3.10.1"
    lodash.merge: "npm:^4.6.2"
    luxon: "npm:^3.5.0"
    nestjs-spelunker: "npm:^1.1.5"
    nodemailer: "npm:^6.9.14"
    openai: "npm:^4.67.1"
    papaparse: "npm:^5.4.0"
    pdf-lib: "npm:^1.17.1"
    pdfkit: "npm:^0.14.0"
    pdfmake: "npm:^0.2.18"
    pg: "npm:^8.10.0"
    prettier: "npm:^2.8.4"
    prom-client: "npm:^15.1.0"
    qs: "npm:^6.11.1"
    queue: "npm:^6.0.2"
    ramda: "npm:^0.28.0"
    raw-body: "npm:^3.0.0"
    rxjs: "npm:^7.8.0"
    sharp: "npm:^0.33.5"
    stream-array: "npm:^1.1.2"
    stream-chain: "npm:^2.2.5"
    stream-json: "npm:^1.8.0"
    supertest: "npm:^6.3.3"
    swagger-ui-express: "npm:^4.6.2"
    ts-jest: "npm:^29.0.5"
    ts-loader: "npm:^9.4.2"
    ts-node: "npm:^10.9.1"
    tsconfig-paths: "npm:^4.2.0"
    typeorm: "npm:^0.3.12"
    typescript: "npm:^4.9.5"
    uuid: "npm:^9.0.0"
  languageName: unknown
  linkType: soft

"data-urls@npm:^5.0.0":
  version: 5.0.0
  resolution: "data-urls@npm:5.0.0"
  dependencies:
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.0.0"
  checksum: 10c0/1b894d7d41c861f3a4ed2ae9b1c3f0909d4575ada02e36d3d3bc584bdd84278e20709070c79c3b3bff7ac98598cb191eb3e86a89a79ea4ee1ef360e1694f92ad
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/7986d40fc7979e9e6241f85db8d17060dd9a71bd53c894fa29d126061715e322a4cd47a00b0b8c710394854183d4120462b980b8554012acc1c0fa49df7ad38c
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/f8a4534b5c69384d95ac18137d381f18a5cfae1f0fc1df0ef6feef51ef0d568606d970b69e02ea186c6c0f0eac77fe4e6ad96fec2569cc86c3afcc7475068c55
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: 10c0/fa7aa40078025b7810dcffc16df02c480573b7b53ef1205aa6a61533011005c1890e5ba17018c692ce7c900212b547262d33279fde801ad9843edc0863bf78c4
  languageName: node
  linkType: hard

"date-bengali-revised@npm:^2.0.2":
  version: 2.0.2
  resolution: "date-bengali-revised@npm:2.0.2"
  checksum: 10c0/c0a83ed6333a7871385a1b1bdddc992f35c69af6ba1231d28562669992916fbc1672022313213ddb34df9478973b2a2b30a931967e55acfc0d9a8200d596fd40
  languageName: node
  linkType: hard

"date-chinese@npm:^2.1.4":
  version: 2.1.4
  resolution: "date-chinese@npm:2.1.4"
  dependencies:
    astronomia: "npm:^4.1.0"
  checksum: 10c0/1ab1e94d0dcb7fa524d0c4ce54249c3b341779870dbd9c26c008c2a17ae72668b109106cd7e9ac8d6e37d7d3bb796b48f7406b755d3aae3f1c06fdf760714610
  languageName: node
  linkType: hard

"date-easter@npm:^1.0.3":
  version: 1.0.3
  resolution: "date-easter@npm:1.0.3"
  checksum: 10c0/08734502a3264fb8c5073c22ab13e6624e7981293c72b5e3c37ea88bceae75db9ec8cd25443ea3729b096f5e062469d934f9ebf072a30229b313d1ba28e5d204
  languageName: node
  linkType: hard

"date-fns@npm:^2.29.3":
  version: 2.30.0
  resolution: "date-fns@npm:2.30.0"
  dependencies:
    "@babel/runtime": "npm:^7.21.0"
  checksum: 10c0/e4b521fbf22bc8c3db332bbfb7b094fd3e7627de0259a9d17c7551e2d2702608a7307a449206065916538e384f37b181565447ce2637ae09828427aed9cb5581
  languageName: node
  linkType: hard

"date-holidays-parser@npm:^3.4.7":
  version: 3.4.7
  resolution: "date-holidays-parser@npm:3.4.7"
  dependencies:
    astronomia: "npm:^4.1.1"
    caldate: "npm:^2.0.5"
    date-bengali-revised: "npm:^2.0.2"
    date-chinese: "npm:^2.1.4"
    date-easter: "npm:^1.0.3"
    deepmerge: "npm:^4.3.1"
    jalaali-js: "npm:^1.2.7"
    moment-timezone: "npm:^0.5.47"
  checksum: 10c0/2e65cf1fa0b350bf0a659729a8ce1ed3fc84789fb64a2b6e1ede68758c233ce7255bb493dd0ea558ef66685b14d777b9bd8319b8fca1d0541a7b5e53c66f5bd1
  languageName: node
  linkType: hard

"date-holidays@npm:^3.24.3":
  version: 3.24.3
  resolution: "date-holidays@npm:3.24.3"
  dependencies:
    date-holidays-parser: "npm:^3.4.7"
    js-yaml: "npm:^4.1.0"
    lodash: "npm:^4.17.21"
    prepin: "npm:^1.0.3"
  bin:
    holidays2json: scripts/holidays2json.cjs
  checksum: 10c0/291e2715e4464e275341fd566e9879d3592576426a52fccc004dab66f3d6862d28e5f25bdb1356516ac7d87a788dd3d100abf91d8454b765856918faed447ec6
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.9":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: 10c0/a3caf6ac8363c7dade9d1ee797848ddcf25c1ace68d9fe8678ecf8ba0675825430de5d793672ec87b24a69bf04a1544b176547b2539982275d5542a7955f35b7
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/db94f1a182bf886f57b4755f85b3a74c39b5114b9377b7ab375dc2cfa3454f09490cc6c30f829df3fc8042bc8b8995f6567ce5cd96f3bc3688bd24027197d9de
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 10c0/37d96ae42cbc71c14844d2ae3ba55adf462ec89fd3a999459dec3833944cd999af6007ff29c780f1c61153bcaaf2c842d1e4ce1ec621e4fc4923244942e4a02a
  languageName: node
  linkType: hard

"decimal.js@npm:^10.5.0":
  version: 10.5.0
  resolution: "decimal.js@npm:10.5.0"
  checksum: 10c0/785c35279df32762143914668df35948920b6c1c259b933e0519a69b7003fc0a5ed2a766b1e1dda02574450c566b21738a45f15e274b47c2ac02072c0d1f3ac3
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.2":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 10c0/1f4fa54eb740414a816b3f6c24818fbfcabd74ac478391e9f4e2282c994127db02010ce804f3d08e38255493cfe68608b3f5c8e09fd6efc4ae46c807691f7a31
  languageName: node
  linkType: hard

"dedent@npm:^1.0.0":
  version: 1.5.3
  resolution: "dedent@npm:1.5.3"
  peerDependencies:
    babel-plugin-macros: ^3.1.0
  peerDependenciesMeta:
    babel-plugin-macros:
      optional: true
  checksum: 10c0/d94bde6e6f780be4da4fd760288fcf755ec368872f4ac5218197200d86430aeb8d90a003a840bff1c20221188e3f23adced0119cb811c6873c70d0ac66d12832
  languageName: node
  linkType: hard

"deep-equal@npm:^1.0.0":
  version: 1.1.2
  resolution: "deep-equal@npm:1.1.2"
  dependencies:
    is-arguments: "npm:^1.1.1"
    is-date-object: "npm:^1.0.5"
    is-regex: "npm:^1.1.4"
    object-is: "npm:^1.1.5"
    object-keys: "npm:^1.1.1"
    regexp.prototype.flags: "npm:^1.5.1"
  checksum: 10c0/cd85d822d18e9b3e1532d0f6ba412d229aa9d22881d70da161674428ae96e47925191296f7cda29306bac252889007da40ed8449363bd1c96c708acb82068a00
  languageName: node
  linkType: hard

"deep-equal@npm:^2.0.5":
  version: 2.2.3
  resolution: "deep-equal@npm:2.2.3"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.0"
    call-bind: "npm:^1.0.5"
    es-get-iterator: "npm:^1.1.3"
    get-intrinsic: "npm:^1.2.2"
    is-arguments: "npm:^1.1.1"
    is-array-buffer: "npm:^3.0.2"
    is-date-object: "npm:^1.0.5"
    is-regex: "npm:^1.1.4"
    is-shared-array-buffer: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    object-is: "npm:^1.1.5"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.4"
    regexp.prototype.flags: "npm:^1.5.1"
    side-channel: "npm:^1.0.4"
    which-boxed-primitive: "npm:^1.0.2"
    which-collection: "npm:^1.0.1"
    which-typed-array: "npm:^1.1.13"
  checksum: 10c0/a48244f90fa989f63ff5ef0cc6de1e4916b48ea0220a9c89a378561960814794a5800c600254482a2c8fd2e49d6c2e196131dc983976adb024c94a42dfe4949f
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10c0/7f0ee496e0dff14a573dc6127f14c95061b448b87b995fc96c017ce0a1e66af1675e73f1d6064407975bc4ea6ab679497a29fff7b5b9c4e99cb10797c1ad0b4c
  languageName: node
  linkType: hard

"deep-object-diff@npm:^1.1.9":
  version: 1.1.9
  resolution: "deep-object-diff@npm:1.1.9"
  checksum: 10c0/12cfd1b000d16c9192fc649923c972f8aac2ddca4f71a292f8f2c1e2d5cf3c9c16c85e73ab3e7d8a89a5ec6918d6460677d0b05bd160f7bd50bb4816d496dc24
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2, deepmerge@npm:^4.3.1":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 10c0/e53481aaf1aa2c4082b5342be6b6d8ad9dfe387bc92ce197a66dea08bd4265904a087e75e464f14d1347cf2ac8afe1e4c16b266e0561cc5df29382d3c5f80044
  languageName: node
  linkType: hard

"default-browser-id@npm:^5.0.0":
  version: 5.0.0
  resolution: "default-browser-id@npm:5.0.0"
  checksum: 10c0/957fb886502594c8e645e812dfe93dba30ed82e8460d20ce39c53c5b0f3e2afb6ceaec2249083b90bdfbb4cb0f34e1f73fde3d68cac00becdbcfd894156b5ead
  languageName: node
  linkType: hard

"default-browser@npm:^5.2.1":
  version: 5.2.1
  resolution: "default-browser@npm:5.2.1"
  dependencies:
    bundle-name: "npm:^4.1.0"
    default-browser-id: "npm:^5.0.0"
  checksum: 10c0/73f17dc3c58026c55bb5538749597db31f9561c0193cd98604144b704a981c95a466f8ecc3c2db63d8bfd04fb0d426904834cfc91ae510c6aeb97e13c5167c4d
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: "npm:^1.0.2"
  checksum: 10c0/9cfbe498f5c8ed733775db62dfd585780387d93c17477949e1670bfcfb9346e0281ce8c4bf9f4ac1fc0f9b851113bd6dc9e41182ea1644ccd97de639fa13c35a
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10c0/dea0606d1483eb9db8d930d4eac62ca0fa16738b0b3e07046cddfacf7d8c868bbe13fa0cb263eb91c7d0d527960dc3f2f2471a69ed7816210307f6744fe62e37
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^3.0.0":
  version: 3.0.0
  resolution: "define-lazy-prop@npm:3.0.0"
  checksum: 10c0/5ab0b2bf3fa58b3a443140bbd4cd3db1f91b985cc8a246d330b9ac3fc0b6a325a6d82bddc0b055123d745b3f9931afeea74a5ec545439a1630b9c8512b0eeb49
  languageName: node
  linkType: hard

"define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/88a152319ffe1396ccc6ded510a3896e77efac7a1bfbaa174a7b00414a1747377e0bb525d303794a47cf30e805c2ec84e575758512c6e44a993076d29fd4e6c3
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: 10c0/ba05874b91148e1db4bf254750c042bf2215febd23a6d3cda2e64896aef79745fbd4b9996488bd3cafb39ce19dbce0fd6e3b6665275638befffe1c9b312b91b5
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10c0/58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10c0/bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.0, detect-libc@npm:^2.0.3":
  version: 2.0.3
  resolution: "detect-libc@npm:2.0.3"
  checksum: 10c0/88095bda8f90220c95f162bf92cad70bd0e424913e655c20578600e35b91edc261af27531cf160a331e185c0ced93944bc7e09939143225f56312d7fd800fdb7
  languageName: node
  linkType: hard

"detect-newline@npm:^3.0.0":
  version: 3.1.0
  resolution: "detect-newline@npm:3.1.0"
  checksum: 10c0/c38cfc8eeb9fda09febb44bcd85e467c970d4e3bf526095394e5a4f18bc26dd0cf6b22c69c1fa9969261521c593836db335c2795218f6d781a512aea2fb8209d
  languageName: node
  linkType: hard

"dezalgo@npm:^1.0.4":
  version: 1.0.4
  resolution: "dezalgo@npm:1.0.4"
  dependencies:
    asap: "npm:^2.0.0"
    wrappy: "npm:1"
  checksum: 10c0/8a870ed42eade9a397e6141fe5c025148a59ed52f1f28b1db5de216b4d57f0af7a257070c3af7ce3d5508c1ce9dd5009028a76f4b2cc9370dc56551d2355fad8
  languageName: node
  linkType: hard

"dfa@npm:^1.2.0":
  version: 1.2.0
  resolution: "dfa@npm:1.2.0"
  checksum: 10c0/ad12f0bc73b530876672e0a9dfbaa350eeff0c876580042734a004e462eca86d7749b9dedf6b067ba54f346137ab23d16615826bbfa424a3e01ab0e2786fad3c
  languageName: node
  linkType: hard

"diff-sequences@npm:^29.6.3":
  version: 29.6.3
  resolution: "diff-sequences@npm:29.6.3"
  checksum: 10c0/32e27ac7dbffdf2fb0eb5a84efd98a9ad084fbabd5ac9abb8757c6770d5320d2acd172830b28c4add29bb873d59420601dfc805ac4064330ce59b1adfd0593b2
  languageName: node
  linkType: hard

"diff@npm:^4.0.1":
  version: 4.0.2
  resolution: "diff@npm:4.0.2"
  checksum: 10c0/81b91f9d39c4eaca068eb0c1eb0e4afbdc5bb2941d197f513dd596b820b956fef43485876226d65d497bebc15666aa2aa82c679e84f65d5f2bfbf14ee46e32c1
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: "npm:^4.0.0"
  checksum: 10c0/dcac00920a4d503e38bb64001acb19df4efc14536ada475725e12f52c16777afdee4db827f55f13a908ee7efc0cb282e2e3dbaeeb98c0993dd93d1802d3bf00c
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10c0/b6416aaff1f380bf56c3b552f31fdf7a69b45689368deca72d28636f41c16bb28ec3ebc40ace97db4c1afc0ceeb8120e8492fe0046841c94c2933b2e30a7d5ac
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10c0/c96bdccabe9d62ab6fea9399fdff04a66e6563c1d6fb3a3a063e8d53c3bb136ba63e84250bbf63d00086a769ad53aef92d2bd483f03f837fc97b71cbee6b2520
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: 10c0/d5ae2b7110ca3746b3643d3ef60ef823f5f078667baf530cec096433f1627ec4b6fa8c072f09d079d7cda915fd2c7bc1b7b935681e9b09e591e1e15f4040b8e2
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10c0/686f5a9ef0fff078c1412c05db73a0dce096190036f33e400a07e2a4518e9f56b1e324f5c576a0a747ef0e75b5d985c040b0d51945ce780c0dd3c625a18cd8c9
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 10c0/bba1e5932b3e196ad6862286d76adc89a0dbf0c773e5ced1eb01f9af930c50093a084eff14b8de5ea60b895c56a04d5de8bbc4930c5543d029091916770b2d2a
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1, domutils@npm:^3.1.0":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 10c0/47938f473b987ea71cd59e59626eb8666d3aa8feba5266e45527f3b636c7883cca7e582d901531961f742c519d7514636b7973353b648762b2e3bedbf235fada
  languageName: node
  linkType: hard

"dotenv@npm:^16.0.0, dotenv@npm:^16.0.3":
  version: 16.4.7
  resolution: "dotenv@npm:16.4.7"
  checksum: 10c0/be9f597e36a8daf834452daa1f4cc30e5375a5968f98f46d89b16b983c567398a330580c88395069a77473943c06b877d1ca25b4afafcdd6d4adb549e8293462
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ecdsa-sig-formatter@npm:1.0.11":
  version: 1.0.11
  resolution: "ecdsa-sig-formatter@npm:1.0.11"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/ebfbf19d4b8be938f4dd4a83b8788385da353d63307ede301a9252f9f7f88672e76f2191618fd8edfc2f24679236064176fab0b78131b161ee73daa37125408c
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10c0/b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"ejs@npm:^3.1.10":
  version: 3.1.10
  resolution: "ejs@npm:3.1.10"
  dependencies:
    jake: "npm:^10.8.5"
  bin:
    ejs: bin/cli.js
  checksum: 10c0/52eade9e68416ed04f7f92c492183340582a36482836b11eab97b159fcdcfdedc62233a1bf0bf5e5e1851c501f2dca0e2e9afd111db2599e4e7f53ee29429ae1
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.73":
  version: 1.5.92
  resolution: "electron-to-chromium@npm:1.5.92"
  checksum: 10c0/336a3f5fea79cef2463c7e485e17d6c30e94155675cfb5b99478503c51c2603f7af79fe3969e72f5630d87b9097100690a0fb03b0d49a67b8a5e7aba03d79180
  languageName: node
  linkType: hard

"emittery@npm:^0.13.1":
  version: 0.13.1
  resolution: "emittery@npm:0.13.1"
  checksum: 10c0/1573d0ae29ab34661b6c63251ff8f5facd24ccf6a823f19417ae8ba8c88ea450325788c67f16c99edec8de4b52ce93a10fe441ece389fd156e88ee7dab9bfa35
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10c0/f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encoding-sniffer@npm:^0.2.0":
  version: 0.2.0
  resolution: "encoding-sniffer@npm:0.2.0"
  dependencies:
    iconv-lite: "npm:^0.6.3"
    whatwg-encoding: "npm:^3.1.1"
  checksum: 10c0/b312e0d67f339bec44e021e5210ee8ee90d7b8f9975eb2c79a36fd467eb07709e88dcf62ee20f62ee0d74a13874307d99557852a2de9b448f1e3fb991fc68257
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10c0/870b423afb2d54bb8d243c63e07c170409d41e20b47eeef0727547aea5740bd6717aca45597a9f2745525667a6b804c1e7bede41f856818faee5806dd9ff3975
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.0.0, enhanced-resolve@npm:^5.14.0, enhanced-resolve@npm:^5.7.0":
  version: 5.18.1
  resolution: "enhanced-resolve@npm:5.18.1"
  dependencies:
    graceful-fs: "npm:^4.2.4"
    tapable: "npm:^2.2.0"
  checksum: 10c0/4cffd9b125225184e2abed9fdf0ed3dbd2224c873b165d0838fd066cde32e0918626cba2f1f4bf6860762f13a7e2364fd89a82b99566be2873d813573ac71846
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.4.0, entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10c0/5b039739f7621f5d1ad996715e53d964035f75ad3b9a4d38c6b3804bb226e282ffeae2443624d8fdd9c47d8e926ae9ac009c54671243f0c3294c26af7cc85250
  languageName: node
  linkType: hard

"entities@npm:^6.0.0":
  version: 6.0.0
  resolution: "entities@npm:6.0.0"
  checksum: 10c0/b82a7bd5de282860f3c36a91e815e41e874fd036c83956a568b82729678492eb088359d6f7e0a4f5c00776427263fcba04959b8340fefa430c39b9bce770427e
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10c0/ba827f89369b4c93382cfca5a264d059dfefdaa56ecc5e338ffa58a6471f5ed93b71a20add1d52290a4873d92381174382658c885ac1a2305f7baca363ce9cce
  languageName: node
  linkType: hard

"es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.9":
  version: 1.23.9
  resolution: "es-abstract@npm:1.23.9"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.0"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-regex: "npm:^1.2.1"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.0"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.3"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.3"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.18"
  checksum: 10c0/1de229c9e08fe13c17fe5abaec8221545dfcd57e51f64909599a6ae896df84b8fd2f7d16c60cb00d7bf495b9298ca3581aded19939d4b7276854a4b066f8422b
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-get-iterator@npm:^1.1.3":
  version: 1.1.3
  resolution: "es-get-iterator@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.2"
    get-intrinsic: "npm:^1.1.3"
    has-symbols: "npm:^1.0.3"
    is-arguments: "npm:^1.1.1"
    is-map: "npm:^2.0.2"
    is-set: "npm:^2.0.2"
    is-string: "npm:^1.0.7"
    isarray: "npm:^2.0.5"
    stop-iteration-iterator: "npm:^1.0.0"
  checksum: 10c0/ebd11effa79851ea75d7f079405f9d0dc185559fd65d986c6afea59a0ff2d46c2ed8675f19f03dce7429d7f6c14ff9aede8d121fbab78d75cfda6a263030bac0
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.2.1":
  version: 1.6.0
  resolution: "es-module-lexer@npm:1.6.0"
  checksum: 10c0/667309454411c0b95c476025929881e71400d74a746ffa1ff4cb450bd87f8e33e8eef7854d68e401895039ac0bac64e7809acbebb6253e055dd49ea9e3ea9212
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2":
  version: 1.0.2
  resolution: "es-shim-unscopables@npm:1.0.2"
  dependencies:
    hasown: "npm:^2.0.0"
  checksum: 10c0/f495af7b4b7601a4c0cfb893581c352636e5c08654d129590386a33a0432cf13a7bdc7b6493801cadd990d838e2839b9013d1de3b880440cb537825e834fe783
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: 10c0/c7e87467abb0b438639baa8139f701a06537d2b9bc758f23e8622c3b42fd0fdb5bde0f535686119e446dd9d5e4c0f238af4e14960f4771877cf818d023f6730b
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10c0/a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 10c0/2530479fe8db57eace5e8646c9c2a9c80fa279614986d16dcc6bcaceb63ae77f05a851ba6c43756d816c61d7f4534baf56e3c705e3e0d884818a46808811c507
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^8.7.0":
  version: 8.10.0
  resolution: "eslint-config-prettier@npm:8.10.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 10c0/19f8c497d9bdc111a17a61b25ded97217be3755bbc4714477dfe535ed539dddcaf42ef5cf8bb97908b058260cf89a3d7c565cb0be31096cbcd39f4c2fa5fe43c
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: "npm:^3.2.7"
    is-core-module: "npm:^2.13.0"
    resolve: "npm:^1.22.4"
  checksum: 10c0/0ea8a24a72328a51fd95aa8f660dcca74c1429806737cf10261ab90cfcaaf62fd1eff664b76a44270868e0a932711a81b250053942595bcd00a93b1c1575dd61
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.12.0":
  version: 2.12.0
  resolution: "eslint-module-utils@npm:2.12.0"
  dependencies:
    debug: "npm:^3.2.7"
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 10c0/4d8b46dcd525d71276f9be9ffac1d2be61c9d54cc53c992e6333cf957840dee09381842b1acbbb15fc6b255ebab99cd481c5007ab438e5455a14abe1a0468558
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.27.5":
  version: 2.31.0
  resolution: "eslint-plugin-import@npm:2.31.0"
  dependencies:
    "@rtsao/scc": "npm:^1.1.0"
    array-includes: "npm:^3.1.8"
    array.prototype.findlastindex: "npm:^1.2.5"
    array.prototype.flat: "npm:^1.3.2"
    array.prototype.flatmap: "npm:^1.3.2"
    debug: "npm:^3.2.7"
    doctrine: "npm:^2.1.0"
    eslint-import-resolver-node: "npm:^0.3.9"
    eslint-module-utils: "npm:^2.12.0"
    hasown: "npm:^2.0.2"
    is-core-module: "npm:^2.15.1"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^3.1.2"
    object.fromentries: "npm:^2.0.8"
    object.groupby: "npm:^1.0.3"
    object.values: "npm:^1.2.0"
    semver: "npm:^6.3.1"
    string.prototype.trimend: "npm:^1.0.8"
    tsconfig-paths: "npm:^3.15.0"
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
  checksum: 10c0/e21d116ddd1900e091ad120b3eb68c5dd5437fe2c930f1211781cd38b246f090a6b74d5f3800b8255a0ed29782591521ad44eb21c5534960a8f1fb4040fd913a
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^4.2.1":
  version: 4.2.1
  resolution: "eslint-plugin-prettier@npm:4.2.1"
  dependencies:
    prettier-linter-helpers: "npm:^1.0.0"
  peerDependencies:
    eslint: ">=7.28.0"
    prettier: ">=2.0.0"
  peerDependenciesMeta:
    eslint-config-prettier:
      optional: true
  checksum: 10c0/c5e7316baeab9d96ac39c279f16686e837277e5c67a8006c6588bcff317edffdc1532fb580441eb598bc6770f6444006756b68a6575dff1cd85ebe227252d0b7
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1, eslint-scope@npm:^5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^4.1.1"
  checksum: 10c0/d30ef9dc1c1cbdece34db1539a4933fe3f9b14e1ffb27ecc85987902ee663ad7c9473bbd49a9a03195a373741e62e2f807c4938992e019b511993d163450e70a
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.2.2":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10c0/613c267aea34b5a6d6c00514e8545ef1f1433108097e857225fed40d397dd6b1809dffd11c2fde23b37ca53d7bf935fe04d2a18e6fc932b31837b6ad67e1c116
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10c0/92708e882c0a5ffd88c23c0b404ac1628cf20104a108c745f240a13c332a11aac54f49a22d5762efbffc18ecbc9a580d1b7ad034bf5f3cc3307e5cbff2ec9820
  languageName: node
  linkType: hard

"eslint@npm:^8.35.0":
  version: 8.57.1
  resolution: "eslint@npm:8.57.1"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.6.1"
    "@eslint/eslintrc": "npm:^2.1.4"
    "@eslint/js": "npm:8.57.1"
    "@humanwhocodes/config-array": "npm:^0.13.0"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@nodelib/fs.walk": "npm:^1.2.8"
    "@ungap/structured-clone": "npm:^1.2.0"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.2"
    debug: "npm:^4.3.2"
    doctrine: "npm:^3.0.0"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^7.2.2"
    eslint-visitor-keys: "npm:^3.4.3"
    espree: "npm:^9.6.1"
    esquery: "npm:^1.4.2"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^6.0.1"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    globals: "npm:^13.19.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    is-path-inside: "npm:^3.0.3"
    js-yaml: "npm:^4.1.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    levn: "npm:^0.4.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
    strip-ansi: "npm:^6.0.1"
    text-table: "npm:^0.2.0"
  bin:
    eslint: bin/eslint.js
  checksum: 10c0/1fd31533086c1b72f86770a4d9d7058ee8b4643fd1cfd10c7aac1ecb8725698e88352a87805cf4b2ce890aa35947df4b4da9655fb7fdfa60dbb448a43f6ebcf1
  languageName: node
  linkType: hard

"espree@npm:^9.6.0, espree@npm:^9.6.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: "npm:^8.9.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 10c0/1a2e9b4699b715347f62330bcc76aee224390c28bb02b31a3752e9d07549c473f5f986720483c6469cf3cfb3c9d05df612ffc69eb1ee94b54b739e67de9bb460
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10c0/ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"esquery@npm:^1.4.2":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10c0/cb9065ec605f9da7a76ca6dadb0619dfb611e37a81e318732977d90fab50a256b95fee2d925fba7c2f3f0523aa16f91587246693bc09bc34d5a59575fe6e93d2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10c0/81a37116d1408ded88ada45b9fb16dbd26fba3aadc369ce50fcaf82a0bac12772ebd7b24cd7b91fc66786bf2c1ac7b5f196bc990a473efff972f5cb338877cf5
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: 10c0/9cb46463ef8a8a4905d3708a652d60122a0c20bb58dec7e0e12ab0e7235123d74214fc0141d743c381813e1b992767e2708194f6f6e0f9fd00c1b4e0887b8b6d
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10c0/1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10c0/9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10c0/12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10c0/0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 10c0/4ba5c00c506e6c786b4d6262cfbce90ddc14c10d4667e5c83ae993c9de88aa856033994dd2b35b83e8dc1170e224e66a319fa80adc4c32adcd2379bbc75da814
  languageName: node
  linkType: hard

"events@npm:1.1.1":
  version: 1.1.1
  resolution: "events@npm:1.1.1"
  checksum: 10c0/29ba5a4c7d03dd2f4a2d3d9d4dfd8332225256f666cd69f490975d2eff8d7c73f1fb4872877b2c1f3b485e8fb42462153d65e5a21ea994eb928c3bec9e0c826e
  languageName: node
  linkType: hard

"events@npm:^3.2.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: 10c0/d6b6f2adbccbcda74ddbab52ed07db727ef52e31a61ed26db9feb7dc62af7fc8e060defa65e5f8af9449b86b52cc1a1f6a79f2eafcf4e62add2b7a1fa4a432f6
  languageName: node
  linkType: hard

"execa@npm:^4.0.2":
  version: 4.1.0
  resolution: "execa@npm:4.1.0"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    get-stream: "npm:^5.0.0"
    human-signals: "npm:^1.1.1"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.0"
    onetime: "npm:^5.1.0"
    signal-exit: "npm:^3.0.2"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10c0/02211601bb1c52710260edcc68fb84c3c030dc68bafc697c90ada3c52cc31375337de8c24826015b8382a58d63569ffd203b79c94fef217d65503e3e8d2c52ba
  languageName: node
  linkType: hard

"execa@npm:^5.0.0":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10c0/c8e615235e8de4c5addf2fa4c3da3e3aa59ce975a3e83533b4f6a71750fb816a2e79610dc5f1799b6e28976c9ae86747a36a606655bf8cb414a74d8d507b304f
  languageName: node
  linkType: hard

"exit@npm:^0.1.2":
  version: 0.1.2
  resolution: "exit@npm:0.1.2"
  checksum: 10c0/71d2ad9b36bc25bb8b104b17e830b40a08989be7f7d100b13269aaae7c3784c3e6e1e88a797e9e87523993a25ba27c8958959a554535370672cfb4d824af8989
  languageName: node
  linkType: hard

"expect@npm:^29.0.0, expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "expect@npm:29.7.0"
  dependencies:
    "@jest/expect-utils": "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/2eddeace66e68b8d8ee5f7be57f3014b19770caaf6815c7a08d131821da527fb8c8cb7b3dcd7c883d2d3d8d184206a4268984618032d1e4b16dc8d6596475d41
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 10c0/160456d2d647e6019640bd07111634d8c353038d9fa40176afb7cd49b0548bdae83b56d05e907c2cce2300b81cae35d800ef92fefb9d0208e190fa3b7d6bb579
  languageName: node
  linkType: hard

"express@npm:4.18.2":
  version: 4.18.2
  resolution: "express@npm:4.18.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.1"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.5.0"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.2.0"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.1"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.7"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.11.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.18.0"
    serve-static: "npm:1.15.0"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/75af556306b9241bc1d7bdd40c9744b516c38ce50ae3210658efcbf96e3aed4ab83b3432f06215eae5610c123bc4136957dc06e50dfc50b7d4d775af56c4c59c
  languageName: node
  linkType: hard

"external-editor@npm:^3.0.3":
  version: 3.1.0
  resolution: "external-editor@npm:3.1.0"
  dependencies:
    chardet: "npm:^0.7.0"
    iconv-lite: "npm:^0.4.24"
    tmp: "npm:^0.0.33"
  checksum: 10c0/c98f1ba3efdfa3c561db4447ff366a6adb5c1e2581462522c56a18bf90dfe4da382f9cd1feee3e330108c3595a854b218272539f311ba1b3298f841eb0fbf339
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 10c0/5c19af237edb5d5effda008c891a18a585f74bf12953be57923f17a3a4d0979565fc64dbc73b9e20926b9d895f5b690c618cbb969af0cf022e3222471220ad29
  languageName: node
  linkType: hard

"fast-fifo@npm:^1.2.0, fast-fifo@npm:^1.3.2":
  version: 1.3.2
  resolution: "fast-fifo@npm:1.3.2"
  checksum: 10c0/d53f6f786875e8b0529f784b59b4b05d4b5c31c651710496440006a398389a579c8dbcd2081311478b5bf77f4b0b21de69109c5a4eabea9d8e8783d1eb864e4c
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.9":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10c0/f6aaa141d0d3384cf73cbcdfc52f475ed293f6d5b65bfc5def368b09163a9f7e5ec2b3014d80f733c405f58e470ee0cc451c2937685045cddcdeaa24199c43fe
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:2.x, fast-json-stable-stringify@npm:^2.0.0, fast-json-stable-stringify@npm:^2.1.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10c0/111972b37338bcb88f7d9e2c5907862c280ebf4234433b95bc611e518d192ccb2d38119c4ac86e26b668d75f7f3894f4ff5c4982899afced7ca78633b08287c4
  languageName: node
  linkType: hard

"fast-safe-stringify@npm:2.1.1, fast-safe-stringify@npm:^2.1.1":
  version: 2.1.1
  resolution: "fast-safe-stringify@npm:2.1.1"
  checksum: 10c0/d90ec1c963394919828872f21edaa3ad6f1dddd288d2bd4e977027afff09f5db40f94e39536d4646f7e01761d704d72d51dce5af1b93717f3489ef808f5f4e4d
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 10c0/74a513c2af0584448aee71ce56005185f81239eab7a2343110e5bad50c39ad4fb19c5a6f99783ead1cac7ccaf3461a6034fda89fffa2b30b6d99b9f21c2f9d29
  languageName: node
  linkType: hard

"fast-xml-parser@npm:4.4.1":
  version: 4.4.1
  resolution: "fast-xml-parser@npm:4.4.1"
  dependencies:
    strnum: "npm:^1.0.5"
  bin:
    fxparser: src/cli/cli.js
  checksum: 10c0/7f334841fe41bfb0bf5d920904ccad09cefc4b5e61eaf4c225bf1e1bb69ee77ef2147d8942f783ee8249e154d1ca8a858e10bda78a5d78b8bed3f48dcee9bf33
  languageName: node
  linkType: hard

"fast-xml-parser@npm:^3.17.6":
  version: 3.21.1
  resolution: "fast-xml-parser@npm:3.21.1"
  dependencies:
    strnum: "npm:^1.0.4"
  bin:
    xml2js: cli.js
  checksum: 10c0/26ed57fd3cb86434959cffa6852ab63231850ff5be6836aef7744b1abc8013fa5696c05179e422965677e116606611242d91ceebf246d91a5247e262d8b59996
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.0
  resolution: "fastq@npm:1.19.0"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/d6a001638f1574a696660fcbba5300d017760432372c801632c325ca7c16819604841c92fd3ccadcdacec0966ca336363a5ff57bc5f0be335d8ea7ac6087b98f
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: "npm:2.1.1"
  checksum: 10c0/feae89ac148adb8f6ae8ccd87632e62b13563e6fb114cacb5265c51f585b17e2e268084519fb2edd133872f1d47a18e6bfd7e5e08625c0d41b93149694187581
  languageName: node
  linkType: hard

"figures@npm:^3.0.0":
  version: 3.2.0
  resolution: "figures@npm:3.2.0"
  dependencies:
    escape-string-regexp: "npm:^1.0.5"
  checksum: 10c0/9c421646ede432829a50bc4e55c7a4eb4bcb7cc07b5bab2f471ef1ab9a344595bbebb6c5c21470093fbb730cd81bbca119624c40473a125293f656f49cb47629
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: "npm:^3.0.4"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"filelist@npm:^1.0.4":
  version: 1.0.4
  resolution: "filelist@npm:1.0.4"
  dependencies:
    minimatch: "npm:^5.0.1"
  checksum: 10c0/426b1de3944a3d153b053f1c0ebfd02dccd0308a4f9e832ad220707a6d1f1b3c9784d6cadf6b2f68f09a57565f63ebc7bcdc913ccf8012d834f472c46e596f41
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"filter-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "filter-obj@npm:1.1.0"
  checksum: 10c0/071e0886b2b50238ca5026c5bbf58c26a7c1a1f720773b8c7813d16ba93d0200de977af14ac143c5ac18f666b2cfc83073f3a5fe6a4e996c49e0863d5500fccf
  languageName: node
  linkType: hard

"finalhandler@npm:1.2.0":
  version: 1.2.0
  resolution: "finalhandler@npm:1.2.0"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/64b7e5ff2ad1fcb14931cd012651631b721ce657da24aedb5650ddde9378bf8e95daa451da43398123f5de161a81e79ff5affe4f9f2a6d2df4a813d6d3e254b7
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/0406ee89ebeefa2d507feb07ec366bebd8a6167ae74aa4e34fb4c4abd06cf782a3ce26ae4194d70706f72182841733f00551c209fe575cb00bd92104056e78c1
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.2.0
  resolution: "flat-cache@npm:3.2.0"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.3"
    rimraf: "npm:^3.0.2"
  checksum: 10c0/b76f611bd5f5d68f7ae632e3ae503e678d205cf97a17c6ab5b12f6ca61188b5f1f7464503efae6dc18683ed8f0b41460beb48ac4b9ac63fe6201296a91ba2f75
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.2
  resolution: "flatted@npm:3.3.2"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/5829165bd112c3c0e82be6c15b1a58fa9dcfaede3b3c54697a82fe4a62dd5ae5e8222956b448d2f98e331525f05d00404aba7d696de9e761ef6e42fdc780244f
  languageName: node
  linkType: hard

"fontkit@npm:^1.8.1":
  version: 1.9.0
  resolution: "fontkit@npm:1.9.0"
  dependencies:
    "@swc/helpers": "npm:^0.3.13"
    brotli: "npm:^1.3.2"
    clone: "npm:^2.1.2"
    deep-equal: "npm:^2.0.5"
    dfa: "npm:^1.2.0"
    restructure: "npm:^2.0.1"
    tiny-inflate: "npm:^1.0.3"
    unicode-properties: "npm:^1.3.1"
    unicode-trie: "npm:^2.0.0"
  checksum: 10c0/a1c45999d90f1b25bf6f09797b64982207d3d24962b2a109bf6c913e7c1caced6a3e0e50e406a0862eae8f6f10846da00cab37818ad8f5c354febbfb8d11e748
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.4
  resolution: "for-each@npm:0.3.4"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10c0/6b2016c0a0fe3107c70a233923cac74f07bedb5a1847636039fa6bcc3df09aefa554cfec23c3342ad365acac1f95e799d9f8e220cb82a4c7b8a84f969234302f
  languageName: node
  linkType: hard

"for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10c0/0e0b50f6a843a282637d43674d1fb278dda1dd85f4f99b640024cfb10b85058aac0cc781bf689d5fe50b4b7f638e91e548560723a4e76e04fe96ae35ef039cee
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/028f1d41000553fcfa6c4bb5c372963bf3d9bf0b1f25a87d1a6253014343fb69dfb1b42d9625d7cf44c8ba429940f3d0ff718b62105d4d4a4f6ef8ca0a53faa2
  languageName: node
  linkType: hard

"fork-ts-checker-webpack-plugin@npm:8.0.0":
  version: 8.0.0
  resolution: "fork-ts-checker-webpack-plugin@npm:8.0.0"
  dependencies:
    "@babel/code-frame": "npm:^7.16.7"
    chalk: "npm:^4.1.2"
    chokidar: "npm:^3.5.3"
    cosmiconfig: "npm:^7.0.1"
    deepmerge: "npm:^4.2.2"
    fs-extra: "npm:^10.0.0"
    memfs: "npm:^3.4.1"
    minimatch: "npm:^3.0.4"
    node-abort-controller: "npm:^3.0.1"
    schema-utils: "npm:^3.1.1"
    semver: "npm:^7.3.5"
    tapable: "npm:^2.2.1"
  peerDependencies:
    typescript: ">3.6.0"
    webpack: ^5.11.0
  checksum: 10c0/1a2bb9bbd3e943e3b3a45d7fa9e8383698f5fea1ba28f7d18c8372c804460c2f13af53f791360b973fddafd3e88de7af59082c3cb3375f4e7c3365cd85accedc
  languageName: node
  linkType: hard

"form-data-encoder@npm:1.7.2":
  version: 1.7.2
  resolution: "form-data-encoder@npm:1.7.2"
  checksum: 10c0/56553768037b6d55d9de524f97fe70555f0e415e781cb56fc457a68263de3d40fadea2304d4beef2d40b1a851269bd7854e42c362107071892cb5238debe9464
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.1
  resolution: "form-data@npm:4.0.1"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/bb102d570be8592c23f4ea72d7df9daa50c7792eb0cf1c5d7e506c1706e7426a4e4ae48a35b109e91c85f1c0ec63774a21ae252b66f4eb981cb8efef7d0463c8
  languageName: node
  linkType: hard

"formdata-node@npm:^4.3.2":
  version: 4.4.1
  resolution: "formdata-node@npm:4.4.1"
  dependencies:
    node-domexception: "npm:1.0.0"
    web-streams-polyfill: "npm:4.0.0-beta.3"
  checksum: 10c0/74151e7b228ffb33b565cec69182694ad07cc3fdd9126a8240468bb70a8ba66e97e097072b60bcb08729b24c7ce3fd3e0bd7f1f80df6f9f662b9656786e76f6a
  languageName: node
  linkType: hard

"formidable@npm:^2.1.2":
  version: 2.1.2
  resolution: "formidable@npm:2.1.2"
  dependencies:
    dezalgo: "npm:^1.0.4"
    hexoid: "npm:^1.0.0"
    once: "npm:^1.4.0"
    qs: "npm:^6.11.0"
  checksum: 10c0/efba03d11127098daa6ef54c3c0fad25693973eb902fa88ccaaa203baebe8c74d12ba0fe1e113eccf79b9172510fa337e4e107330b124fb3a8c74697b4aa2ce3
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 10c0/9b67c3fac86acdbc9ae47ba1ddd5f2f81526fa4c8226863ede5600a3f7c7416ef451f6f1e240a3cc32d0fd79fcfe6beb08fd0da454f360032bde70bf80afbb33
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10c0/c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/5f579466e7109719d162a9249abbeffe7f426eb133ea486e020b89bc6d67a741134076bf439983f2eb79276ceaf6bd7b7c1e43c3fd67fe889863e69072fb0a5e
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/703d16522b8282d7299337539c3ed6edddd1afe82435e4f5b76e34a79cd74e488a8a0e26a636afc2440e1a23b03878e2122e3a2cfe375a5cf63c37d92b86a004
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs-monkey@npm:^1.0.4":
  version: 1.0.6
  resolution: "fs-monkey@npm:1.0.6"
  checksum: 10c0/6f2508e792a47e37b7eabd5afc79459c1ea72bce2a46007d2b7ed0bfc3a4d64af38975c6eb7e93edb69ac98bbb907c13ff1b1579b2cf52d3d02dbc0303fca79f
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:^2.3.2, fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A^2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: 10c0/e920a2ab52663005f3cbe7ee3373e3c71c1fb5558b0b0548648cdf3e51961085032458e26c71ff1a8c8c20e7ee7caeb03d43a5d1fa8610c459333323a2e71253
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10c0/33e77fd29bddc2d9bb78ab3eb854c165909201f88c75faa8272e35899e2d35a8a642a15e7420ef945e1f64a9670d6aa3ec744106b2aa42be68ca5114025954ca
  languageName: node
  linkType: hard

"gauge@npm:^3.0.0":
  version: 3.0.2
  resolution: "gauge@npm:3.0.2"
  dependencies:
    aproba: "npm:^1.0.3 || ^2.0.0"
    color-support: "npm:^1.1.2"
    console-control-strings: "npm:^1.0.0"
    has-unicode: "npm:^2.0.1"
    object-assign: "npm:^4.1.1"
    signal-exit: "npm:^3.0.0"
    string-width: "npm:^4.2.3"
    strip-ansi: "npm:^6.0.1"
    wide-align: "npm:^1.1.2"
  checksum: 10c0/75230ccaf216471e31025c7d5fcea1629596ca20792de50c596eb18ffb14d8404f927cd55535aab2eeecd18d1e11bd6f23ec3c2e9878d2dda1dc74bccc34b913
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10c0/c6c7b60271931fa752aeb92f2b47e355eac1af3a2673f47c9589e8f8a41adc74d45551c1bc57b5e66a80609f10ffb72b6f575e4370d61cc3f7f3aaff01757cde
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.2, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7":
  version: 1.2.7
  resolution: "get-intrinsic@npm:1.2.7"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.0"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/b475dec9f8bff6f7422f51ff4b7b8d0b68e6776ee83a753c1d627e3008c3442090992788038b37eff72e93e43dceed8c1acbdf2d6751672687ec22127933080d
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: 10c0/e34cdf447fdf1902a1f6d5af737eaadf606d2ee3518287abde8910e04159368c268568174b2e71102b87b26c2020486f126bfca9c4fb1ceb986ff99b52ecd1be
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-stream@npm:^5.0.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: "npm:^3.0.0"
  checksum: 10c0/43797ffd815fbb26685bf188c8cfebecb8af87b3925091dd7b9a9c915993293d78e3c9e1bce125928ff92f2d0796f3889b92b5ec6d58d1041b574682132e0a80
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 10c0/49825d57d3fd6964228e6200a58169464b8e8970489b3acdc24906c782fb7f01f9f56f8e6653c4a50713771d6658f7cfe051e5eb8c12e334138c9c918b296341
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/d6a7d6afca375779a4b307738c9e80dbf7afc0bdbe5948768d54ab9653c865523d8920e670991a925936eb524b7cb6a6361d199a760b21d0ca7620194455aa4b
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10c0/317034d88654730230b3f43bb7ad4f7c90257a426e872ea0bf157473ac61c99bf5d205fad8f0185f989be8d2fa6d3c7dce1645d99d545b6ea9089c39f838e7f8
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: 10c0/0486925072d7a916f052842772b61c3e86247f0a80cc0deb9b5a3e8a1a9faad5b04fb6f58986a09f34d3e96cd2a22a24b7e9882fb1cf904c31e9a310de96c429
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"glob@npm:^7.0.0, glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"glob@npm:^8.0.0":
  version: 8.1.0
  resolution: "glob@npm:8.1.0"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^5.0.1"
    once: "npm:^1.3.0"
  checksum: 10c0/cb0b5cab17a59c57299376abe5646c7070f8acb89df5595b492dba3bfb43d301a46c01e5695f01154e6553168207cb60d4eaf07d3be4bc3eb9b0457c5c561d0f
  languageName: node
  linkType: hard

"glob@npm:^9.2.0":
  version: 9.3.5
  resolution: "glob@npm:9.3.5"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    minimatch: "npm:^8.0.2"
    minipass: "npm:^4.2.4"
    path-scurry: "npm:^1.6.1"
  checksum: 10c0/2f6c2b9ee019ee21dc258ae97a88719614591e4c979cb4580b1b9df6f0f778a3cb38b4bdaf18dfa584637ea10f89a3c5f2533a5e449cf8741514ad18b0951f2e
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10c0/758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"globals@npm:^13.19.0":
  version: 13.24.0
  resolution: "globals@npm:13.24.0"
  dependencies:
    type-fest: "npm:^0.20.2"
  checksum: 10c0/d3c11aeea898eb83d5ec7a99508600fbe8f83d2cf00cbb77f873dbf2bcb39428eff1b538e4915c993d8a3b3473fa71eeebfe22c9bb3a3003d1e26b1f2c8a42cd
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 10c0/9d156f313af79d80b1566b93e19285f481c591ad6d0d319b4be5e03750d004dde40a39a0f26f7e635f9007a3600802f53ecd85a759b86f109e80a5f705e01846
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: "npm:^2.1.0"
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.2.9"
    ignore: "npm:^5.2.0"
    merge2: "npm:^1.4.1"
    slash: "npm:^3.0.0"
  checksum: 10c0/b39511b4afe4bd8a7aead3a27c4ade2b9968649abab0a6c28b1a90141b96ca68ca5db1302f7c7bd29eab66bf51e13916b8e0a3d0ac08f75e1e84a39b35691189
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10c0/e951259d8cd2e0d196c72ec711add7115d42eb9a8146c8eeda5b8d3ac91e5dd816b9cd68920726d9fd4490368e7ed86e9c423f40db87e2d8dfafa00fa17c3a31
  languageName: node
  linkType: hard

"handlebars@npm:*, handlebars@npm:^4.7.8":
  version: 4.7.8
  resolution: "handlebars@npm:4.7.8"
  dependencies:
    minimist: "npm:^1.2.5"
    neo-async: "npm:^2.6.2"
    source-map: "npm:^0.6.1"
    uglify-js: "npm:^3.1.4"
    wordwrap: "npm:^1.0.0"
  dependenciesMeta:
    uglify-js:
      optional: true
  bin:
    handlebars: bin/handlebars
  checksum: 10c0/7aff423ea38a14bb379316f3857fe0df3c5d66119270944247f155ba1f08e07a92b340c58edaa00cfe985c21508870ee5183e0634dcb53dd405f35c93ef7f10d
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 10c0/2de0cdc4a1ccf7a1e75ffede1876994525ac03cc6f5ae7392d3415dd475cd9eee5bceec63669ab61aa997ff6cceebb50ef75561c7002bed8988de2b9d1b40788
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10c0/253c1f59e80bb476cf0dde8ff5284505d90c3bdb762983c3514d36414290475fe3fd6f574929d84de2a8eec00d35cf07cb6776205ff32efd7c50719125f00236
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 10c0/46538dddab297ec2f43923c3d35237df45d8c55a6fc1067031e04c13ed8a9a8f94954460632fd4da84c31a1721eefee16d901cbb1ae9602bab93bb6e08f93b95
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 10c0/ebdb2f4895c26bb08a8a100b62d362e49b2190bcfd84b76bc4be1a3bd4d254ec52d0dd9f2fbcc093fc5eb878b20c52146f9dfd33e2686ed28982187be593b47c
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0, hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"hexoid@npm:^1.0.0":
  version: 1.0.0
  resolution: "hexoid@npm:1.0.0"
  checksum: 10c0/9c45e8ba676b9eb88455631ebceec4c829a8374a583410dc735472ab9808bf11339fcd074633c3fa30e420901b894d8a92ffd5e2e21eddd41149546e05a91f69
  languageName: node
  linkType: hard

"highlight.js@npm:^10.7.1":
  version: 10.7.3
  resolution: "highlight.js@npm:10.7.3"
  checksum: 10c0/073837eaf816922427a9005c56c42ad8786473dc042332dfe7901aa065e92bc3d94ebf704975257526482066abb2c8677cc0326559bb8621e046c21c5991c434
  languageName: node
  linkType: hard

"hjson@npm:^3.2.2":
  version: 3.2.2
  resolution: "hjson@npm:3.2.2"
  bin:
    hjson: bin/hjson
  checksum: 10c0/5053d7f5ea088b53d1b2b4ec35766df7d8a651a8fb576898b79ea0951e6f6c836a23cc9cc345fd855054514960301762dc1c92e8fdb71af7b75dadaa8ee89033
  languageName: node
  linkType: hard

"html-encoding-sniffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "html-encoding-sniffer@npm:4.0.0"
  dependencies:
    whatwg-encoding: "npm:^3.1.1"
  checksum: 10c0/523398055dc61ac9b34718a719cb4aa691e4166f29187e211e1607de63dc25ac7af52ca7c9aead0c4b3c0415ffecb17326396e1202e2e86ff4bca4c0ee4c6140
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: 10c0/208e8a12de1a6569edbb14544f4567e6ce8ecc30b9394fcaa4e7bb1e60c12a7c9a1ed27e31290817157e8626f3a4f29e76c8747030822eb84a6abb15c255f0a0
  languageName: node
  linkType: hard

"html-to-pdfmake@npm:^2.5.23":
  version: 2.5.23
  resolution: "html-to-pdfmake@npm:2.5.23"
  checksum: 10c0/37f044364c7b2f1da87f71ae030e5360832328eb56c5a6de3c7d638783c509afd123abe916b0fdfd4c2fbdd8e15f1ee887b2ca2469d4e5836384dc704fdfdd21
  languageName: node
  linkType: hard

"html-to-text@npm:^9.0.5":
  version: 9.0.5
  resolution: "html-to-text@npm:9.0.5"
  dependencies:
    "@selderee/plugin-htmlparser2": "npm:^0.11.0"
    deepmerge: "npm:^4.3.1"
    dom-serializer: "npm:^2.0.0"
    htmlparser2: "npm:^8.0.2"
    selderee: "npm:^0.11.0"
  checksum: 10c0/5d2c77b798cf88a81b1da2fc1ea1a3b3e2ff49fe5a3d812392f802fff18ec315cf0969bd7846ef2eb7df8c37f463bc63e8cbdcf84e42696c6f3e15dfa61cdf4f
  languageName: node
  linkType: hard

"htmlparser2@npm:^8.0.2":
  version: 8.0.2
  resolution: "htmlparser2@npm:8.0.2"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.0.1"
    entities: "npm:^4.4.0"
  checksum: 10c0/609cca85886d0bf2c9a5db8c6926a89f3764596877492e2caa7a25a789af4065bc6ee2cdc81807fe6b1d03a87bf8a373b5a754528a4cc05146b713c20575aab4
  languageName: node
  linkType: hard

"htmlparser2@npm:^9.1.0":
  version: 9.1.0
  resolution: "htmlparser2@npm:9.1.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.1.0"
    entities: "npm:^4.5.0"
  checksum: 10c0/394f6323efc265bbc791d8c0d96bfe95984e0407565248521ab92e2dc7668e5ceeca7bc6ed18d408b9ee3b25032c5743368a4280d280332d782821d5d467ad8f
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 10c0/ce1319b8a382eb3cbb4a37c19f6bfe14e5bb5be3d09079e885e8c513ab2d3cd9214902f8a31c9dc4e37022633ceabfc2d697405deeaf1b8f3552bb4ed996fdfc
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10c0/fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0, http-proxy-agent@npm:^7.0.2":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/6dd639f03434003577c62b27cafdb864784ef19b2de430d8ae2a1d45e31c4fd60719e5637b44db1a88a046934307da7089e03d6089ec3ddacc1189d8de8897d1
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.0, https-proxy-agent@npm:^7.0.1, https-proxy-agent@npm:^7.0.6":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"human-signals@npm:^1.1.1":
  version: 1.1.1
  resolution: "human-signals@npm:1.1.1"
  checksum: 10c0/18810ed239a7a5e23fb6c32d0fd4be75d7cd337a07ad59b8dbf0794cb0761e6e628349ee04c409e605fe55344716eab5d0a47a62ba2a2d0d367c89a2b4247b1e
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: 10c0/695edb3edfcfe9c8b52a76926cd31b36978782062c0ed9b1192b36bebc75c4c87c82e178dfcb0ed0fc27ca59d434198aac0bd0be18f5781ded775604db22304a
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: "npm:^2.0.0"
  checksum: 10c0/f34a2c20161d02303c2807badec2f3b49cbfbbb409abd4f95a07377ae01cfe6b59e3d15ac609cffcd8f2521f0eb37b7e1091acf65da99aa2a4f1ad63c21e7e7a
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24, iconv-lite@npm:^0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10c0/c6886a24cc00f2a059767440ec1bc00d334a89f250db8e0f7feb4961c8727118457e27c495ba94d082e51d3baca378726cd110aaf7ded8b9bbfd6a44760cf1d4
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2, iconv-lite@npm:^0.6.3":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:1.1.13":
  version: 1.1.13
  resolution: "ieee754@npm:1.1.13"
  checksum: 10c0/eaf8c87e014282bfb5b13670991a2ed086eaef35ccc3fb713833863f2e7213041b2c29246adbc5f6561d51d53861c3b11f3b82b28fc6fa1352edeff381f056e5
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13, ieee754@npm:^1.1.4, ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10c0/f9f652c957983634ded1e7f02da3b559a0d4cc210fca3792cb67f1b153623c9c42efdc1c4121af171e295444459fc4a9201101fb041b1104a3c000bccb188337
  languageName: node
  linkType: hard

"image-size@npm:^1.1.1":
  version: 1.2.0
  resolution: "image-size@npm:1.2.0"
  dependencies:
    queue: "npm:6.0.2"
  bin:
    image-size: bin/image-size.js
  checksum: 10c0/782669b530d9bbdcb334c8451db5f104dfbbcf90940751e6b75ba4e1b86d98bf3127c339eac8fb7a25c7a9ec4ea868d27b4916df3943c269b7419a8cc4459dca
  languageName: node
  linkType: hard

"immediate@npm:~3.0.5":
  version: 3.0.6
  resolution: "immediate@npm:3.0.6"
  checksum: 10c0/f8ba7ede69bee9260241ad078d2d535848745ff5f6995c7c7cb41cfdc9ccc213f66e10fa5afb881f90298b24a3f7344b637b592beb4f54e582770cdce3f1f039
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10c0/bf8cc494872fef783249709385ae883b447e3eb09db0ebd15dcead7d9afe7224dad7bd7591c6b73b0b19b3c0f9640eb8ee884f01cfaf2887ab995b0b36a0cbec
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.2.0
  resolution: "import-local@npm:3.2.0"
  dependencies:
    pkg-dir: "npm:^4.2.0"
    resolve-cwd: "npm:^3.0.0"
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: 10c0/94cd6367a672b7e0cb026970c85b76902d2710a64896fa6de93bd5c571dd03b228c5759308959de205083e3b1c61e799f019c9e36ee8e9c523b993e1057f0433
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.1, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"inquirer@npm:8.2.4":
  version: 8.2.4
  resolution: "inquirer@npm:8.2.4"
  dependencies:
    ansi-escapes: "npm:^4.2.1"
    chalk: "npm:^4.1.1"
    cli-cursor: "npm:^3.1.0"
    cli-width: "npm:^3.0.0"
    external-editor: "npm:^3.0.3"
    figures: "npm:^3.0.0"
    lodash: "npm:^4.17.21"
    mute-stream: "npm:0.0.8"
    ora: "npm:^5.4.1"
    run-async: "npm:^2.4.0"
    rxjs: "npm:^7.5.5"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
    through: "npm:^2.3.6"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/e8c6185548a2da6a04b6d2096d9173451ae8aa01432bfd8a5ffcd29fb871ed7764419a4fd693fbfb99621891b54c131f5473f21660d4808d25c6818618f2de73
  languageName: node
  linkType: hard

"inquirer@npm:8.2.5":
  version: 8.2.5
  resolution: "inquirer@npm:8.2.5"
  dependencies:
    ansi-escapes: "npm:^4.2.1"
    chalk: "npm:^4.1.1"
    cli-cursor: "npm:^3.1.0"
    cli-width: "npm:^3.0.0"
    external-editor: "npm:^3.0.3"
    figures: "npm:^3.0.0"
    lodash: "npm:^4.17.21"
    mute-stream: "npm:0.0.8"
    ora: "npm:^5.4.1"
    run-async: "npm:^2.4.0"
    rxjs: "npm:^7.5.5"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
    through: "npm:^2.3.6"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/e3e64e10f5daeeb8f770f1310acceb4aab593c10d693e7676ecd4a5b023d5b865b484fec7ead516e5e394db70eff687ef85459f75890f11a99ceadc0f4adce18
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10c0/03966f5e259b009a9bf1a78d60da920df198af4318ec004f57b8aef1dd3fe377fbc8cce63a96e8c810010302654de89f9e19de1cd8ad0061d15be28a695465c7
  languageName: node
  linkType: hard

"interpret@npm:^1.0.0":
  version: 1.4.0
  resolution: "interpret@npm:1.4.0"
  checksum: 10c0/08c5ad30032edeec638485bc3f6db7d0094d9b3e85e0f950866600af3c52e9fd69715416d29564731c479d9f4d43ff3e4d302a178196bdc0e6837ec147640450
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10c0/0486e775047971d3fdb5fb4f063829bac45af299ae0b82dcf3afa2145338e08290563a2a70f34b732d795ecc8311902e541a8530eeb30d75860a78ff4e94ce2a
  languageName: node
  linkType: hard

"is-arguments@npm:^1.0.4, is-arguments@npm:^1.1.1":
  version: 1.2.0
  resolution: "is-arguments@npm:1.2.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/6377344b31e9fcb707c6751ee89b11f132f32338e6a782ec2eac9393b0cbd32235dad93052998cda778ee058754860738341d8114910d50ada5615912bb929fc
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.2, is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/c5c9f25606e86dbb12e756694afbbff64bc8b348d1bc989324c037e1068695131930199d6ad381952715dad3a9569333817f0b1a72ce5af7f883ce802e49c83d
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10c0/e7fb686a739068bb70f860b39b67afc62acc62e36bb61c5f965768abce1873b379c563e61dd2adad96ebb7edf6651111b385e490cf508378959b0ed4cac4e729
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 10c0/f59b43dc1d129edb6f0e282595e56477f98c40278a2acdc8b0a5c57097c9eff8fe55470493df5775478cf32a4dc8eaf6d3a749f07ceee5bc263a78b2434f6a54
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/d70c236a5e82de6fc4d44368ffd0c2fee2b088b893511ce21e679da275a5ecc6015ff59a7d7e1bdd7ca39f71a8dbdd253cf8cce5c6b3c91cdd5b42b5ce677298
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: 10c0/f4f4b905ceb195be90a6ea7f34323bf1c18e3793f18922e3e9a73c684c29eeeeff5175605c3a3a74cc38185fe27758f07efba3dbae812e5c5afbc0d2316b40e4
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10c0/a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/36ff6baf6bd18b3130186990026f5a95c709345c39cd368468e6c1b6ab52201e9fd26d8e1f4c066357b4938b0f0401e1a5000e08257787c1a02f3a719457001e
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 10c0/ceebaeb9d92e8adee604076971dd6000d38d6afc40bb843ea8e45c5579b57671c3f3b50d7f04869618242c6cee08d1b67806a8cb8edaaaf7c0748b3720d6066f
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0, is-core-module@npm:^2.15.1, is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: 10c0/ef3548a99d7e7f1370ce21006baca6d40c73e9f15c941f89f0049c79714c873d03b02dae1c64b3f861f55163ecc16da06506c5b8a1d4f16650b3d9351c380153
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/1a4d199c8e9e9cac5128d32e6626fa7805175af9df015620ac0d5d45854ccf348ba494679d872d37301032e35a54fc7978fba1687e8721b2139aea7870cafa2f
  languageName: node
  linkType: hard

"is-docker@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-docker@npm:3.0.0"
  bin:
    is-docker: cli.js
  checksum: 10c0/d2c4f8e6d3e34df75a5defd44991b6068afad4835bb783b902fa12d13ebdb8f41b2a199dcb0b5ed2cb78bfee9e4c0bbdb69c2d9646f4106464674d3e697a5856
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/818dff679b64f19e228a8205a1e2d09989a98e98def3a817f889208cfcbf918d321b251aadf2c05918194803ebd2eb01b14fc9d0b2bea53d984f4137bfca5e97
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-generator-fn@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-generator-fn@npm:2.1.0"
  checksum: 10c0/2957cab387997a466cd0bf5c1b6047bd21ecb32bdcfd8996b15747aa01002c1c88731802f1b3d34ac99f4f6874b626418bd118658cf39380fe5fff32a3af9c4d
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10, is-generator-function@npm:^1.0.7":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/fdfa96c8087bf36fc4cd514b474ba2ff404219a4dd4cfa6cf5426404a1eed259bdcdb98f082a71029a48d01f27733e3436ecc6690129a7ec09cb0434bee03a2a
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-inside-container@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-inside-container@npm:1.0.0"
  dependencies:
    is-docker: "npm:^3.0.0"
  bin:
    is-inside-container: cli.js
  checksum: 10c0/a8efb0e84f6197e6ff5c64c52890fa9acb49b7b74fed4da7c95383965da6f0fa592b4dbd5e38a79f87fc108196937acdbcd758fcefc9b140e479b39ce1fcd1cd
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: 10c0/dd47904dbf286cd20aa58c5192161be1a67138485b9836d5a70433b21a45442e9611b8498b8ab1f839fc962c7620667a50535fdfb4a6bc7989b8858645c06b4d
  languageName: node
  linkType: hard

"is-map@npm:^2.0.2, is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 10c0/2c4d431b74e00fdda7162cd8e4b763d6f6f217edf97d4f8538b94b8702b150610e2c64961340015fe8df5b1fcee33ccd2e9b62619c4a8a3a155f8de6d6d355fc
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/97b451b41f25135ff021d85c436ff0100d84a039bb87ffd799cbcdbea81ef30c464ced38258cdd34f080be08fc3b076ca1f472086286d2aa43521d6ec6a79f53
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: 10c0/cf7d4ac35fb96bab6a1d2c3598fe5ebb29aafb52c0aaa482b5a3ed9d8ba3edc11631e3ec2637660c44b3ce0e61a08d54946e8af30dec0b60a7c27296c68ffd05
  languageName: node
  linkType: hard

"is-potential-custom-element-name@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-potential-custom-element-name@npm:1.0.1"
  checksum: 10c0/b73e2f22bc863b0939941d369486d308b43d7aef1f9439705e3582bfccaa4516406865e32c968a35f97a99396dac84e2624e67b0a16b0a15086a785e16ce7db9
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4, is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/1d3715d2b7889932349241680032e85d0b492cfcb045acb75ffc2c3085e8d561184f1f7e84b6f8321935b4aea39bc9c6ba74ed595b57ce4881a51dfdbc214e04
  languageName: node
  linkType: hard

"is-set@npm:^2.0.2, is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 10c0/f73732e13f099b2dc879c2a12341cfc22ccaca8dd504e6edae26484bd5707a35d503fba5b4daad530a9b088ced1ae6c9d8200fd92e09b428fe14ea79ce8080b7
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.2, is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/65158c2feb41ff1edd6bbd6fd8403a69861cf273ff36077982b5d4d68e1d59278c71691216a4a64632bd76d4792d4d1d2553901b6666d84ade13bba5ea7bc7db
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10c0/7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"is-string@npm:^1.0.7, is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/2f518b4e47886bb81567faba6ffd0d8a8333cf84336e2e78bf160693972e32ad00fe84b0926491cc598dee576fdc55642c92e62d0cbe96bf36f643b6f956f94d
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/f08f3e255c12442e833f75a9e2b84b2d4882fdfd920513cf2a4a2324f0a5b076c8fd913778e3ea5d258d5183e9d92c0cd20e04b03ab3df05316b049b2670af1e
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15, is-typed-array@npm:^1.1.3":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/415511da3669e36e002820584e264997ffe277ff136643a3126cc949197e6ca3334d0f12d084e83b1994af2e9c8141275c741cf2b7da5a2ff62dd0cac26f76c4
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: 10c0/00cbe3455c3756be68d2542c416cab888aebd5012781d6819749fefb15162ff23e38501fe681b3d751c73e8ff561ac09a5293eba6f58fdf0178462ce6dcb3453
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 10c0/443c35bb86d5e6cc5929cd9c75a4024bb0fff9586ed50b092f94e700b89c43a33b186b76dbc6d54f3d3d09ece689ab38dcdc1af6a482cbe79c0f2da0a17f1299
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/8e0a9c07b0c780949a100e2cab2b5560a48ecd4c61726923c1a9b77b6ab0aa0046c9e7fb2206042296817045376dee2c8ab1dabe08c7c3dfbf195b01275a085b
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/6491eba08acb8dc9532da23cb226b7d0192ede0b88f16199e592e4769db0a077119c1f5d2283d1e0d16d739115f70046e887e477eb0e66cd90e1bb29f28ba647
  languageName: node
  linkType: hard

"is-wsl@npm:^3.1.0":
  version: 3.1.0
  resolution: "is-wsl@npm:3.1.0"
  dependencies:
    is-inside-container: "npm:^1.0.0"
  checksum: 10c0/d3317c11995690a32c362100225e22ba793678fe8732660c6de511ae71a0ff05b06980cf21f98a6bf40d7be0e9e9506f859abe00a1118287d63e53d0a3d06947
  languageName: node
  linkType: hard

"isarray@npm:^1.0.0, isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10c0/18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10c0/4199f14a7a13da2177c66c31080008b7124331956f47bca57dd0b6ea9f11687aa25e565a2c7a2b519bc86988d10398e3049a1f5df13c9f6b7664154690ae79fd
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 10c0/6c7ff2106769e5f592ded1fb418f9f73b4411fd5a084387a5410538332b6567cd1763ff6b6cadca9b9eb2c443cce2f7ea7d7f1b8d315f9ce58539793b1e0922b
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^5.0.4":
  version: 5.2.1
  resolution: "istanbul-lib-instrument@npm:5.2.1"
  dependencies:
    "@babel/core": "npm:^7.12.3"
    "@babel/parser": "npm:^7.14.7"
    "@istanbuljs/schema": "npm:^0.1.2"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^6.3.0"
  checksum: 10c0/8a1bdf3e377dcc0d33ec32fe2b6ecacdb1e4358fd0eb923d4326bb11c67622c0ceb99600a680f3dad5d29c66fc1991306081e339b4d43d0b8a2ab2e1d910a6ee
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^6.0.0":
  version: 6.0.3
  resolution: "istanbul-lib-instrument@npm:6.0.3"
  dependencies:
    "@babel/core": "npm:^7.23.9"
    "@babel/parser": "npm:^7.23.9"
    "@istanbuljs/schema": "npm:^0.1.3"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^7.5.4"
  checksum: 10c0/a1894e060dd2a3b9f046ffdc87b44c00a35516f5e6b7baf4910369acca79e506fc5323a816f811ae23d82334b38e3ddeb8b3b331bd2c860540793b59a8689128
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: "npm:^3.0.0"
    make-dir: "npm:^4.0.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/84323afb14392de8b6a5714bd7e9af845cfbd56cfe71ed276cda2f5f1201aea673c7111901227ee33e68e4364e288d73861eb2ed48f6679d1e69a43b6d9b3ba7
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^4.0.0":
  version: 4.0.1
  resolution: "istanbul-lib-source-maps@npm:4.0.1"
  dependencies:
    debug: "npm:^4.1.1"
    istanbul-lib-coverage: "npm:^3.0.0"
    source-map: "npm:^0.6.1"
  checksum: 10c0/19e4cc405016f2c906dff271a76715b3e881fa9faeb3f09a86cb99b8512b3a5ed19cadfe0b54c17ca0e54c1142c9c6de9330d65506e35873994e06634eebeb66
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.3":
  version: 3.1.7
  resolution: "istanbul-reports@npm:3.1.7"
  dependencies:
    html-escaper: "npm:^2.0.0"
    istanbul-lib-report: "npm:^3.0.0"
  checksum: 10c0/a379fadf9cf8dc5dfe25568115721d4a7eb82fbd50b005a6672aff9c6989b20cc9312d7865814e0859cd8df58cbf664482e1d3604be0afde1f7fc3ccc1394a51
  languageName: node
  linkType: hard

"iterare@npm:1.2.1":
  version: 1.2.1
  resolution: "iterare@npm:1.2.1"
  checksum: 10c0/02667d486e3e83ead028ba8484d927498c2ceab7e8c6a69dd881fd02abc4114f00b13abb36b592252fbb578b6e6f99ca1dfc2835408b9158c9a112a9964f453f
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jake@npm:^10.8.5":
  version: 10.9.2
  resolution: "jake@npm:10.9.2"
  dependencies:
    async: "npm:^3.2.3"
    chalk: "npm:^4.0.2"
    filelist: "npm:^1.0.4"
    minimatch: "npm:^3.1.2"
  bin:
    jake: bin/cli.js
  checksum: 10c0/c4597b5ed9b6a908252feab296485a4f87cba9e26d6c20e0ca144fb69e0c40203d34a2efddb33b3d297b8bd59605e6c1f44f6221ca1e10e69175ecbf3ff5fe31
  languageName: node
  linkType: hard

"jalaali-js@npm:^1.2.7":
  version: 1.2.8
  resolution: "jalaali-js@npm:1.2.8"
  checksum: 10c0/a90e314b61a2c2eb644633659d95838ef0fed1ccd8b551482868dd5e902df683579a336893eca1215b58ae2410139185de51ebe1b7aecd6790ae4a5aa868877f
  languageName: node
  linkType: hard

"jest-changed-files@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-changed-files@npm:29.7.0"
  dependencies:
    execa: "npm:^5.0.0"
    jest-util: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
  checksum: 10c0/e071384d9e2f6bb462231ac53f29bff86f0e12394c1b49ccafbad225ce2ab7da226279a8a94f421949920bef9be7ef574fd86aee22e8adfa149be73554ab828b
  languageName: node
  linkType: hard

"jest-circus@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-circus@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/expect": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    co: "npm:^4.6.0"
    dedent: "npm:^1.0.0"
    is-generator-fn: "npm:^2.0.0"
    jest-each: "npm:^29.7.0"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
    pretty-format: "npm:^29.7.0"
    pure-rand: "npm:^6.0.0"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.3"
  checksum: 10c0/8d15344cf7a9f14e926f0deed64ed190c7a4fa1ed1acfcd81e4cc094d3cc5bf7902ebb7b874edc98ada4185688f90c91e1747e0dfd7ac12463b097968ae74b5e
  languageName: node
  linkType: hard

"jest-cli@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-cli@npm:29.7.0"
  dependencies:
    "@jest/core": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    create-jest: "npm:^29.7.0"
    exit: "npm:^0.1.2"
    import-local: "npm:^3.0.2"
    jest-config: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    yargs: "npm:^17.3.1"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 10c0/a658fd55050d4075d65c1066364595962ead7661711495cfa1dfeecf3d6d0a8ffec532f3dbd8afbb3e172dd5fd2fb2e813c5e10256e7cf2fea766314942fb43a
  languageName: node
  linkType: hard

"jest-config@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-config@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@jest/test-sequencer": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    babel-jest: "npm:^29.7.0"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    deepmerge: "npm:^4.2.2"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    jest-circus: "npm:^29.7.0"
    jest-environment-node: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-runner: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    parse-json: "npm:^5.2.0"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-json-comments: "npm:^3.1.1"
  peerDependencies:
    "@types/node": "*"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    "@types/node":
      optional: true
    ts-node:
      optional: true
  checksum: 10c0/bab23c2eda1fff06e0d104b00d6adfb1d1aabb7128441899c9bff2247bd26710b050a5364281ce8d52b46b499153bf7e3ee88b19831a8f3451f1477a0246a0f1
  languageName: node
  linkType: hard

"jest-diff@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-diff@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    diff-sequences: "npm:^29.6.3"
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/89a4a7f182590f56f526443dde69acefb1f2f0c9e59253c61d319569856c4931eae66b8a3790c443f529267a0ddba5ba80431c585deed81827032b2b2a1fc999
  languageName: node
  linkType: hard

"jest-docblock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-docblock@npm:29.7.0"
  dependencies:
    detect-newline: "npm:^3.0.0"
  checksum: 10c0/d932a8272345cf6b6142bb70a2bb63e0856cc0093f082821577ea5bdf4643916a98744dfc992189d2b1417c38a11fa42466f6111526bc1fb81366f56410f3be9
  languageName: node
  linkType: hard

"jest-each@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-each@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    jest-get-type: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/f7f9a90ebee80cc688e825feceb2613627826ac41ea76a366fa58e669c3b2403d364c7c0a74d862d469b103c843154f8456d3b1c02b487509a12afa8b59edbb4
  languageName: node
  linkType: hard

"jest-environment-node@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-environment-node@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/61f04fec077f8b1b5c1a633e3612fc0c9aa79a0ab7b05600683428f1e01a4d35346c474bde6f439f9fcc1a4aa9a2861ff852d079a43ab64b02105d1004b2592b
  languageName: node
  linkType: hard

"jest-get-type@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-get-type@npm:29.6.3"
  checksum: 10c0/552e7a97a983d3c2d4e412a44eb7de0430ff773dd99f7500962c268d6dfbfa431d7d08f919c9d960530e5f7f78eb47f267ad9b318265e5092b3ff9ede0db7c2b
  languageName: node
  linkType: hard

"jest-haste-map@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-haste-map@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/graceful-fs": "npm:^4.1.3"
    "@types/node": "npm:*"
    anymatch: "npm:^3.0.3"
    fb-watchman: "npm:^2.0.0"
    fsevents: "npm:^2.3.2"
    graceful-fs: "npm:^4.2.9"
    jest-regex-util: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    walker: "npm:^1.0.8"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/2683a8f29793c75a4728787662972fedd9267704c8f7ef9d84f2beed9a977f1cf5e998c07b6f36ba5603f53cb010c911fe8cd0ac9886e073fe28ca66beefd30c
  languageName: node
  linkType: hard

"jest-leak-detector@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-leak-detector@npm:29.7.0"
  dependencies:
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/71bb9f77fc489acb842a5c7be030f2b9acb18574dc9fb98b3100fc57d422b1abc55f08040884bd6e6dbf455047a62f7eaff12aa4058f7cbdc11558718ca6a395
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-matcher-utils@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    jest-diff: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/0d0e70b28fa5c7d4dce701dc1f46ae0922102aadc24ed45d594dd9b7ae0a8a6ef8b216718d1ab79e451291217e05d4d49a82666e1a3cc2b428b75cd9c933244e
  languageName: node
  linkType: hard

"jest-message-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-message-util@npm:29.7.0"
  dependencies:
    "@babel/code-frame": "npm:^7.12.13"
    "@jest/types": "npm:^29.6.3"
    "@types/stack-utils": "npm:^2.0.0"
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    micromatch: "npm:^4.0.4"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.3"
  checksum: 10c0/850ae35477f59f3e6f27efac5215f706296e2104af39232bb14e5403e067992afb5c015e87a9243ec4d9df38525ef1ca663af9f2f4766aa116f127247008bd22
  languageName: node
  linkType: hard

"jest-mock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-mock@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/7b9f8349ee87695a309fe15c46a74ab04c853369e5c40952d68061d9dc3159a0f0ed73e215f81b07ee97a9faaf10aebe5877a9d6255068a0977eae6a9ff1d5ac
  languageName: node
  linkType: hard

"jest-pnp-resolver@npm:^1.2.2":
  version: 1.2.3
  resolution: "jest-pnp-resolver@npm:1.2.3"
  peerDependencies:
    jest-resolve: "*"
  peerDependenciesMeta:
    jest-resolve:
      optional: true
  checksum: 10c0/86eec0c78449a2de733a6d3e316d49461af6a858070e113c97f75fb742a48c2396ea94150cbca44159ffd4a959f743a47a8b37a792ef6fdad2cf0a5cba973fac
  languageName: node
  linkType: hard

"jest-regex-util@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-regex-util@npm:29.6.3"
  checksum: 10c0/4e33fb16c4f42111159cafe26397118dcfc4cf08bc178a67149fb05f45546a91928b820894572679d62559839d0992e21080a1527faad65daaae8743a5705a3b
  languageName: node
  linkType: hard

"jest-resolve-dependencies@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve-dependencies@npm:29.7.0"
  dependencies:
    jest-regex-util: "npm:^29.6.3"
    jest-snapshot: "npm:^29.7.0"
  checksum: 10c0/b6e9ad8ae5b6049474118ea6441dfddd385b6d1fc471db0136f7c8fbcfe97137a9665e4f837a9f49f15a29a1deb95a14439b7aec812f3f99d08f228464930f0d
  languageName: node
  linkType: hard

"jest-resolve@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-pnp-resolver: "npm:^1.2.2"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    resolve: "npm:^1.20.0"
    resolve.exports: "npm:^2.0.0"
    slash: "npm:^3.0.0"
  checksum: 10c0/59da5c9c5b50563e959a45e09e2eace783d7f9ac0b5dcc6375dea4c0db938d2ebda97124c8161310082760e8ebbeff9f6b177c15ca2f57fb424f637a5d2adb47
  languageName: node
  linkType: hard

"jest-runner@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runner@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/environment": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    emittery: "npm:^0.13.1"
    graceful-fs: "npm:^4.2.9"
    jest-docblock: "npm:^29.7.0"
    jest-environment-node: "npm:^29.7.0"
    jest-haste-map: "npm:^29.7.0"
    jest-leak-detector: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-resolve: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-watcher: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
    source-map-support: "npm:0.5.13"
  checksum: 10c0/2194b4531068d939f14c8d3274fe5938b77fa73126aedf9c09ec9dec57d13f22c72a3b5af01ac04f5c1cf2e28d0ac0b4a54212a61b05f10b5d6b47f2a1097bb4
  languageName: node
  linkType: hard

"jest-runtime@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runtime@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/globals": "npm:^29.7.0"
    "@jest/source-map": "npm:^29.6.3"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    cjs-module-lexer: "npm:^1.0.0"
    collect-v8-coverage: "npm:^1.0.0"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-mock: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-bom: "npm:^4.0.0"
  checksum: 10c0/7cd89a1deda0bda7d0941835434e44f9d6b7bd50b5c5d9b0fc9a6c990b2d4d2cab59685ab3cb2850ed4cc37059f6de903af5a50565d7f7f1192a77d3fd6dd2a6
  languageName: node
  linkType: hard

"jest-snapshot@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-snapshot@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@babel/generator": "npm:^7.7.2"
    "@babel/plugin-syntax-jsx": "npm:^7.7.2"
    "@babel/plugin-syntax-typescript": "npm:^7.7.2"
    "@babel/types": "npm:^7.3.3"
    "@jest/expect-utils": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    babel-preset-current-node-syntax: "npm:^1.0.0"
    chalk: "npm:^4.0.0"
    expect: "npm:^29.7.0"
    graceful-fs: "npm:^4.2.9"
    jest-diff: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    natural-compare: "npm:^1.4.0"
    pretty-format: "npm:^29.7.0"
    semver: "npm:^7.5.3"
  checksum: 10c0/6e9003c94ec58172b4a62864a91c0146513207bedf4e0a06e1e2ac70a4484088a2683e3a0538d8ea913bcfd53dc54a9b98a98cdfa562e7fe1d1339aeae1da570
  languageName: node
  linkType: hard

"jest-util@npm:^29.0.0, jest-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-util@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    graceful-fs: "npm:^4.2.9"
    picomatch: "npm:^2.2.3"
  checksum: 10c0/bc55a8f49fdbb8f51baf31d2a4f312fb66c9db1483b82f602c9c990e659cdd7ec529c8e916d5a89452ecbcfae4949b21b40a7a59d4ffc0cd813a973ab08c8150
  languageName: node
  linkType: hard

"jest-validate@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-validate@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    camelcase: "npm:^6.2.0"
    chalk: "npm:^4.0.0"
    jest-get-type: "npm:^29.6.3"
    leven: "npm:^3.1.0"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/a20b930480c1ed68778c739f4739dce39423131bc070cd2505ddede762a5570a256212e9c2401b7ae9ba4d7b7c0803f03c5b8f1561c62348213aba18d9dbece2
  languageName: node
  linkType: hard

"jest-watcher@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-watcher@npm:29.7.0"
  dependencies:
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    ansi-escapes: "npm:^4.2.1"
    chalk: "npm:^4.0.0"
    emittery: "npm:^0.13.1"
    jest-util: "npm:^29.7.0"
    string-length: "npm:^4.0.1"
  checksum: 10c0/ec6c75030562fc8f8c727cb8f3b94e75d831fc718785abfc196e1f2a2ebc9a2e38744a15147170039628a853d77a3b695561ce850375ede3a4ee6037a2574567
  languageName: node
  linkType: hard

"jest-worker@npm:^27.4.5":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "npm:*"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.0.0"
  checksum: 10c0/8c4737ffd03887b3c6768e4cc3ca0269c0336c1e4b1b120943958ddb035ed2a0fc6acab6dc99631720a3720af4e708ff84fb45382ad1e83c27946adf3623969b
  languageName: node
  linkType: hard

"jest-worker@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-worker@npm:29.7.0"
  dependencies:
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.0.0"
  checksum: 10c0/5570a3a005b16f46c131968b8a5b56d291f9bbb85ff4217e31c80bd8a02e7de799e59a54b95ca28d5c302f248b54cbffde2d177c2f0f52ffcee7504c6eabf660
  languageName: node
  linkType: hard

"jest@npm:^29.5.0":
  version: 29.7.0
  resolution: "jest@npm:29.7.0"
  dependencies:
    "@jest/core": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    import-local: "npm:^3.0.2"
    jest-cli: "npm:^29.7.0"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 10c0/f40eb8171cf147c617cc6ada49d062fbb03b4da666cb8d39cdbfb739a7d75eea4c3ca150fb072d0d273dce0c753db4d0467d54906ad0293f59c54f9db4a09d8b
  languageName: node
  linkType: hard

"jmespath@npm:0.16.0":
  version: 0.16.0
  resolution: "jmespath@npm:0.16.0"
  checksum: 10c0/84cdca62c4a3d339701f01cc53decf16581c76ce49e6455119be1c5f6ab09a19e6788372536bd261d348d21cd817981605f8debae67affadba966219a2bac1c5
  languageName: node
  linkType: hard

"jpeg-exif@npm:^1.1.4":
  version: 1.1.4
  resolution: "jpeg-exif@npm:1.1.4"
  checksum: 10c0/0f9225b2423184d60c66b3d7361176801c17ede92fc9b3c044fcf00f379a5a1d424b360ecf0027dda47d405d253c7b62bf5b353fb08b2589e3650f38cc575e82
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:4.1.0, js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/6746baaaeac312c4db8e75fa22331d9a04cccb7792d126ed8ce6a0bbcfef0cedaddd0c5098fade53db067c09fe00aa1c957674b4765610a8b06a5a189e46433b
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsdom@npm:^26.1.0":
  version: 26.1.0
  resolution: "jsdom@npm:26.1.0"
  dependencies:
    cssstyle: "npm:^4.2.1"
    data-urls: "npm:^5.0.0"
    decimal.js: "npm:^10.5.0"
    html-encoding-sniffer: "npm:^4.0.0"
    http-proxy-agent: "npm:^7.0.2"
    https-proxy-agent: "npm:^7.0.6"
    is-potential-custom-element-name: "npm:^1.0.1"
    nwsapi: "npm:^2.2.16"
    parse5: "npm:^7.2.1"
    rrweb-cssom: "npm:^0.8.0"
    saxes: "npm:^6.0.0"
    symbol-tree: "npm:^3.2.4"
    tough-cookie: "npm:^5.1.1"
    w3c-xmlserializer: "npm:^5.0.0"
    webidl-conversions: "npm:^7.0.0"
    whatwg-encoding: "npm:^3.1.1"
    whatwg-mimetype: "npm:^4.0.0"
    whatwg-url: "npm:^14.1.1"
    ws: "npm:^8.18.0"
    xml-name-validator: "npm:^5.0.0"
  peerDependencies:
    canvas: ^3.0.0
  peerDependenciesMeta:
    canvas:
      optional: true
  checksum: 10c0/5b14a5bc32ce077a06fb42d1ab95b1191afa5cbbce8859e3b96831c5143becbbcbf0511d4d4934e922d2901443ced2cdc3b734c1cf30b5f73b3e067ce457d0f4
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10c0/0d1c91569d9588e7eef2b49b59851f297f3ab93c7b35c7c221e288099322be6b562767d11e4821da500f3219542b9afd2e54c5dc573107c1126ed1080f8e96d7
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0, json-parse-even-better-errors@npm:^2.3.1":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10c0/140932564c8f0b88455432e0f33c4cb4086b8868e37524e07e723f4eaedb9425bdc2bafd71bd1d9765bd15fd1e2d126972bc83990f55c467168c228c24d665f3
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10c0/108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 10c0/71e30015d7f3d6dc1c316d6298047c8ef98a06d31ad064919976583eb61e1018a60a0067338f0f79cabc00d84af3fcc489bd48ce8a46ea165d9541ba17fb30c6
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10c0/cb168b61fd4de83e58d09aaa6425ef71001bae30d260e2c57e7d09a5fd82223e2f22a042dedaab8db23b7d9ae46854b08bb1f91675a8be11c5cffebef5fb66a5
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: "npm:^1.2.0"
  bin:
    json5: lib/cli.js
  checksum: 10c0/9ee316bf21f000b00752e6c2a3b79ecf5324515a5c60ee88983a1910a45426b643a4f3461657586e8aeca87aaf96f0a519b0516d2ae527a6c3e7eed80f68717f
  languageName: node
  linkType: hard

"json5@npm:^2.2.2, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonc-parser@npm:3.2.0":
  version: 3.2.0
  resolution: "jsonc-parser@npm:3.2.0"
  checksum: 10c0/5a12d4d04dad381852476872a29dcee03a57439574e4181d91dca71904fcdcc5e8e4706c0a68a2c61ad9810e1e1c5806b5100d52d3e727b78f5cdc595401045b
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"jsonwebtoken@npm:9.0.2, jsonwebtoken@npm:^9.0.0":
  version: 9.0.2
  resolution: "jsonwebtoken@npm:9.0.2"
  dependencies:
    jws: "npm:^3.2.2"
    lodash.includes: "npm:^4.3.0"
    lodash.isboolean: "npm:^3.0.3"
    lodash.isinteger: "npm:^4.0.4"
    lodash.isnumber: "npm:^3.0.3"
    lodash.isplainobject: "npm:^4.0.6"
    lodash.isstring: "npm:^4.0.1"
    lodash.once: "npm:^4.0.0"
    ms: "npm:^2.1.1"
    semver: "npm:^7.5.4"
  checksum: 10c0/d287a29814895e866db2e5a0209ce730cbc158441a0e5a70d5e940eb0d28ab7498c6bf45029cc8b479639bca94056e9a7f254e2cdb92a2f5750c7f358657a131
  languageName: node
  linkType: hard

"jszip@npm:^3.10.1":
  version: 3.10.1
  resolution: "jszip@npm:3.10.1"
  dependencies:
    lie: "npm:~3.3.0"
    pako: "npm:~1.0.2"
    readable-stream: "npm:~2.3.6"
    setimmediate: "npm:^1.0.5"
  checksum: 10c0/58e01ec9c4960383fb8b38dd5f67b83ccc1ec215bf74c8a5b32f42b6e5fb79fada5176842a11409c4051b5b94275044851814a31076bf49e1be218d3ef57c863
  languageName: node
  linkType: hard

"jwa@npm:^1.4.1":
  version: 1.4.1
  resolution: "jwa@npm:1.4.1"
  dependencies:
    buffer-equal-constant-time: "npm:1.0.1"
    ecdsa-sig-formatter: "npm:1.0.11"
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/5c533540bf38702e73cf14765805a94027c66a0aa8b16bc3e89d8d905e61a4ce2791e87e21be97d1293a5ee9d4f3e5e47737e671768265ca4f25706db551d5e9
  languageName: node
  linkType: hard

"jws@npm:^3.2.2":
  version: 3.2.2
  resolution: "jws@npm:3.2.2"
  dependencies:
    jwa: "npm:^1.4.1"
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/e770704533d92df358adad7d1261fdecad4d7b66fa153ba80d047e03ca0f1f73007ce5ed3fbc04d2eba09ba6e7e6e645f351e08e5ab51614df1b0aa4f384dfff
  languageName: node
  linkType: hard

"keyv@npm:^4.5.3":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10c0/aa52f3c5e18e16bb6324876bb8b59dd02acf782a4b789c7b2ae21107fab95fab3890ed448d4f8dba80ce05391eeac4bfabb4f02a20221342982f806fa2cf271e
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: 10c0/cd3a0b8878e7d6d3799e54340efe3591ca787d9f95f109f28129bdd2915e37807bf8918bb295ab86afb8c82196beec5a1adcaf29042ce3f2bd932b038fe3aa4b
  languageName: node
  linkType: hard

"lazystream@npm:^1.0.0":
  version: 1.0.1
  resolution: "lazystream@npm:1.0.1"
  dependencies:
    readable-stream: "npm:^2.0.5"
  checksum: 10c0/ea4e509a5226ecfcc303ba6782cc269be8867d372b9bcbd625c88955df1987ea1a20da4643bf9270336415a398d33531ebf0d5f0d393b9283dc7c98bfcbd7b69
  languageName: node
  linkType: hard

"leac@npm:^0.6.0":
  version: 0.6.0
  resolution: "leac@npm:0.6.0"
  checksum: 10c0/5257781e10791ef8462eb1cbe5e48e3cda7692486f2a775265d6f5216cc088960c62f138163b8df0dcf2119d18673bfe7b050d6b41543d92a7b7ac90e4eb1e8b
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 10c0/cd778ba3fbab0f4d0500b7e87d1f6e1f041507c56fdcd47e8256a3012c98aaee371d4c15e0a76e0386107af2d42e2b7466160a2d80688aaa03e66e49949f42df
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10c0/effb03cad7c89dfa5bd4f6989364bfc79994c2042ec5966cb9b95990e2edee5cd8969ddf42616a0373ac49fac1403437deaf6e9050fbbaa3546093a59b9ac94e
  languageName: node
  linkType: hard

"libphonenumber-js@npm:^1.10.53":
  version: 1.11.19
  resolution: "libphonenumber-js@npm:1.11.19"
  checksum: 10c0/cf02204746dd89cacbd5830c32e6ea4a7e4593efa84b0707207900d79bb0bbd19bd38eb6da7544f49638d609b8c4b9dcbd7e50ea6506e957cd149f6a3c2c7b2b
  languageName: node
  linkType: hard

"lie@npm:~3.3.0":
  version: 3.3.0
  resolution: "lie@npm:3.3.0"
  dependencies:
    immediate: "npm:~3.0.5"
  checksum: 10c0/56dd113091978f82f9dc5081769c6f3b947852ecf9feccaf83e14a123bc630c2301439ce6182521e5fbafbde88e88ac38314327a4e0493a1bea7e0699a7af808
  languageName: node
  linkType: hard

"linebreak@npm:^1.0.2":
  version: 1.1.0
  resolution: "linebreak@npm:1.1.0"
  dependencies:
    base64-js: "npm:0.0.8"
    unicode-trie: "npm:^2.0.0"
  checksum: 10c0/b350c90d7b10db30345ed56cdb869548110ce73ccdc4337100eaee50755eed78e9823490e6f2d7ed0adde14f7ed2a12d8583015e072c54f34dc70b316fde133d
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10c0/3da6ee62d4cd9f03f5dc90b4df2540fb85b352081bee77fe4bbcd12c9000ead7f35e0a38b8d09a9bb99b13223446dd8689ff3c4959807620726d788701a83d2d
  languageName: node
  linkType: hard

"loader-runner@npm:^4.2.0":
  version: 4.3.0
  resolution: "loader-runner@npm:4.3.0"
  checksum: 10c0/a44d78aae0907a72f73966fe8b82d1439c8c485238bd5a864b1b9a2a3257832effa858790241e6b37876b5446a78889adf2fcc8dd897ce54c089ecc0a0ce0bf0
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10c0/33a1c5247e87e022f9713e6213a744557a3e9ec32c5d0b5efb10aa3a38177615bf90221a5592674857039c1a0fd2063b82f285702d37b792d973e9e72ace6c59
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10c0/d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"lodash.clonedeep@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.clonedeep@npm:4.5.0"
  checksum: 10c0/2caf0e4808f319d761d2939ee0642fa6867a4bbf2cfce43276698828380756b99d4c4fa226d881655e6ac298dd453fe12a5ec8ba49861777759494c534936985
  languageName: node
  linkType: hard

"lodash.includes@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.includes@npm:4.3.0"
  checksum: 10c0/7ca498b9b75bf602d04e48c0adb842dfc7d90f77bcb2a91a2b2be34a723ad24bc1c8b3683ec6b2552a90f216c723cdea530ddb11a3320e08fa38265703978f4b
  languageName: node
  linkType: hard

"lodash.isboolean@npm:^3.0.3":
  version: 3.0.3
  resolution: "lodash.isboolean@npm:3.0.3"
  checksum: 10c0/0aac604c1ef7e72f9a6b798e5b676606042401dd58e49f051df3cc1e3adb497b3d7695635a5cbec4ae5f66456b951fdabe7d6b387055f13267cde521f10ec7f7
  languageName: node
  linkType: hard

"lodash.isinteger@npm:^4.0.4":
  version: 4.0.4
  resolution: "lodash.isinteger@npm:4.0.4"
  checksum: 10c0/4c3e023a2373bf65bf366d3b8605b97ec830bca702a926939bcaa53f8e02789b6a176e7f166b082f9365bfec4121bfeb52e86e9040cb8d450e64c858583f61b7
  languageName: node
  linkType: hard

"lodash.isnumber@npm:^3.0.3":
  version: 3.0.3
  resolution: "lodash.isnumber@npm:3.0.3"
  checksum: 10c0/2d01530513a1ee4f72dd79528444db4e6360588adcb0e2ff663db2b3f642d4bb3d687051ae1115751ca9082db4fdef675160071226ca6bbf5f0c123dbf0aa12d
  languageName: node
  linkType: hard

"lodash.isplainobject@npm:^4.0.6":
  version: 4.0.6
  resolution: "lodash.isplainobject@npm:4.0.6"
  checksum: 10c0/afd70b5c450d1e09f32a737bed06ff85b873ecd3d3d3400458725283e3f2e0bb6bf48e67dbe7a309eb371a822b16a26cca4a63c8c52db3fc7dc9d5f9dd324cbb
  languageName: node
  linkType: hard

"lodash.isstring@npm:^4.0.1":
  version: 4.0.1
  resolution: "lodash.isstring@npm:4.0.1"
  checksum: 10c0/09eaf980a283f9eef58ef95b30ec7fee61df4d6bf4aba3b5f096869cc58f24c9da17900febc8ffd67819b4e29de29793190e88dc96983db92d84c95fa85d1c92
  languageName: node
  linkType: hard

"lodash.memoize@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.memoize@npm:4.1.2"
  checksum: 10c0/c8713e51eccc650422716a14cece1809cfe34bc5ab5e242b7f8b4e2241c2483697b971a604252807689b9dd69bfe3a98852e19a5b89d506b000b4187a1285df8
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10c0/402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"lodash.once@npm:^4.0.0":
  version: 4.1.1
  resolution: "lodash.once@npm:4.1.1"
  checksum: 10c0/46a9a0a66c45dd812fcc016e46605d85ad599fe87d71a02f6736220554b52ffbe82e79a483ad40f52a8a95755b0d1077fba259da8bfb6694a7abbf4a48f1fc04
  languageName: node
  linkType: hard

"lodash@npm:4.17.21, lodash@npm:^4.17.15, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: "npm:^4.1.0"
    is-unicode-supported: "npm:^0.1.0"
  checksum: 10c0/67f445a9ffa76db1989d0fa98586e5bc2fd5247260dafb8ad93d9f0ccd5896d53fb830b0e54dade5ad838b9de2006c826831a3c528913093af20dff8bd24aca6
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0, lru-cache@npm:^10.2.2, lru-cache@npm:^10.4.3":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"luxon@npm:^3.2.1, luxon@npm:^3.5.0":
  version: 3.5.0
  resolution: "luxon@npm:3.5.0"
  checksum: 10c0/335789bba95077db831ef99894edadeb23023b3eb2137a1b56acd0d290082b691cf793143d69e30bc069ec95f0b49f36419f48e951c68014f19ffe12045e3494
  languageName: node
  linkType: hard

"macos-release@npm:^2.5.0":
  version: 2.5.1
  resolution: "macos-release@npm:2.5.1"
  checksum: 10c0/fd03674e0b91e88a82cabecb75d75bc562863b186a22eac857f7d90c117486e44e02bede0926315637749aaaa934415bd1c2d0c0b53b78a86b729f3c165c5850
  languageName: node
  linkType: hard

"magic-string@npm:0.30.0":
  version: 0.30.0
  resolution: "magic-string@npm:0.30.0"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.4.13"
  checksum: 10c0/5fac57cf190bee966d3b5c55e0c23d6148b043a43220de91a369c4a81301b483418712b38440d15055a2ac04beec63dea4866a4e5c84ad6b919186e1c5c61241
  languageName: node
  linkType: hard

"make-dir@npm:^3.1.0":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: "npm:^6.0.0"
  checksum: 10c0/56aaafefc49c2dfef02c5c95f9b196c4eb6988040cf2c712185c7fe5c99b4091591a7fc4d4eafaaefa70ff763a26f6ab8c3ff60b9e75ea19876f49b18667ecaa
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: "npm:^7.5.3"
  checksum: 10c0/69b98a6c0b8e5c4fe9acb61608a9fbcfca1756d910f51e5dbe7a9e5cfb74fca9b8a0c8a0ffdf1294a740826c1ab4871d5bf3f62f72a3049e5eac6541ddffed68
  languageName: node
  linkType: hard

"make-error@npm:^1.1.1, make-error@npm:^1.3.6":
  version: 1.3.6
  resolution: "make-error@npm:1.3.6"
  checksum: 10c0/171e458d86854c6b3fc46610cfacf0b45149ba043782558c6875d9f42f222124384ad0b468c92e996d815a8a2003817a710c0a160e49c1c394626f76fa45396f
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: "npm:1.0.5"
  checksum: 10c0/b0e6e599780ce6bab49cc413eba822f7d1f0dfebd1c103eaa3785c59e43e22c59018323cf9e1708f0ef5329e94a745d163fcbb6bff8e4c6742f9be9e86f3500c
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 10c0/d160f31246907e79fed398470285f21bafb45a62869dc469b1c8877f3f064f5eabc4bcc122f9479b8b605bc5c76187d7871cf84c4ee3ecd3e487da1993279928
  languageName: node
  linkType: hard

"memfs@npm:^3.4.1":
  version: 3.5.3
  resolution: "memfs@npm:3.5.3"
  dependencies:
    fs-monkey: "npm:^1.0.4"
  checksum: 10c0/038fc81bce17ea92dde15aaa68fa0fdaf4960c721ce3ffc7c2cb87a259333f5159784ea48b3b72bf9e054254d9d0d0d5209d0fdc3d07d08653a09933b168fbd7
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.1":
  version: 1.0.1
  resolution: "merge-descriptors@npm:1.0.1"
  checksum: 10c0/b67d07bd44cfc45cebdec349bb6e1f7b077ee2fd5beb15d1f7af073849208cb6f144fe403e29a36571baf3f4e86469ac39acf13c318381e958e186b2766f54ec
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10c0/867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"methods@npm:^1.1.2, methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 10c0/bdf7cc72ff0a33e3eede03708c08983c4d7a173f91348b4b1e4f47d4cdbf734433ad971e7d1e8c77247d9e5cd8adb81ea4c67b0a2db526b758b2233d7814b8b2
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.0, micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/166fa6eb926b9553f32ef81f5f531d27b4ce7da60e5baf8c021d043b27a388fb95e46a8038d5045877881e673f8134122b59624d5cecbd16eb50a42e7a6b5ca8
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-db@npm:>= 1.43.0 < 2":
  version: 1.54.0
  resolution: "mime-db@npm:1.54.0"
  checksum: 10c0/8d907917bc2a90fa2df842cdf5dfeaf509adc15fe0531e07bb2f6ab15992416479015828d6a74200041c492e42cce3ebf78e5ce714388a0a538ea9c53eece284
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.27, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mime@npm:2.6.0":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/a7f2589900d9c16e3bdf7672d16a6274df903da958c1643c9c45771f0478f3846dcb1097f31eb9178452570271361e2149310931ec705c037210fc69639c8e6c
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10c0/b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.0.5, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1, minimatch@npm:^5.1.0":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/3defdfd230914f22a8da203747c42ee3c405c39d4d37ffda284dac5e45b7e1f6c49aa8be606509002898e73091ff2a3bbfc59c2c6c71d4660609f63aa92f98e3
  languageName: node
  linkType: hard

"minimatch@npm:^8.0.2":
  version: 8.0.4
  resolution: "minimatch@npm:8.0.4"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/a0a394c356dd5b4cb7f821720841a82fa6f07c9c562c5b716909d1b6ec5e56a7e4c4b5029da26dd256b7d2b3a3f38cbf9ddd8680e887b9b5282b09c05501c1ca
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.5, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/7fa30ce7c373fb6f94c086b374fff1589fd7e78451855d2d06c2e2d9df936d131e73e952163063016592ed3081444bd8d1ea608533313b0149156ce23311da4b
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^4.2.4":
  version: 4.2.8
  resolution: "minipass@npm:4.2.8"
  checksum: 10c0/4ea76b030d97079f4429d6e8a8affd90baf1b6a1898977c8ccce4701c5a2ba2792e033abc6709373f25c2c4d4d95440d9d5e9464b46b7b76ca44d2ce26d939ce
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 10c0/a91d8043f691796a8ac88df039da19933ef0f633e3d7f0d35dcd5373af49131cf2399bfc355f41515dc495e3990369c3858cd319e5c2722b4753c90bf3152462
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: "npm:^3.0.0"
    yallist: "npm:^4.0.0"
  checksum: 10c0/64fae024e1a7d0346a1102bb670085b17b7f95bf6cfdf5b128772ec8faf9ea211464ea4add406a3a6384a7d87a0cd1a96263692134323477b4fb43659a6cab78
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
    rimraf: "npm:^5.0.5"
  checksum: 10c0/82f8bf70da8af656909a8ee299d7ed3b3372636749d29e105f97f20e88971be31f5ed7642f2e898f00283b68b701cc01307401cdc209b0efc5dd3818220e5093
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.4":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: "npm:^1.2.6"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/e2e2be789218807b58abced04e7b49851d9e46e88a2f9539242cc8a92c9b5c3a0b9bab360bd3014e02a140fc4fbc58e31176c408b493f8a2a6f4986bd7527b01
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/46ea0f3ffa8bc6a5bc0c7081ffc3907777f0ed6516888d40a518c5111f8366d97d2678911ad1a6882bf592fa9de6c784fea32e1687bb94e1f4944170af48a5cf
  languageName: node
  linkType: hard

"mkdirp@npm:^2.1.3":
  version: 2.1.6
  resolution: "mkdirp@npm:2.1.6"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/96f551c651dd8f5f9435d53df1a7b9bfc553be769ee6da5192c37c1f303a376ef1c6996f96913d4a8d357060451d4526a346031d1919f92c58806a5fa3cd8dfe
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"moment-timezone@npm:^0.5.43, moment-timezone@npm:^0.5.47":
  version: 0.5.48
  resolution: "moment-timezone@npm:0.5.48"
  dependencies:
    moment: "npm:^2.29.4"
  checksum: 10c0/ab14ec9d94bc33f29ac18e5417b7f8aca0b17130b952c5cc9697b8fea839e5ece9313af5fd3c9703a05db472b1560ddbfc7ad2aa24aac9afd047d6da6c3c6033
  languageName: node
  linkType: hard

"moment@npm:^2.29.4":
  version: 2.30.1
  resolution: "moment@npm:2.30.1"
  checksum: 10c0/865e4279418c6de666fca7786607705fd0189d8a7b7624e2e56be99290ac846f90878a6f602e34b4e0455c549b85385b1baf9966845962b313699e7cb847543a
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.0.0, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"multer@npm:1.4.4-lts.1":
  version: 1.4.4-lts.1
  resolution: "multer@npm:1.4.4-lts.1"
  dependencies:
    append-field: "npm:^1.0.0"
    busboy: "npm:^1.0.0"
    concat-stream: "npm:^1.5.2"
    mkdirp: "npm:^0.5.4"
    object-assign: "npm:^4.1.1"
    type-is: "npm:^1.6.4"
    xtend: "npm:^4.0.0"
  checksum: 10c0/63277d3483869f424274ef8ce6ab7ff4ce9d2c1cc69e707fc8b5d9b2b348ae6f742809e0b357a591dea885d147594bcd06528d3d6bbe32046115d4a7e126b954
  languageName: node
  linkType: hard

"mute-stream@npm:0.0.8":
  version: 0.0.8
  resolution: "mute-stream@npm:0.0.8"
  checksum: 10c0/18d06d92e5d6d45e2b63c0e1b8f25376af71748ac36f53c059baa8b76ffac31c5ab225480494e7d35d30215ecdb18fed26ec23cafcd2f7733f2f14406bcd19e2
  languageName: node
  linkType: hard

"mz@npm:^2.4.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: "npm:^1.0.0"
    object-assign: "npm:^4.0.1"
    thenify-all: "npm:^1.0.0"
  checksum: 10c0/103114e93f87362f0b56ab5b2e7245051ad0276b646e3902c98397d18bb8f4a77f2ea4a2c9d3ad516034ea3a56553b60d3f5f78220001ca4c404bd711bd0af39
  languageName: node
  linkType: hard

"natural-compare-lite@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare-lite@npm:1.4.0"
  checksum: 10c0/f6cef26f5044515754802c0fc475d81426f3b90fe88c20fabe08771ce1f736ce46e0397c10acb569a4dd0acb84c7f1ee70676122f95d5bfdd747af3a6c6bbaa8
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10c0/f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"negotiator@npm:~0.6.4":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 10c0/3e677139c7fb7628a6f36335bf11a885a62c21d5390204590a1a214a5631fcbe5ea74ef6a610b60afe84b4d975cbe0566a23f20ee17c77c73e74b80032108dea
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: 10c0/c2f5a604a54a8ec5438a342e1f356dff4bc33ccccdb6dc668d94fe8e5eccfc9d2c2eea6064b0967a767ba63b33763f51ccf2cd2441b461a7322656c1f06b3f5d
  languageName: node
  linkType: hard

"nestjs-spelunker@npm:^1.1.5":
  version: 1.3.2
  resolution: "nestjs-spelunker@npm:1.3.2"
  dependencies:
    "@ogma/styler": "npm:^1.0.0"
  peerDependencies:
    "@nestjs/common": ">6.11.0"
    "@nestjs/core": ">6.11.0"
  checksum: 10c0/b0734b7683dbfc8a3edd22898f0ea662e3e9457742343c3f6ef978c3d59c384b447fc71cba64d4fb7a8003ea689c18473ee7bc16f7ba82cf60d057ab04739a22
  languageName: node
  linkType: hard

"node-abort-controller@npm:^3.0.1":
  version: 3.1.1
  resolution: "node-abort-controller@npm:3.1.1"
  checksum: 10c0/f7ad0e7a8e33809d4f3a0d1d65036a711c39e9d23e0319d80ebe076b9a3b4432b4d6b86a7fab65521de3f6872ffed36fc35d1327487c48eb88c517803403eda3
  languageName: node
  linkType: hard

"node-addon-api@npm:^5.0.0":
  version: 5.1.0
  resolution: "node-addon-api@npm:5.1.0"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/0eb269786124ba6fad9df8007a149e03c199b3e5a3038125dfb3e747c2d5113d406a4e33f4de1ea600aa2339be1f137d55eba1a73ee34e5fff06c52a5c296d1d
  languageName: node
  linkType: hard

"node-domexception@npm:1.0.0":
  version: 1.0.0
  resolution: "node-domexception@npm:1.0.0"
  checksum: 10c0/5e5d63cda29856402df9472335af4bb13875e1927ad3be861dc5ebde38917aecbf9ae337923777af52a48c426b70148815e890a5d72760f1b4d758cc671b1a2b
  languageName: node
  linkType: hard

"node-emoji@npm:1.11.0":
  version: 1.11.0
  resolution: "node-emoji@npm:1.11.0"
  dependencies:
    lodash: "npm:^4.17.21"
  checksum: 10c0/5dac6502dbef087092d041fcc2686d8be61168593b3a9baf964d62652f55a3a9c2277f171b81cccb851ccef33f2d070f45e633fab1fda3264f8e1ae9041c673f
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.1, node-fetch@npm:^2.6.7":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.0.0
  resolution: "node-gyp@npm:11.0.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/a3b885bbee2d271f1def32ba2e30ffcf4562a3db33af06b8b365e053153e2dd2051b9945783c3c8e852d26a0f20f65b251c7e83361623383a99635c0280ee573
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: 10c0/a6a4d8369e2f2720e9c645255ffde909c0fbd41c92ea92a5607fc17055955daac99c1ff589d421eee12a0d24e99f7bfc2aabfeb1a4c14742f6c099a51863f31a
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10c0/52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nodemailer@npm:^6.9.14":
  version: 6.10.0
  resolution: "nodemailer@npm:6.10.0"
  checksum: 10c0/39fd35d65b021b94c968eeac82a66dd843021b6ba53c659d01b1dd4cda73b6a2f96e20facfe500efa4b8d3f1cb23df10245c6c86b2bde5f806691ed17ce87826
  languageName: node
  linkType: hard

"nopt@npm:^5.0.0":
  version: 5.0.0
  resolution: "nopt@npm:5.0.0"
  dependencies:
    abbrev: "npm:1"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/fc5c4f07155cb455bf5fc3dd149fac421c1a40fd83c6bfe83aa82b52f02c17c5e88301321318adaa27611c8a6811423d51d29deaceab5fa158b585a61a551061
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.0, npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 10c0/6f9353a95288f8455cf64cbeb707b28826a7f29690244c1e4bb61ec573256e021b6ad6651b394eb1ccfd00d6ec50147253aba2c5fe58a57ceb111fad62c519ac
  languageName: node
  linkType: hard

"npmlog@npm:^5.0.1":
  version: 5.0.1
  resolution: "npmlog@npm:5.0.1"
  dependencies:
    are-we-there-yet: "npm:^2.0.0"
    console-control-strings: "npm:^1.1.0"
    gauge: "npm:^3.0.0"
    set-blocking: "npm:^2.0.0"
  checksum: 10c0/489ba519031013001135c463406f55491a17fc7da295c18a04937fe3a4d523fd65e88dd418a28b967ab743d913fdeba1e29838ce0ad8c75557057c481f7d49fa
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10c0/5fee7ff309727763689cfad844d979aedd2204a817fbaaf0e1603794a7c20db28548d7b024692f953557df6ce4a0ee4ae46cd8ebd9b36cfb300b9226b567c479
  languageName: node
  linkType: hard

"nwsapi@npm:^2.2.16":
  version: 2.2.20
  resolution: "nwsapi@npm:2.2.20"
  checksum: 10c0/07f4dafa3186aef7c007863e90acd4342a34ba9d44b22f14f644fdb311f6086887e21c2fc15efaa826c2bc39ab2bc841364a1a630e7c87e0cb723ba59d729297
  languageName: node
  linkType: hard

"object-assign@npm:^4, object-assign@npm:^4.0.1, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10c0/d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"object-is@npm:^1.1.5":
  version: 1.1.6
  resolution: "object-is@npm:1.1.6"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
  checksum: 10c0/506af444c4dce7f8e31f34fc549e2fb8152d6b9c4a30c6e62852badd7f520b579c679af433e7a072f9d78eb7808d230dc12e1cf58da9154dfbf8813099ea0fe0
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10c0/b11f7ccdbc6d406d1f186cdadb9d54738e347b2692a14439ca5ac70c225fa6db46db809711b78589866d47b25fc3e8dee0b4c722ac751e11180f9380e3d8601d
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4, object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/3b2732bd860567ea2579d1567525168de925a8d852638612846bd8082b3a1602b7b89b67b09913cbb5b9bd6e95923b2ae73580baa9d99cb4e990564e8cbf5ddc
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/cd4327e6c3369cfa805deb4cbbe919bfb7d3aeebf0bcaba291bb568ea7169f8f8cdbcabe2f00b40db0c20cd20f08e11b5f3a5a36fb7dd3fe04850c50db3bf83b
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.3":
  version: 1.0.3
  resolution: "object.groupby@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
  checksum: 10c0/60d0455c85c736fbfeda0217d1a77525956f76f7b2495edeca9e9bbf8168a45783199e77b894d30638837c654d0cc410e0e02cbfcf445bc8de71c3da1ede6a9c
  languageName: node
  linkType: hard

"object.values@npm:^1.2.0":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/3c47814fdc64842ae3d5a74bc9d06bdd8d21563c04d9939bf6716a9c00596a4ebc342552f8934013d1ec991c74e3671b26710a0c51815f0b603795605ab6b2c9
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 10c0/f649e65c197bf31505a4c0444875db0258e198292f34b884d73c2f751e91792ef96bb5cf89aa0f4fecc2e4dc662461dda606b1274b0e564f539cae5d2f5fc32f
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0, onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10c0/ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"open@npm:^10.1.0":
  version: 10.1.2
  resolution: "open@npm:10.1.2"
  dependencies:
    default-browser: "npm:^5.2.1"
    define-lazy-prop: "npm:^3.0.0"
    is-inside-container: "npm:^1.0.0"
    is-wsl: "npm:^3.1.0"
  checksum: 10c0/1bee796f06e549ce764f693272100323fbc04da8fa3c5b0402d6c2d11b3d76fa0aac0be7535e710015ff035326638e3b9a563f3b0e7ac3266473ed5663caae6d
  languageName: node
  linkType: hard

"openai@npm:^4.67.1":
  version: 4.82.0
  resolution: "openai@npm:4.82.0"
  dependencies:
    "@types/node": "npm:^18.11.18"
    "@types/node-fetch": "npm:^2.6.4"
    abort-controller: "npm:^3.0.0"
    agentkeepalive: "npm:^4.2.1"
    form-data-encoder: "npm:1.7.2"
    formdata-node: "npm:^4.3.2"
    node-fetch: "npm:^2.6.7"
  peerDependencies:
    ws: ^8.18.0
    zod: ^3.23.8
  peerDependenciesMeta:
    ws:
      optional: true
    zod:
      optional: true
  bin:
    openai: bin/cli
  checksum: 10c0/d1901673b44e7a1e8cd8e7fac0f4b3bc49f7d254ba84e3efa6fcfd23e84a1d55b0d97b1fd317c848abce7f71e2b09505d93eba5569ceb92ba92076df1bc50a38
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10c0/4afb687a059ee65b61df74dfe87d8d6815cd6883cb8b3d5883a910df72d0f5d029821f37025e4bccf4048873dbdb09acc6d303d27b8f76b1a80dd5a7d5334675
  languageName: node
  linkType: hard

"ora@npm:5.4.1, ora@npm:^5.4.1":
  version: 5.4.1
  resolution: "ora@npm:5.4.1"
  dependencies:
    bl: "npm:^4.1.0"
    chalk: "npm:^4.1.0"
    cli-cursor: "npm:^3.1.0"
    cli-spinners: "npm:^2.5.0"
    is-interactive: "npm:^1.0.0"
    is-unicode-supported: "npm:^0.1.0"
    log-symbols: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
    wcwidth: "npm:^1.0.1"
  checksum: 10c0/10ff14aace236d0e2f044193362b22edce4784add08b779eccc8f8ef97195cae1248db8ec1ec5f5ff076f91acbe573f5f42a98c19b78dba8c54eefff983cae85
  languageName: node
  linkType: hard

"os-name@npm:4.0.1":
  version: 4.0.1
  resolution: "os-name@npm:4.0.1"
  dependencies:
    macos-release: "npm:^2.5.0"
    windows-release: "npm:^4.0.0"
  checksum: 10c0/2a78bb1a25afa04ec53a972ed164948432fee93d9e039afaec3a27ffe30473ffc85afb03c0776ca3e01c8d806f99f61cb85ad3fbc060bc3e37a549c0a4867f3f
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 10c0/f438450224f8e2687605a8dd318f0db694b6293c5d835ae509a69e97c8de38b6994645337e5577f5001115470414638978cc49da1cdcc25106dad8738dc69990
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 10c0/6dfeb3455bff92ec3f16a982d4e3e65676345f6902d9f5ded1d8265a6318d0200ce461956d6d1c70053c7fe9f9fe65e552faac03f8140d37ef0fdd108e67013a
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10c0/8da01ac53efe6a627080fafc127c873da40c18d87b3f5d5492d465bb85ec7207e153948df6b9cbaeb130be70152f874229b8242ee2be84c0794082510af97f12
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2, p-limit@npm:^3.1.0":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10c0/1b476ad69ad7f6059744f343b26d51ce091508935c1dbb80c4e0a2f397ffce0ca3a1f9f5cd3c7ce19d7929a09719d5c65fe70d8ee289c3f267cd36f2881813e9
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10c0/2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10c0/c36c19907734c904b16994e6535b02c36c2224d433e01a2f1ab777237f4d86e6289fd5fd464850491e940379d4606ed850c03e0f9ab600b0ebddb511312e177f
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"pako@npm:^0.2.5":
  version: 0.2.9
  resolution: "pako@npm:0.2.9"
  checksum: 10c0/79c1806ebcf325b60ae599e4d7227c2e346d7b829dc20f5cf24cef07c934079dc3a61c5b3c8278a2f7a190c4a613e343ea11e5302dbe252efd11712df4b6b041
  languageName: node
  linkType: hard

"pako@npm:^1.0.10, pako@npm:^1.0.11, pako@npm:^1.0.6, pako@npm:~1.0.2":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 10c0/86dd99d8b34c3930345b8bbeb5e1cd8a05f608eeb40967b293f72fe469d0e9c88b783a8777e4cc7dc7c91ce54c5e93d88ff4b4f060e6ff18408fd21030d9ffbe
  languageName: node
  linkType: hard

"papaparse@npm:^5.4.0":
  version: 5.5.2
  resolution: "papaparse@npm:5.5.2"
  checksum: 10c0/83b8c0cf570395581a42331cd9231194dbba43bc8c608026739f5180827506575993dc788def039a9666bc103e2a96075de8732ea8a63e507b74c02aa757bcd5
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10c0/c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0, parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10c0/77947f2253005be7a12d858aedbafa09c9ae39eb4863adf330f7b416ca4f4a08132e453e08de2db46459256fb66afaac5ee758b44fe6541b7cdaf9d252e59585
  languageName: node
  linkType: hard

"parse5-htmlparser2-tree-adapter@npm:^6.0.0":
  version: 6.0.1
  resolution: "parse5-htmlparser2-tree-adapter@npm:6.0.1"
  dependencies:
    parse5: "npm:^6.0.1"
  checksum: 10c0/dfa5960e2aaf125707e19a4b1bc333de49232eba5a6ffffb95d313a7d6087c3b7a274b58bee8d3bd41bdf150638815d1d601a42bbf2a0345208c3c35b1279556
  languageName: node
  linkType: hard

"parse5-htmlparser2-tree-adapter@npm:^7.0.0":
  version: 7.1.0
  resolution: "parse5-htmlparser2-tree-adapter@npm:7.1.0"
  dependencies:
    domhandler: "npm:^5.0.3"
    parse5: "npm:^7.0.0"
  checksum: 10c0/e5a4e0b834c84c9e244b5749f8d007f4baaeafac7a1da2c54be3421ffd9ef8fdec4f198bf55cda22e88e6ba95e9943f6ed5aa3ae5900b39972ebf5dc8c3f4722
  languageName: node
  linkType: hard

"parse5-parser-stream@npm:^7.1.2":
  version: 7.1.2
  resolution: "parse5-parser-stream@npm:7.1.2"
  dependencies:
    parse5: "npm:^7.0.0"
  checksum: 10c0/e236c61000d38ecad369e725a48506b051cebad8abb00e6d4e8bff7aa85c183820fcb45db1559cc90955bdbbdbd665ea94c41259594e74566fff411478dc7fcb
  languageName: node
  linkType: hard

"parse5@npm:^5.1.1":
  version: 5.1.1
  resolution: "parse5@npm:5.1.1"
  checksum: 10c0/b0f87a77a7fea5f242e3d76917c983bbea47703b9371801d51536b78942db6441cbda174bf84eb30e47315ddc6f8a0b57d68e562c790154430270acd76c1fa03
  languageName: node
  linkType: hard

"parse5@npm:^6.0.1":
  version: 6.0.1
  resolution: "parse5@npm:6.0.1"
  checksum: 10c0/595821edc094ecbcfb9ddcb46a3e1fe3a718540f8320eff08b8cf6742a5114cce2d46d45f95c26191c11b184dcaf4e2960abcd9c5ed9eb9393ac9a37efcfdecb
  languageName: node
  linkType: hard

"parse5@npm:^7.0.0, parse5@npm:^7.1.2":
  version: 7.2.1
  resolution: "parse5@npm:7.2.1"
  dependencies:
    entities: "npm:^4.5.0"
  checksum: 10c0/829d37a0c709215a887e410a7118d754f8e1afd7edb529db95bc7bbf8045fb0266a7b67801331d8e8d9d073ea75793624ec27ce9ff3b96862c3b9008f4d68e80
  languageName: node
  linkType: hard

"parse5@npm:^7.2.1":
  version: 7.3.0
  resolution: "parse5@npm:7.3.0"
  dependencies:
    entities: "npm:^6.0.0"
  checksum: 10c0/7fd2e4e247e85241d6f2a464d0085eed599a26d7b0a5233790c49f53473232eb85350e8133344d9b3fd58b89339e7ad7270fe1f89d28abe50674ec97b87f80b5
  languageName: node
  linkType: hard

"parseley@npm:^0.12.0":
  version: 0.12.1
  resolution: "parseley@npm:0.12.1"
  dependencies:
    leac: "npm:^0.6.0"
    peberminta: "npm:^0.9.0"
  checksum: 10c0/df3de74172b72305b867298a71e5882c413df75d30f2bafb5fb70779dfd349c5e4db03441fbf8ca83da8e4aa72bd0ef2b5c73086c4825d27d1c649d61bc0bcc0
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10c0/90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1, path-scurry@npm:^1.6.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.7":
  version: 0.1.7
  resolution: "path-to-regexp@npm:0.1.7"
  checksum: 10c0/50a1ddb1af41a9e68bd67ca8e331a705899d16fb720a1ea3a41e310480948387daf603abb14d7b0826c58f10146d49050a1291ba6a82b78a382d1c02c0b8f905
  languageName: node
  linkType: hard

"path-to-regexp@npm:3.2.0":
  version: 3.2.0
  resolution: "path-to-regexp@npm:3.2.0"
  checksum: 10c0/2eeb1c698293acf6f89fe5af33b4c20822b3cee3e4e910c43bbee098c8dde34232fc194d5c2bc02df72affada446a181784e24f7a46932af323706be029ed1ba
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10c0/666f6973f332f27581371efaf303fd6c272cc43c2057b37aa99e3643158c7e4b2626549555d88626e99ea9e046f82f32e41bbde5f1508547e9a11b149b52387c
  languageName: node
  linkType: hard

"pdf-lib@npm:^1.17.1":
  version: 1.17.1
  resolution: "pdf-lib@npm:1.17.1"
  dependencies:
    "@pdf-lib/standard-fonts": "npm:^1.0.0"
    "@pdf-lib/upng": "npm:^1.0.1"
    pako: "npm:^1.0.11"
    tslib: "npm:^1.11.1"
  checksum: 10c0/a9489a402880dacd1389a3e14ff8f6139d58e3bc82f26b0fcbd0798b841aee6ccb7fcfab0992b574e57b40404ced0330a7170b3c6467363461a9df5d9daec489
  languageName: node
  linkType: hard

"pdfkit@npm:^0.14.0":
  version: 0.14.0
  resolution: "pdfkit@npm:0.14.0"
  dependencies:
    crypto-js: "npm:^4.2.0"
    fontkit: "npm:^1.8.1"
    linebreak: "npm:^1.0.2"
    png-js: "npm:^1.0.0"
  checksum: 10c0/461e3e718c68cde4ab49f85ac88cdc4299a8bf6ca3fef1bbc44937723828337a8134da0d8ec82c9bf62373cd5e6c4dec1dddd27be30d2970aa4d917eccfbf2d4
  languageName: node
  linkType: hard

"pdfmake@npm:^0.2.18":
  version: 0.2.18
  resolution: "pdfmake@npm:0.2.18"
  dependencies:
    "@foliojs-fork/linebreak": "npm:^1.1.2"
    "@foliojs-fork/pdfkit": "npm:^0.15.3"
    iconv-lite: "npm:^0.6.3"
    xmldoc: "npm:^1.3.0"
  checksum: 10c0/173c5e347d137af744a103c155822b52c58da44902a51576f0e6d151c3233c9a573a4b799fa282b5cffa3abf7f6304e75d18d7ed48ceb977d6223e823be35368
  languageName: node
  linkType: hard

"peberminta@npm:^0.9.0":
  version: 0.9.0
  resolution: "peberminta@npm:0.9.0"
  checksum: 10c0/59c2c39269d9f7f559cf44582f1c0503524c6a9bc3478e0309adba2b41c71ab98745a239a4e6f98f46105291256e6d8f12ae9860d9f016b1c9a6f52c0b63bfe7
  languageName: node
  linkType: hard

"pg-cloudflare@npm:^1.1.1":
  version: 1.1.1
  resolution: "pg-cloudflare@npm:1.1.1"
  checksum: 10c0/a68b957f755be6af813d68ccaf4c906a000fd2ecb362cd281220052cc9e2f6c26da3b88792742387008c30b3bf0d2fa3a0eff04aeb8af4414023c99ae78e07bd
  languageName: node
  linkType: hard

"pg-connection-string@npm:^2.7.0":
  version: 2.7.0
  resolution: "pg-connection-string@npm:2.7.0"
  checksum: 10c0/50a1496a1c858f9495d78a2c7a66d93ef3602e718aff2953bb5738f3ea616d7f727f32fc20513c9bed127650cd14c1ddc7b458396f4000e689d4b64c65c5c51e
  languageName: node
  linkType: hard

"pg-int8@npm:1.0.1":
  version: 1.0.1
  resolution: "pg-int8@npm:1.0.1"
  checksum: 10c0/be6a02d851fc2a4ae3e9de81710d861de3ba35ac927268973eb3cb618873a05b9424656df464dd43bd7dc3fc5295c3f5b3c8349494f87c7af50ec59ef14e0b98
  languageName: node
  linkType: hard

"pg-pool@npm:^3.7.0":
  version: 3.7.0
  resolution: "pg-pool@npm:3.7.0"
  peerDependencies:
    pg: ">=8.0"
  checksum: 10c0/9128673cf941f288c0cb1a74ca959a9b4f6075ef73b2cc7dece5d4db3dd7043784869e7c12bce2e69ca0df22132a419cc45c2050b4373632856fe8bae9eb94b5
  languageName: node
  linkType: hard

"pg-protocol@npm:^1.7.0":
  version: 1.7.0
  resolution: "pg-protocol@npm:1.7.0"
  checksum: 10c0/c4af854d9b843c808231c0040fed89f2b9101006157df8da2bb2f62a7dde702de748d852228dc22df41cc7ffddfb526af3bcb34b278b581e9f76a060789186c1
  languageName: node
  linkType: hard

"pg-types@npm:^2.1.0":
  version: 2.2.0
  resolution: "pg-types@npm:2.2.0"
  dependencies:
    pg-int8: "npm:1.0.1"
    postgres-array: "npm:~2.0.0"
    postgres-bytea: "npm:~1.0.0"
    postgres-date: "npm:~1.0.4"
    postgres-interval: "npm:^1.1.0"
  checksum: 10c0/ab3f8069a323f601cd2d2279ca8c425447dab3f9b61d933b0601d7ffc00d6200df25e26a4290b2b0783b59278198f7dd2ed03e94c4875797919605116a577c65
  languageName: node
  linkType: hard

"pg@npm:^8.10.0":
  version: 8.13.1
  resolution: "pg@npm:8.13.1"
  dependencies:
    pg-cloudflare: "npm:^1.1.1"
    pg-connection-string: "npm:^2.7.0"
    pg-pool: "npm:^3.7.0"
    pg-protocol: "npm:^1.7.0"
    pg-types: "npm:^2.1.0"
    pgpass: "npm:1.x"
  peerDependencies:
    pg-native: ">=3.0.1"
  dependenciesMeta:
    pg-cloudflare:
      optional: true
  peerDependenciesMeta:
    pg-native:
      optional: true
  checksum: 10c0/c13bc661cbdb115337bc8519254836faf4bd79106dfd7ed588c8ece8c8b2dd3b7376bfec9a9a2f7646fa095b0b310cec77a83c3ba2ea4872331446eb93fd9055
  languageName: node
  linkType: hard

"pgpass@npm:1.x":
  version: 1.0.5
  resolution: "pgpass@npm:1.0.5"
  dependencies:
    split2: "npm:^4.1.0"
  checksum: 10c0/5ea6c9b2de04c33abb08d33a2dded303c4a3c7162a9264519cbe85c0a9857d712463140ba42fad0c7cd4b21f644dd870b45bb2e02fcbe505b4de0744fd802c1d
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.2.3, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"pirates@npm:^4.0.4":
  version: 4.0.6
  resolution: "pirates@npm:4.0.6"
  checksum: 10c0/00d5fa51f8dded94d7429700fb91a0c1ead00ae2c7fd27089f0c5b63e6eca36197fe46384631872690a66f390c5e27198e99006ab77ae472692ab9c2ca903f36
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: "npm:^4.0.0"
  checksum: 10c0/c56bda7769e04907a88423feb320babaed0711af8c436ce3e56763ab1021ba107c7b0cafb11cde7529f669cfc22bffcaebffb573645cbd63842ea9fb17cd7728
  languageName: node
  linkType: hard

"pluralize@npm:8.0.0":
  version: 8.0.0
  resolution: "pluralize@npm:8.0.0"
  checksum: 10c0/2044cfc34b2e8c88b73379ea4a36fc577db04f651c2909041b054c981cd863dd5373ebd030123ab058d194ae615d3a97cfdac653991e499d10caf592e8b3dc33
  languageName: node
  linkType: hard

"png-js@npm:^1.0.0":
  version: 1.0.0
  resolution: "png-js@npm:1.0.0"
  checksum: 10c0/b591107bb888c66cb1799327774f73f2865a366b5992ed7335c81610eab0e9cb4f13fb396e3c00e28781ada6ccde170b566209e9df833a2abe71d11da55aea85
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.0.0
  resolution: "possible-typed-array-names@npm:1.0.0"
  checksum: 10c0/d9aa22d31f4f7680e20269db76791b41c3a32c01a373e25f8a4813b4d45f7456bfc2b6d68f752dc4aab0e0bb0721cb3d76fb678c9101cb7a16316664bc2c73fd
  languageName: node
  linkType: hard

"postgres-array@npm:~2.0.0":
  version: 2.0.0
  resolution: "postgres-array@npm:2.0.0"
  checksum: 10c0/cbd56207e4141d7fbf08c86f2aebf21fa7064943d3f808ec85f442ff94b48d891e7a144cc02665fb2de5dbcb9b8e3183a2ac749959e794b4a4cfd379d7a21d08
  languageName: node
  linkType: hard

"postgres-bytea@npm:~1.0.0":
  version: 1.0.0
  resolution: "postgres-bytea@npm:1.0.0"
  checksum: 10c0/febf2364b8a8953695cac159eeb94542ead5886792a9627b97e33f6b5bb6e263bc0706ab47ec221516e79fbd6b2452d668841830fb3b49ec6c0fc29be61892ce
  languageName: node
  linkType: hard

"postgres-date@npm:~1.0.4":
  version: 1.0.7
  resolution: "postgres-date@npm:1.0.7"
  checksum: 10c0/0ff91fccc64003e10b767fcfeefb5eaffbc522c93aa65d5051c49b3c4ce6cb93ab091a7d22877a90ad60b8874202c6f1d0f935f38a7235ed3b258efd54b97ca9
  languageName: node
  linkType: hard

"postgres-interval@npm:^1.1.0":
  version: 1.2.0
  resolution: "postgres-interval@npm:1.2.0"
  dependencies:
    xtend: "npm:^4.0.0"
  checksum: 10c0/c1734c3cb79e7f22579af0b268a463b1fa1d084e742a02a7a290c4f041e349456f3bee3b4ee0bb3f226828597f7b76deb615c1b857db9a742c45520100456272
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10c0/b00d617431e7886c520a6f498a2e14c75ec58f6d93ba48c3b639cf241b54232d90daa05d83a9e9b9fef6baa63cb7e1e4602c2372fea5bc169668401eb127d0cd
  languageName: node
  linkType: hard

"prepin@npm:^1.0.3":
  version: 1.0.3
  resolution: "prepin@npm:1.0.3"
  bin:
    prepin: ./bin/prepin.js
  checksum: 10c0/fecef4ee90499f26b7db532146f29612f47383b32965b5bab86b5dfde208cd6a1926edc7c6c1113b062f7c26e5d39cd6bd414721663b3d57aabe175cd58d14a5
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: "npm:^1.1.2"
  checksum: 10c0/81e0027d731b7b3697ccd2129470ed9913ecb111e4ec175a12f0fcfab0096516373bf0af2fef132af50cafb0a905b74ff57996d615f59512bb9ac7378fcc64ab
  languageName: node
  linkType: hard

"prettier@npm:^2.8.4":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: 10c0/463ea8f9a0946cd5b828d8cf27bd8b567345cf02f56562d5ecde198b91f47a76b7ac9eae0facd247ace70e927143af6135e8cf411986b8cb8478784a4d6d724a
  languageName: node
  linkType: hard

"pretty-format@npm:^29.0.0, pretty-format@npm:^29.7.0":
  version: 29.7.0
  resolution: "pretty-format@npm:29.7.0"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    ansi-styles: "npm:^5.0.0"
    react-is: "npm:^18.0.0"
  checksum: 10c0/edc5ff89f51916f036c62ed433506b55446ff739358de77207e63e88a28ca2894caac6e73dcb68166a606e51c8087d32d400473e6a9fdd2dbe743f46c9c0276f
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"process-nextick-args@npm:~1.0.6":
  version: 1.0.7
  resolution: "process-nextick-args@npm:1.0.7"
  checksum: 10c0/941bb79700261e44c535e234f751a924df564d4d8ff250dd06c3e213f639060c190364879722c096e777cae32116c6a88d97bee50d0b5704ab2899813818f4c8
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 10c0/bec089239487833d46b59d80327a1605e1c5287eaad770a291add7f45fda1bb5e28b38e0e061add0a1d0ee0984788ce74fa394d345eed1c420cacf392c554367
  languageName: node
  linkType: hard

"prom-client@npm:^15.1.0":
  version: 15.1.3
  resolution: "prom-client@npm:15.1.3"
  dependencies:
    "@opentelemetry/api": "npm:^1.4.0"
    tdigest: "npm:^0.1.1"
  checksum: 10c0/816525572e5799a2d1d45af78512fb47d073c842dc899c446e94d17cfc343d04282a1627c488c7ca1bcd47f766446d3e49365ab7249f6d9c22c7664a5bce7021
  languageName: node
  linkType: hard

"promise-coalesce@npm:^1.1.2":
  version: 1.1.2
  resolution: "promise-coalesce@npm:1.1.2"
  checksum: 10c0/9bcdc954a645b30ccb094ae76e36212af2ee7ecc6b9fc67634d7d4f490d082c1efc39590737d957a32f6df93e7c5f51ca232549095e9e2d7291c0482d1bb1557
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"prompts@npm:^2.0.1":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: "npm:^3.0.3"
    sisteransi: "npm:^1.0.5"
  checksum: 10c0/16f1ac2977b19fe2cf53f8411cc98db7a3c8b115c479b2ca5c82b5527cd937aa405fa04f9a5960abeb9daef53191b53b4d13e35c1f5d50e8718c76917c5f1ea4
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: 10c0/c3eed999781a35f7fd935f398b6d8920b6fb00bbc14287bc6de78128ccc1a02c89b95b56742bf7cf0362cc333c61d138532049c7dedc7a328ef13343eff81210
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10c0/fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.2
  resolution: "pump@npm:3.0.2"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 10c0/5ad655cb2a7738b4bcf6406b24ad0970d680649d996b55ad20d1be8e0c02394034e4c45ff7cd105d87f1e9b96a0e3d06fd28e11fae8875da26e7f7a8e2c9726f
  languageName: node
  linkType: hard

"punycode@npm:1.3.2":
  version: 1.3.2
  resolution: "punycode@npm:1.3.2"
  checksum: 10c0/281fd20eaf4704f79d80cb0dc65065bf6452ee67989b3e8941aed6360a5a9a8a01d3e2ed71d0bde3cd74fb5a5dd9db4160bed5a8c20bed4b6764c24ce4c7d2d2
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"pure-rand@npm:^6.0.0":
  version: 6.1.0
  resolution: "pure-rand@npm:6.1.0"
  checksum: 10c0/1abe217897bf74dcb3a0c9aba3555fe975023147b48db540aa2faf507aee91c03bf54f6aef0eb2bf59cc259a16d06b28eca37f0dc426d94f4692aeff02fb0e65
  languageName: node
  linkType: hard

"qs@npm:6.11.0":
  version: 6.11.0
  resolution: "qs@npm:6.11.0"
  dependencies:
    side-channel: "npm:^1.0.4"
  checksum: 10c0/4e4875e4d7c7c31c233d07a448e7e4650f456178b9dd3766b7cfa13158fdb24ecb8c4f059fa91e820dc6ab9f2d243721d071c9c0378892dcdad86e9e9a27c68f
  languageName: node
  linkType: hard

"qs@npm:^6.11.0, qs@npm:^6.11.1":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: "npm:^1.1.0"
  checksum: 10c0/8ea5d91bf34f440598ee389d4a7d95820e3b837d3fd9f433871f7924801becaa0cd3b3b4628d49a7784d06a8aea9bc4554d2b6d8d584e2d221dc06238a42909c
  languageName: node
  linkType: hard

"query-string@npm:<8.x":
  version: 7.1.3
  resolution: "query-string@npm:7.1.3"
  dependencies:
    decode-uri-component: "npm:^0.2.2"
    filter-obj: "npm:^1.1.0"
    split-on-first: "npm:^1.0.0"
    strict-uri-encode: "npm:^2.0.0"
  checksum: 10c0/a896c08e9e0d4f8ffd89a572d11f668c8d0f7df9c27c6f49b92ab31366d3ba0e9c331b9a620ee747893436cd1f2f821a6327e2bc9776bde2402ac6c270b801b2
  languageName: node
  linkType: hard

"querystring@npm:0.2.0":
  version: 0.2.0
  resolution: "querystring@npm:0.2.0"
  checksum: 10c0/2036c9424beaacd3978bac9e4ba514331cc73163bea7bf3ad7e2c7355e55501938ec195312c607753f9c6e70b1bf9dfcda38db6241bd299c034e27ac639d64ed
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"queue@npm:6.0.2, queue@npm:^6.0.2":
  version: 6.0.2
  resolution: "queue@npm:6.0.2"
  dependencies:
    inherits: "npm:~2.0.3"
  checksum: 10c0/cf987476cc72e7d3aaabe23ccefaab1cd757a2b5e0c8d80b67c9575a6b5e1198807ffd4f0948a3f118b149d1111d810ee773473530b77a5c606673cac2c9c996
  languageName: node
  linkType: hard

"ramda@npm:^0.28.0":
  version: 0.28.0
  resolution: "ramda@npm:0.28.0"
  checksum: 10c0/0f9dc0cc3b0432ff047f1e2a5e58860c531a84574674c0f52fef535efc6e1e07fa3851102fff3da7dd551a592c743f6f6fa521379a6aa5fe50266f8af8f0b570
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: "npm:^5.1.0"
  checksum: 10c0/50395efda7a8c94f5dffab564f9ff89736064d32addf0cc7e8bf5e4166f09f8ded7a0849ca6c2d2a59478f7d90f78f20d8048bca3cdf8be09d8e8a10790388f3
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10c0/96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"raw-body@npm:2.5.1":
  version: 2.5.1
  resolution: "raw-body@npm:2.5.1"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10c0/5dad5a3a64a023b894ad7ab4e5c7c1ce34d3497fc7138d02f8c88a3781e68d8a55aa7d4fd3a458616fa8647cc228be314a1c03fb430a07521de78b32c4dd09d2
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10c0/b201c4b66049369a60e766318caff5cb3cc5a900efd89bdac431463822d976ad0670912c931fdbdcf5543207daf6f6833bca57aa116e1661d2ea91e12ca692c4
  languageName: node
  linkType: hard

"raw-body@npm:^3.0.0":
  version: 3.0.0
  resolution: "raw-body@npm:3.0.0"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.6.3"
    unpipe: "npm:1.0.0"
  checksum: 10c0/f8daf4b724064a4811d118745a781ca0fb4676298b8adadfd6591155549cfea0a067523cf7dd3baeb1265fecc9ce5dfb2fc788c12c66b85202a336593ece0f87
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: 10c0/f2f1e60010c683479e74c63f96b09fb41603527cd131a9959e2aee1e5a8b0caf270b365e5ca77d4a6b18aae659b60a86150bb3979073528877029b35aecd2072
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.5, readable-stream@npm:^2.2.2, readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 10c0/7efdb01f3853bc35ac62ea25493567bf588773213f5f4a79f9c365e1ad13bab845ac0dae7bc946270dc40c3929483228415e92a3fc600cc7e4548992f41ee3fa
  languageName: node
  linkType: hard

"readable-stream@npm:^3.4.0, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readable-stream@npm:~2.1.0":
  version: 2.1.5
  resolution: "readable-stream@npm:2.1.5"
  dependencies:
    buffer-shims: "npm:^1.0.0"
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.1"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~1.0.6"
    string_decoder: "npm:~0.10.x"
    util-deprecate: "npm:~1.0.1"
  checksum: 10c0/a7e5dd8211872bd31392e20a61e431be7c64aa975991f240037efc3e0d28456e45c627b2c81576f78789e60980df1310ba949278bac7d6cfa19892d051ae5e5f
  languageName: node
  linkType: hard

"readdir-glob@npm:^1.1.2":
  version: 1.1.3
  resolution: "readdir-glob@npm:1.1.3"
  dependencies:
    minimatch: "npm:^5.1.0"
  checksum: 10c0/a37e0716726650845d761f1041387acd93aa91b28dd5381950733f994b6c349ddc1e21e266ec7cc1f9b92e205a7a972232f9b89d5424d07361c2c3753d5dbace
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10c0/6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"rechoir@npm:^0.6.2":
  version: 0.6.2
  resolution: "rechoir@npm:0.6.2"
  dependencies:
    resolve: "npm:^1.1.6"
  checksum: 10c0/22c4bb32f4934a9468468b608417194f7e3ceba9a508512125b16082c64f161915a28467562368eeb15dc16058eb5b7c13a20b9eb29ff9927d1ebb3b5aa83e84
  languageName: node
  linkType: hard

"reflect-metadata@npm:^0.2.1":
  version: 0.2.2
  resolution: "reflect-metadata@npm:0.2.2"
  checksum: 10c0/1cd93a15ea291e420204955544637c264c216e7aac527470e393d54b4bb075f10a17e60d8168ec96600c7e0b9fcc0cb0bb6e91c3fbf5b0d8c9056f04e6ac1ec2
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 10c0/7facec28c8008876f8ab98e80b7b9cb4b1e9224353fd4756dda5f2a4ab0d30fa0a5074777c6df24e1e0af463a2697513b0a11e548d99cf52f21f7bc6ba48d3ac
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 10c0/1b16eb2c4bceb1665c89de70dcb64126a22bc8eb958feef3cd68fe11ac6d2a4899b5cd1b80b0774c7c03591dc57d16631a7f69d2daa2ec98100e2f29f7ec4cc4
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.1, regexp.prototype.flags@npm:^1.5.3":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10c0/83b88e6115b4af1c537f8dabf5c3744032cb875d63bc05c288b1b8c0ef37cbe55353f95d8ca817e8843806e3e150b118bc624e4279b24b4776b4198232735a77
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10c0/83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 10c0/aaa267e0c5b022fc5fd4eef49d8285086b15f2a1c54b28240fdf03599cbd9c26049fee3eab894f2e1f6ca65e513b030a7c264201e3f005601e80c49fb2937ce2
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: "npm:^5.0.0"
  checksum: 10c0/e608a3ebd15356264653c32d7ecbc8fd702f94c6703ea4ac2fb81d9c359180cba0ae2e6b71faa446631ed6145454d5a56b227efc33a2d40638ac13f8beb20ee4
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10c0/8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 10c0/b21cb7f1fb746de8107b9febab60095187781137fd803e6a59a76d421444b1531b641bba5857f5dc011974d8a5c635d61cec49e6bd3b7fc20e01f0fafc4efbf2
  languageName: node
  linkType: hard

"resolve.exports@npm:^2.0.0":
  version: 2.0.3
  resolution: "resolve.exports@npm:2.0.3"
  checksum: 10c0/1ade1493f4642a6267d0a5e68faeac20b3d220f18c28b140343feb83694d8fed7a286852aef43689d16042c61e2ddb270be6578ad4a13990769e12065191200d
  languageName: node
  linkType: hard

"resolve@npm:^1.1.6, resolve@npm:^1.20.0, resolve@npm:^1.22.4":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/8967e1f4e2cc40f79b7e080b4582b9a8c5ee36ffb46041dccb20e6461161adf69f843b43067b4a375de926a2cd669157e29a29578191def399dd5ef89a1b5203
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.1.6#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.20.0#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.4#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/52a4e505bbfc7925ac8f4cd91fd8c4e096b6a89728b9f46861d3b405ac9a1ccf4dcbf8befb4e89a2e11370dacd0160918163885cbc669369590f2f31f4c58939
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: "npm:^5.1.0"
    signal-exit: "npm:^3.0.2"
  checksum: 10c0/8051a371d6aa67ff21625fa94e2357bd81ffdc96267f3fb0fc4aaf4534028343836548ef34c240ffa8c25b280ca35eb36be00b3cb2133fa4f51896d7e73c6b4f
  languageName: node
  linkType: hard

"restructure@npm:^2.0.1":
  version: 2.0.1
  resolution: "restructure@npm:2.0.1"
  checksum: 10c0/b7f5beccac2286de38c387f470ddd836a331ce5062b9414b1007f7f21f8ac0ac64452a91b237b4f5c70dc519bcd376c9e6cc762e0734b25b99ec71318ba33a40
  languageName: node
  linkType: hard

"retry-axios@npm:<3.x":
  version: 2.6.0
  resolution: "retry-axios@npm:2.6.0"
  peerDependencies:
    axios: "*"
  checksum: 10c0/da951029dacaf9eba56b2714dd97941381e15d670d22aaa39effc13e0793b392da6b0e7b4ba36539c957d40b859bc6c46d64f85097ba625da196218074aeacf7
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: 10c0/c19ef26e4e188f408922c46f7ff480d38e8dfc55d448310dfb518736b23ed2c4f547fb64a6ed5bdba92cd7e7ddc889d36ff78f794816d5e71498d645ef476107
  languageName: node
  linkType: hard

"rimraf@npm:4.4.1":
  version: 4.4.1
  resolution: "rimraf@npm:4.4.1"
  dependencies:
    glob: "npm:^9.2.0"
  bin:
    rimraf: dist/cjs/src/bin.js
  checksum: 10c0/8c5e142d26d8b222be9dc9a1a41ba48e95d8f374e813e66a8533e87c6180174fcb3f573b9b592eca12740ebf8b78526d136acd971d4a790763d6f2232c34fa24
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10c0/9cb7757acb489bd83757ba1a274ab545eafd75598a9d817e0c3f8b164238dd90eba50d6b848bd4dcc5f3040912e882dc7ba71653e35af660d77b25c381d402e8
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: "npm:^10.3.7"
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 10c0/7da4fd0e15118ee05b918359462cfa1e7fe4b1228c7765195a45b55576e8c15b95db513b8466ec89129666f4af45ad978a3057a02139afba1a63512a2d9644cc
  languageName: node
  linkType: hard

"rrweb-cssom@npm:^0.8.0":
  version: 0.8.0
  resolution: "rrweb-cssom@npm:0.8.0"
  checksum: 10c0/56f2bfd56733adb92c0b56e274c43f864b8dd48784d6fe946ef5ff8d438234015e59ad837fc2ad54714b6421384141c1add4eb569e72054e350d1f8a50b8ac7b
  languageName: node
  linkType: hard

"run-applescript@npm:^7.0.0":
  version: 7.0.0
  resolution: "run-applescript@npm:7.0.0"
  checksum: 10c0/bd821bbf154b8e6c8ecffeaf0c33cebbb78eb2987476c3f6b420d67ab4c5301faa905dec99ded76ebb3a7042b4e440189ae6d85bbbd3fc6e8d493347ecda8bfe
  languageName: node
  linkType: hard

"run-async@npm:^2.4.0":
  version: 2.4.1
  resolution: "run-async@npm:2.4.1"
  checksum: 10c0/35a68c8f1d9664f6c7c2e153877ca1d6e4f886e5ca067c25cdd895a6891ff3a1466ee07c63d6a9be306e9619ff7d509494e6d9c129516a36b9fd82263d579ee1
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"rxjs@npm:7.8.1, rxjs@npm:^7.5.5, rxjs@npm:^7.8.0":
  version: 7.8.1
  resolution: "rxjs@npm:7.8.1"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: 10c0/3c49c1ecd66170b175c9cacf5cef67f8914dcbc7cd0162855538d365c83fea631167cacb644b3ce533b2ea0e9a4d0b12175186985f89d75abe73dbd8f7f06f68
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/43c86ffdddc461fb17ff8a17c5324f392f4868f3c7dd2c6a5d9f5971713bc5fd755667212c80eab9567595f9a7509cc2f83e590ddaebd1bd19b780f9c79f9a8d
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10c0/780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/831f1c9aae7436429e7862c7e46f847dfe490afac20d0ee61bae06108dbf5c745a0de3568ada30ccdd3eeb0864ca8331b2eef703abd69bfea0745b21fd320750
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: 10c0/f2c25281bbe5d39cddbbce7f86fca5ea9b3ce3354ea6cd7c81c31b006a5a9fff4286acc5450a3b9122c56c33eba69c56b9131ad751457b2b4a585825e6a10665
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sax@npm:1.2.1":
  version: 1.2.1
  resolution: "sax@npm:1.2.1"
  checksum: 10c0/1ae269cfde0b3774b4c92eb744452b6740bde5c5744fe5cadef6f496e42d9b632f483fb6aff9a23c0749c94c6951b06b0c5a90a5e99c879d3401cfd5ba61dc02
  languageName: node
  linkType: hard

"sax@npm:>=0.6.0, sax@npm:^1.2.4":
  version: 1.4.1
  resolution: "sax@npm:1.4.1"
  checksum: 10c0/6bf86318a254c5d898ede6bd3ded15daf68ae08a5495a2739564eb265cd13bcc64a07ab466fb204f67ce472bb534eb8612dac587435515169593f4fffa11de7c
  languageName: node
  linkType: hard

"saxes@npm:^6.0.0":
  version: 6.0.0
  resolution: "saxes@npm:6.0.0"
  dependencies:
    xmlchars: "npm:^2.2.0"
  checksum: 10c0/3847b839f060ef3476eb8623d099aa502ad658f5c40fd60c105ebce86d244389b0d76fcae30f4d0c728d7705ceb2f7e9b34bb54717b6a7dbedaf5dad2d9a4b74
  languageName: node
  linkType: hard

"schema-utils@npm:^3.1.1, schema-utils@npm:^3.1.2":
  version: 3.3.0
  resolution: "schema-utils@npm:3.3.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.8"
    ajv: "npm:^6.12.5"
    ajv-keywords: "npm:^3.5.2"
  checksum: 10c0/fafdbde91ad8aa1316bc543d4b61e65ea86970aebbfb750bfb6d8a6c287a23e415e0e926c2498696b242f63af1aab8e585252637fabe811fd37b604351da6500
  languageName: node
  linkType: hard

"schema-utils@npm:^4.3.0":
  version: 4.3.0
  resolution: "schema-utils@npm:4.3.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.9"
    ajv: "npm:^8.9.0"
    ajv-formats: "npm:^2.1.1"
    ajv-keywords: "npm:^5.1.0"
  checksum: 10c0/c23f0fa73ef71a01d4a2bb7af4c91e0d356ec640e071aa2d06ea5e67f042962bb7ac7c29a60a295bb0125878801bc3209197a2b8a833dd25bd38e37c3ed21427
  languageName: node
  linkType: hard

"selderee@npm:^0.11.0":
  version: 0.11.0
  resolution: "selderee@npm:0.11.0"
  dependencies:
    parseley: "npm:^0.12.0"
  checksum: 10c0/c2ad8313a0dbf3c0b74752a8d03cfbc0931ae77a36679cdb64733eb732c1762f95a5174249bf7e8b8103874cb0e013a030f9c8b72f5d41e62f1d847d4a845d39
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.3.4, semver@npm:^7.3.5, semver@npm:^7.3.7, semver@npm:^7.3.8, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.3":
  version: 7.7.1
  resolution: "semver@npm:7.7.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/fd603a6fb9c399c6054015433051bdbe7b99a940a8fb44b85c2b524c4004b023d7928d47cb22154f8d054ea7ee8597f586605e05b52047f048278e4ac56ae958
  languageName: node
  linkType: hard

"send@npm:0.18.0":
  version: 0.18.0
  resolution: "send@npm:0.18.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/0eb134d6a51fc13bbcb976a1f4214ea1e33f242fae046efc311e80aff66c7a43603e26a79d9d06670283a13000e51be6e0a2cb80ff0942eaf9f1cd30b7ae736a
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.2":
  version: 6.0.2
  resolution: "serialize-javascript@npm:6.0.2"
  dependencies:
    randombytes: "npm:^2.1.0"
  checksum: 10c0/2dd09ef4b65a1289ba24a788b1423a035581bef60817bea1f01eda8e3bda623f86357665fe7ac1b50f6d4f583f97db9615b3f07b2a2e8cbcb75033965f771dd2
  languageName: node
  linkType: hard

"serve-static@npm:1.15.0":
  version: 1.15.0
  resolution: "serve-static@npm:1.15.0"
  dependencies:
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.18.0"
  checksum: 10c0/fa9f0e21a540a28f301258dfe1e57bb4f81cd460d28f0e973860477dd4acef946a1f41748b5bd41c73b621bea2029569c935faa38578fd34cd42a9b4947088ba
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 10c0/9f8c1b2d800800d0b589de1477c753492de5c1548d4ade52f57f1d1f5e04af5481554d75ce5e5c43d4004b80a3eb714398d6907027dc0534177b7539119f4454
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/82850e62f412a258b71e123d4ed3873fa9377c216809551192bb6769329340176f109c2eeae8c22a8d386c76739855f78e8716515c818bcaef384b51110f0f3c
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/fce59f90696c450a8523e754abb305e2b8c73586452619c2bad5f7bf38c7b6b4651895c9db895679c5bef9554339cf3ef1c329b66ece3eda7255785fbe299316
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/ca5c3ccbba479d07c30460e367e66337cec825560b11e8ba9c5ebe13a2a0d6021ae34eddf94ff3dfe17a3104dc1f191519cb6c48378b503e5c3f36393938776a
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 10c0/5bae81bfdbfbd0ce992893286d49c9693c82b1bcc00dcaaf3a09c8f428fdeacf4190c013598b81875dfac2b08a572422db7df779a99332d0fce186d15a3e4d49
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10c0/68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.11":
  version: 2.4.11
  resolution: "sha.js@npm:2.4.11"
  dependencies:
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  bin:
    sha.js: ./bin.js
  checksum: 10c0/b7a371bca8821c9cc98a0aeff67444a03d48d745cb103f17228b96793f455f0eb0a691941b89ea1e60f6359207e36081d9be193252b0f128e0daf9cfea2815a5
  languageName: node
  linkType: hard

"sharp@npm:^0.33.5":
  version: 0.33.5
  resolution: "sharp@npm:0.33.5"
  dependencies:
    "@img/sharp-darwin-arm64": "npm:0.33.5"
    "@img/sharp-darwin-x64": "npm:0.33.5"
    "@img/sharp-libvips-darwin-arm64": "npm:1.0.4"
    "@img/sharp-libvips-darwin-x64": "npm:1.0.4"
    "@img/sharp-libvips-linux-arm": "npm:1.0.5"
    "@img/sharp-libvips-linux-arm64": "npm:1.0.4"
    "@img/sharp-libvips-linux-s390x": "npm:1.0.4"
    "@img/sharp-libvips-linux-x64": "npm:1.0.4"
    "@img/sharp-libvips-linuxmusl-arm64": "npm:1.0.4"
    "@img/sharp-libvips-linuxmusl-x64": "npm:1.0.4"
    "@img/sharp-linux-arm": "npm:0.33.5"
    "@img/sharp-linux-arm64": "npm:0.33.5"
    "@img/sharp-linux-s390x": "npm:0.33.5"
    "@img/sharp-linux-x64": "npm:0.33.5"
    "@img/sharp-linuxmusl-arm64": "npm:0.33.5"
    "@img/sharp-linuxmusl-x64": "npm:0.33.5"
    "@img/sharp-wasm32": "npm:0.33.5"
    "@img/sharp-win32-ia32": "npm:0.33.5"
    "@img/sharp-win32-x64": "npm:0.33.5"
    color: "npm:^4.2.3"
    detect-libc: "npm:^2.0.3"
    semver: "npm:^7.6.3"
  dependenciesMeta:
    "@img/sharp-darwin-arm64":
      optional: true
    "@img/sharp-darwin-x64":
      optional: true
    "@img/sharp-libvips-darwin-arm64":
      optional: true
    "@img/sharp-libvips-darwin-x64":
      optional: true
    "@img/sharp-libvips-linux-arm":
      optional: true
    "@img/sharp-libvips-linux-arm64":
      optional: true
    "@img/sharp-libvips-linux-s390x":
      optional: true
    "@img/sharp-libvips-linux-x64":
      optional: true
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
    "@img/sharp-linux-arm":
      optional: true
    "@img/sharp-linux-arm64":
      optional: true
    "@img/sharp-linux-s390x":
      optional: true
    "@img/sharp-linux-x64":
      optional: true
    "@img/sharp-linuxmusl-arm64":
      optional: true
    "@img/sharp-linuxmusl-x64":
      optional: true
    "@img/sharp-wasm32":
      optional: true
    "@img/sharp-win32-ia32":
      optional: true
    "@img/sharp-win32-x64":
      optional: true
  checksum: 10c0/6b81421ddfe6ee524d8d77e325c5e147fef22884e1c7b1656dfd89a88d7025894115da02d5f984261bf2e6daa16f98cadd1721c4ba408b4212b1d2a60f233484
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"shelljs@npm:0.8.5":
  version: 0.8.5
  resolution: "shelljs@npm:0.8.5"
  dependencies:
    glob: "npm:^7.0.0"
    interpret: "npm:^1.0.0"
    rechoir: "npm:^0.6.2"
  bin:
    shjs: bin/shjs
  checksum: 10c0/feb25289a12e4bcd04c40ddfab51aff98a3729f5c2602d5b1a1b95f6819ec7804ac8147ebd8d9a85dfab69d501bcf92d7acef03247320f51c1552cec8d8e2382
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10c0/71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4, side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10c0/cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.0, signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: "npm:^0.3.1"
  checksum: 10c0/df5e4662a8c750bdba69af4e8263c5d96fe4cd0f9fe4bdfa3cbdeb45d2e869dff640beaaeb1ef0e99db4d8d2ec92f85508c269f50c972174851bc1ae5bd64308
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: 10c0/230ac975cca485b7f6fe2b96a711aa62a6a26ead3e6fb8ba17c5a00d61b8bed0d7adc21f5626b70d7c33c62ff4e63933017a6462942c719d1980bb0b1207ad46
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10c0/e18488c6a42bdfd4ac5be85b2ced3ccd0224773baae6ad42cfbb9ec74fc07f9fa8396bd35ee638084ead7a2a0818eb5e7151111544d4731ce843019dab4be47b
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/d54a52bf9325165770b674a67241143a3d8b4e4c8884560c4e0e078aace2a728dffc7f70150660f51b85797c4e1a3b82f9b7aa25e0a0ceae1a243365da5c51a7
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.13":
  version: 0.5.13
  resolution: "source-map-support@npm:0.5.13"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/137539f8c453fa0f496ea42049ab5da4569f96781f6ac8e5bfda26937be9494f4e8891f523c5f98f0e85f71b35d74127a00c46f83f6a4f54672b58d53202565e
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.21, source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map@npm:0.7.4, source-map@npm:^0.7.4":
  version: 0.7.4
  resolution: "source-map@npm:0.7.4"
  checksum: 10c0/dc0cf3768fe23c345ea8760487f8c97ef6fca8a73c83cd7c9bf2fde8bc2c34adb9c0824d6feb14bc4f9e37fb522e18af621543f1289038a66ac7586da29aa7dc
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"split-on-first@npm:^1.0.0":
  version: 1.1.0
  resolution: "split-on-first@npm:1.1.0"
  checksum: 10c0/56df8344f5a5de8521898a5c090023df1d8b8c75be6228f56c52491e0fc1617a5236f2ac3a066adb67a73231eac216ccea7b5b4a2423a543c277cb2f48d24c29
  languageName: node
  linkType: hard

"split2@npm:^4.1.0":
  version: 4.2.0
  resolution: "split2@npm:4.2.0"
  checksum: 10c0/b292beb8ce9215f8c642bb68be6249c5a4c7f332fc8ecadae7be5cbdf1ea95addc95f0459ef2e7ad9d45fd1064698a097e4eb211c83e772b49bc0ee423e91534
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10c0/ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"stack-utils@npm:^2.0.3":
  version: 2.0.6
  resolution: "stack-utils@npm:2.0.6"
  dependencies:
    escape-string-regexp: "npm:^2.0.0"
  checksum: 10c0/651c9f87667e077584bbe848acaecc6049bc71979f1e9a46c7b920cad4431c388df0f51b8ad7cfd6eed3db97a2878d0fc8b3122979439ea8bac29c61c95eec8a
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10c0/34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.0.0":
  version: 1.1.0
  resolution: "stop-iteration-iterator@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    internal-slot: "npm:^1.1.0"
  checksum: 10c0/de4e45706bb4c0354a4b1122a2b8cc45a639e86206807ce0baf390ee9218d3ef181923fa4d2b67443367c491aa255c5fbaa64bb74648e3c5b48299928af86c09
  languageName: node
  linkType: hard

"stream-array@npm:^1.1.2":
  version: 1.1.2
  resolution: "stream-array@npm:1.1.2"
  dependencies:
    readable-stream: "npm:~2.1.0"
  checksum: 10c0/9f65ad9e71021a1b2a5c1eebe4e1081a9b1ea951d291444832d1aa298df91e49828f7b6201e6737b1ffacf477c7b208d4e89ea4f0af47299482db4ed97f2e75b
  languageName: node
  linkType: hard

"stream-chain@npm:^2.2.5":
  version: 2.2.5
  resolution: "stream-chain@npm:2.2.5"
  checksum: 10c0/c512f50190d7c92d688fa64e7af540c51b661f9c2b775fc72bca38ea9bca515c64c22c2197b1be463741daacbaaa2dde8a8ea24ebda46f08391224f15249121a
  languageName: node
  linkType: hard

"stream-json@npm:^1.8.0":
  version: 1.9.1
  resolution: "stream-json@npm:1.9.1"
  dependencies:
    stream-chain: "npm:^2.2.5"
  checksum: 10c0/0521e5cb3fb6b0e2561d715975e891bd81fa77d0239c8d0b1756846392bc3c7cdd7f1ddb0fe7ed77e6fdef58daab9e665d3b39f7d677bd0859e65a2bff59b92c
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 10c0/fbd9aecc2621364384d157f7e59426f4bfd385e8b424b5aaa79c83a6f5a1c8fd2e4e3289e95de1eb3511cb96bb333d6281a9919fafce760e4edb35b2cd2facab
  languageName: node
  linkType: hard

"streamx@npm:^2.15.0":
  version: 2.22.0
  resolution: "streamx@npm:2.22.0"
  dependencies:
    bare-events: "npm:^2.2.0"
    fast-fifo: "npm:^1.3.2"
    text-decoder: "npm:^1.1.0"
  dependenciesMeta:
    bare-events:
      optional: true
  checksum: 10c0/f5017998a5b6360ba652599d20ef308c8c8ab0e26c8e5f624f0706f0ea12624e94fdf1ec18318124498529a1b106a1ab1c94a1b1e1ad6c2eec7cb9c8ac1b9198
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^2.0.0":
  version: 2.0.0
  resolution: "strict-uri-encode@npm:2.0.0"
  checksum: 10c0/010cbc78da0e2cf833b0f5dc769e21ae74cdc5d5f5bd555f14a4a4876c8ad2c85ab8b5bdf9a722dc71a11dcd3184085e1c3c0bd50ec6bb85fffc0f28cf82597d
  languageName: node
  linkType: hard

"string-length@npm:^4.0.1":
  version: 4.0.2
  resolution: "string-length@npm:4.0.2"
  dependencies:
    char-regex: "npm:^1.0.2"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/1cd77409c3d7db7bc59406f6bcc9ef0783671dcbabb23597a1177c166906ef2ee7c8290f78cae73a8aec858768f189d2cb417797df5e15ec4eb5e16b3346340c
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.0.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.2, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/8a8854241c4b54a948e992eb7dd6b8b3a97185112deb0037a134f5ba57541d8248dd610c966311887b6c2fd1181a3877bffb14d873ce937a344535dabcc648f8
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.8, string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/59e1a70bf9414cb4c536a6e31bef5553c8ceb0cf44d8b4d0ed65c9653358d1c64dd0ec203b100df83d0413bbcde38b8c5d49e14bc4b86737d74adc593a0d35b6
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/d53af1899959e53c83b64a5fd120be93e067da740e7e75acb433849aa640782fb6c7d4cd5b84c954c84413745a3764df135a8afeb22908b86a835290788d8366
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10c0/810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"string_decoder@npm:~0.10.x":
  version: 0.10.31
  resolution: "string_decoder@npm:0.10.31"
  checksum: 10c0/1c628d78f974aa7539c496029f48e7019acc32487fc695464f9d6bdfec98edd7d933a06b3216bc2016918f6e75074c611d84430a53cb0e43071597d6c1ac5e25
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 10c0/b4f89f3a92fd101b5653ca3c99550e07bdf9e13b35037e9e2a1c7b47cec4e55e06ff3fc468e314a0b5e80bfbaf65c1ca5a84978764884ae9413bec1fc6ca924e
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 10c0/51201f50e021ef16672593d7434ca239441b7b760e905d9f33df6e4f3954ff54ec0e0a06f100d028af0982d6f25c35cd5cda2ce34eaebccd0250b8befb90d8f1
  languageName: node
  linkType: hard

"strip-bom@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-bom@npm:4.0.0"
  checksum: 10c0/26abad1172d6bc48985ab9a5f96c21e440f6e7e476686de49be813b5a59b3566dccb5c525b831ec54fe348283b47f3ffb8e080bc3f965fde12e84df23f6bb7ef
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 10c0/bddf8ccd47acd85c0e09ad7375409d81653f645fda13227a9d459642277c253d877b68f2e5e4d819fe75733b0e626bac7e954c04f3236f6d196f79c94fa4a96f
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10c0/9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"strnum@npm:^1.0.4, strnum@npm:^1.0.5":
  version: 1.0.5
  resolution: "strnum@npm:1.0.5"
  checksum: 10c0/64fb8cc2effbd585a6821faa73ad97d4b553c8927e49086a162ffd2cc818787643390b89d567460a8e74300148d11ac052e21c921ef2049f2987f4b1b89a7ff1
  languageName: node
  linkType: hard

"superagent@npm:^8.1.2":
  version: 8.1.2
  resolution: "superagent@npm:8.1.2"
  dependencies:
    component-emitter: "npm:^1.3.0"
    cookiejar: "npm:^2.1.4"
    debug: "npm:^4.3.4"
    fast-safe-stringify: "npm:^2.1.1"
    form-data: "npm:^4.0.0"
    formidable: "npm:^2.1.2"
    methods: "npm:^1.1.2"
    mime: "npm:2.6.0"
    qs: "npm:^6.11.0"
    semver: "npm:^7.3.8"
  checksum: 10c0/016416fc9c3d3a04fb648bc0efb3d3d5c9d96da00de47e4a625d9976d28c6c37ab0a7f185f2c3ec6d653ee8bb522f70fba0c1072aea7774341a6c0269a9fa77f
  languageName: node
  linkType: hard

"supertest@npm:^6.3.3":
  version: 6.3.4
  resolution: "supertest@npm:6.3.4"
  dependencies:
    methods: "npm:^1.1.2"
    superagent: "npm:^8.1.2"
  checksum: 10c0/f8c0b6c73b5e87da31feee6ccb36e7af766a438513cad89d6907f22c97edd83b1e765b4c8de955d5f7af4bca5fd0aaf9149ff48e21567dd290b326a8633af2a7
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/ea1d3c275dd604c974670f63943ed9bd83623edc102430c05adb8efc56ba492746b6e95386e7831b872ec3807fd89dd8eb43f735195f37b5ec343e4234cc7e89
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"swagger-ui-dist@npm:4.18.2":
  version: 4.18.2
  resolution: "swagger-ui-dist@npm:4.18.2"
  checksum: 10c0/63dd5e0af727edc5e10d393c6574bec248b0c812d68bbfbad1681512538a78726c77a71e248a7eb3afa78f8dad97ee00b8ec7a9bc69e13b7064676b7e6032ee6
  languageName: node
  linkType: hard

"swagger-ui-dist@npm:>=4.11.0":
  version: 5.18.3
  resolution: "swagger-ui-dist@npm:5.18.3"
  dependencies:
    "@scarf/scarf": "npm:=1.4.0"
  checksum: 10c0/3eb417cdffea16c1924b20e285ddef0cca3f4b6a7240d861524572d9e8ce78c156407d5b2e5f721c709000b9465d935da2f1140a2dea0b80e11dd1b67668df0d
  languageName: node
  linkType: hard

"swagger-ui-express@npm:^4.6.2":
  version: 4.6.3
  resolution: "swagger-ui-express@npm:4.6.3"
  dependencies:
    swagger-ui-dist: "npm:>=4.11.0"
  peerDependencies:
    express: ">=4.0.0 || >=5.0.0-beta"
  checksum: 10c0/208eb1483209ee63d3c6036e1dfbaac5405a37ddf13105e1e3da2352f1bfa398cf5f7356f926743e8c9e99b04db4e42573690eb6d4985fd8340912910d3203c0
  languageName: node
  linkType: hard

"symbol-observable@npm:4.0.0":
  version: 4.0.0
  resolution: "symbol-observable@npm:4.0.0"
  checksum: 10c0/5e9a3ab08263a6be8cbee76587ad5880dcc62a47002787ed5ebea56b1eb30dc87da6f0183d67e88286806799fbe21c69077fbd677be4be2188e92318d6c6f31d
  languageName: node
  linkType: hard

"symbol-tree@npm:^3.2.4":
  version: 3.2.4
  resolution: "symbol-tree@npm:3.2.4"
  checksum: 10c0/dfbe201ae09ac6053d163578778c53aa860a784147ecf95705de0cd23f42c851e1be7889241495e95c37cabb058edb1052f141387bef68f705afc8f9dd358509
  languageName: node
  linkType: hard

"tapable@npm:^2.1.1, tapable@npm:^2.2.0, tapable@npm:^2.2.1":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 10c0/bc40e6efe1e554d075469cedaba69a30eeb373552aaf41caeaaa45bf56ffacc2674261b106245bd566b35d8f3329b52d838e851ee0a852120acae26e622925c9
  languageName: node
  linkType: hard

"tar-stream@npm:^3.0.0":
  version: 3.1.7
  resolution: "tar-stream@npm:3.1.7"
  dependencies:
    b4a: "npm:^1.6.4"
    fast-fifo: "npm:^1.2.0"
    streamx: "npm:^2.15.0"
  checksum: 10c0/a09199d21f8714bd729993ac49b6c8efcb808b544b89f23378ad6ffff6d1cb540878614ba9d4cfec11a64ef39e1a6f009a5398371491eb1fda606ffc7f70f718
  languageName: node
  linkType: hard

"tar@npm:^6.1.11":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: "npm:^2.0.0"
    fs-minipass: "npm:^2.0.0"
    minipass: "npm:^5.0.0"
    minizlib: "npm:^2.1.1"
    mkdirp: "npm:^1.0.3"
    yallist: "npm:^4.0.0"
  checksum: 10c0/a5eca3eb50bc11552d453488344e6507156b9193efd7635e98e867fab275d527af53d8866e2370cd09dfe74378a18111622ace35af6a608e5223a7d27fe99537
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"tdigest@npm:^0.1.1":
  version: 0.1.2
  resolution: "tdigest@npm:0.1.2"
  dependencies:
    bintrees: "npm:1.0.2"
  checksum: 10c0/10187b8144b112fcdfd3a5e4e9068efa42c990b1e30cd0d4f35ee8f58f16d1b41bc587e668fa7a6f6ca31308961cbd06cd5d4a4ae1dc388335902ae04f7d57df
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.3.7":
  version: 5.3.11
  resolution: "terser-webpack-plugin@npm:5.3.11"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jest-worker: "npm:^27.4.5"
    schema-utils: "npm:^4.3.0"
    serialize-javascript: "npm:^6.0.2"
    terser: "npm:^5.31.1"
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: 10c0/4794274f445dc589f4c113c75a55ce51364ccf09bfe8a545cdb462e3f752bf300ea91f072fa28bbed291bbae03274da06fe4eca180e784fb8a43646aa7dbcaef
  languageName: node
  linkType: hard

"terser@npm:^5.31.1":
  version: 5.37.0
  resolution: "terser@npm:5.37.0"
  dependencies:
    "@jridgewell/source-map": "npm:^0.3.3"
    acorn: "npm:^8.8.2"
    commander: "npm:^2.20.0"
    source-map-support: "npm:~0.5.20"
  bin:
    terser: bin/terser
  checksum: 10c0/ff0dc79b0a0da821e7f5bf7a047eab6d04e70e88b62339a0f1d71117db3310e255f5c00738fa3b391f56c3571f800a00047720261ba04ced0241c1f9922199f4
  languageName: node
  linkType: hard

"test-exclude@npm:^6.0.0":
  version: 6.0.0
  resolution: "test-exclude@npm:6.0.0"
  dependencies:
    "@istanbuljs/schema": "npm:^0.1.2"
    glob: "npm:^7.1.4"
    minimatch: "npm:^3.0.4"
  checksum: 10c0/019d33d81adff3f9f1bfcff18125fb2d3c65564f437d9be539270ee74b994986abb8260c7c2ce90e8f30162178b09dbbce33c6389273afac4f36069c48521f57
  languageName: node
  linkType: hard

"text-decoder@npm:^1.1.0":
  version: 1.2.3
  resolution: "text-decoder@npm:1.2.3"
  dependencies:
    b4a: "npm:^1.6.4"
  checksum: 10c0/569d776b9250158681c83656ef2c3e0a5d5c660c27ca69f87eedef921749a4fbf02095e5f9a0f862a25cf35258379b06e31dee9c125c9f72e273b7ca1a6d1977
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: 10c0/02805740c12851ea5982686810702e2f14369a5f4c5c40a836821e3eefc65ffeec3131ba324692a37608294b0fd8c1e55a2dd571ffed4909822787668ddbee5c
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: "npm:>= 3.1.0 < 4"
  checksum: 10c0/9b896a22735e8122754fe70f1d65f7ee691c1d70b1f116fda04fea103d0f9b356e3676cb789506e3909ae0486a79a476e4914b0f92472c2e093d206aed4b7d6b
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: "npm:^1.0.0"
  checksum: 10c0/f375aeb2b05c100a456a30bc3ed07ef03a39cbdefe02e0403fb714b8c7e57eeaad1a2f5c4ecfb9ce554ce3db9c2b024eba144843cd9e344566d9fcee73b04767
  languageName: node
  linkType: hard

"through@npm:^2.3.6":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: 10c0/4b09f3774099de0d4df26d95c5821a62faee32c7e96fb1f4ebd54a2d7c11c57fe88b0a0d49cf375de5fee5ae6bf4eb56dbbf29d07366864e2ee805349970d3cc
  languageName: node
  linkType: hard

"tiny-inflate@npm:^1.0.0, tiny-inflate@npm:^1.0.2, tiny-inflate@npm:^1.0.3":
  version: 1.0.3
  resolution: "tiny-inflate@npm:1.0.3"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"tldts-core@npm:^6.1.86":
  version: 6.1.86
  resolution: "tldts-core@npm:6.1.86"
  checksum: 10c0/8133c29375f3f99f88fce5f4d62f6ecb9532b106f31e5423b27c1eb1b6e711bd41875184a456819ceaed5c8b94f43911b1ad57e25c6eb86e1fc201228ff7e2af
  languageName: node
  linkType: hard

"tldts@npm:^6.1.32":
  version: 6.1.86
  resolution: "tldts@npm:6.1.86"
  dependencies:
    tldts-core: "npm:^6.1.86"
  bin:
    tldts: bin/cli.js
  checksum: 10c0/27ae7526d9d78cb97b2de3f4d102e0b4321d1ccff0648a7bb0e039ed54acbce86bacdcd9cd3c14310e519b457854e7bafbef1f529f58a1e217a737ced63f0940
  languageName: node
  linkType: hard

"tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: "npm:~1.0.2"
  checksum: 10c0/69863947b8c29cabad43fe0ce65cec5bb4b481d15d4b4b21e036b060b3edbf3bc7a5541de1bacb437bb3f7c4538f669752627fdf9b4aaf034cebd172ba373408
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: 10c0/f935537799c2d1922cb5d6d3805f594388f75338fe7a4a9dac41504dd539704ca4db45b883b52e7b0aa5b2fd5ddadb1452bf95cd23a69da2f793a843f9451cc9
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10c0/93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"tough-cookie@npm:^5.1.1":
  version: 5.1.2
  resolution: "tough-cookie@npm:5.1.2"
  dependencies:
    tldts: "npm:^6.1.32"
  checksum: 10c0/5f95023a47de0f30a902bba951664b359725597d8adeabc66a0b93a931c3af801e1e697dae4b8c21a012056c0ea88bd2bf4dfe66b2adcf8e2f42cd9796fe0626
  languageName: node
  linkType: hard

"tr46@npm:^5.1.0":
  version: 5.1.1
  resolution: "tr46@npm:5.1.1"
  dependencies:
    punycode: "npm:^2.3.1"
  checksum: 10c0/ae270e194d52ec67ebd695c1a42876e0f19b96e4aca2ab464ab1d9d17dc3acd3e18764f5034c93897db73421563be27c70c98359c4501136a497e46deda5d5ec
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"tree-kill@npm:1.2.2":
  version: 1.2.2
  resolution: "tree-kill@npm:1.2.2"
  bin:
    tree-kill: cli.js
  checksum: 10c0/7b1b7c7f17608a8f8d20a162e7957ac1ef6cd1636db1aba92f4e072dc31818c2ff0efac1e3d91064ede67ed5dc57c565420531a8134090a12ac10cf792ab14d2
  languageName: node
  linkType: hard

"ts-jest@npm:^29.0.5":
  version: 29.2.5
  resolution: "ts-jest@npm:29.2.5"
  dependencies:
    bs-logger: "npm:^0.2.6"
    ejs: "npm:^3.1.10"
    fast-json-stable-stringify: "npm:^2.1.0"
    jest-util: "npm:^29.0.0"
    json5: "npm:^2.2.3"
    lodash.memoize: "npm:^4.1.2"
    make-error: "npm:^1.3.6"
    semver: "npm:^7.6.3"
    yargs-parser: "npm:^21.1.1"
  peerDependencies:
    "@babel/core": ">=7.0.0-beta.0 <8"
    "@jest/transform": ^29.0.0
    "@jest/types": ^29.0.0
    babel-jest: ^29.0.0
    jest: ^29.0.0
    typescript: ">=4.3 <6"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    "@jest/transform":
      optional: true
    "@jest/types":
      optional: true
    babel-jest:
      optional: true
    esbuild:
      optional: true
  bin:
    ts-jest: cli.js
  checksum: 10c0/acb62d168faec073e64b20873b583974ba8acecdb94681164eb346cef82ade8fb481c5b979363e01a97ce4dd1e793baf64d9efd90720bc941ad7fc1c3d6f3f68
  languageName: node
  linkType: hard

"ts-loader@npm:^9.4.2":
  version: 9.5.2
  resolution: "ts-loader@npm:9.5.2"
  dependencies:
    chalk: "npm:^4.1.0"
    enhanced-resolve: "npm:^5.0.0"
    micromatch: "npm:^4.0.0"
    semver: "npm:^7.3.4"
    source-map: "npm:^0.7.4"
  peerDependencies:
    typescript: "*"
    webpack: ^5.0.0
  checksum: 10c0/d4f4e67f1365a8c4a929d26148611b6a82a9241bd988863386c9cc0c034eec8b14562206e09540fae38154595e0b3b9520b701b5c83c0e5d743c4016cd91d9f1
  languageName: node
  linkType: hard

"ts-node@npm:^10.9.1":
  version: 10.9.2
  resolution: "ts-node@npm:10.9.2"
  dependencies:
    "@cspotcode/source-map-support": "npm:^0.8.0"
    "@tsconfig/node10": "npm:^1.0.7"
    "@tsconfig/node12": "npm:^1.0.7"
    "@tsconfig/node14": "npm:^1.0.0"
    "@tsconfig/node16": "npm:^1.0.2"
    acorn: "npm:^8.4.1"
    acorn-walk: "npm:^8.1.1"
    arg: "npm:^4.1.0"
    create-require: "npm:^1.1.0"
    diff: "npm:^4.0.1"
    make-error: "npm:^1.1.1"
    v8-compile-cache-lib: "npm:^3.0.1"
    yn: "npm:3.1.1"
  peerDependencies:
    "@swc/core": ">=1.2.50"
    "@swc/wasm": ">=1.2.50"
    "@types/node": "*"
    typescript: ">=2.7"
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    "@swc/wasm":
      optional: true
  bin:
    ts-node: dist/bin.js
    ts-node-cwd: dist/bin-cwd.js
    ts-node-esm: dist/bin-esm.js
    ts-node-script: dist/bin-script.js
    ts-node-transpile-only: dist/bin-transpile.js
    ts-script: dist/bin-script-deprecated.js
  checksum: 10c0/5f29938489f96982a25ba650b64218e83a3357d76f7bede80195c65ab44ad279c8357264639b7abdd5d7e75fc269a83daa0e9c62fd8637a3def67254ecc9ddc2
  languageName: node
  linkType: hard

"ts-toolbelt@npm:^6.15.1":
  version: 6.15.5
  resolution: "ts-toolbelt@npm:6.15.5"
  checksum: 10c0/c2c5cf77b006cbedb836abb260e9544aef268b0fc718e52db44600308ac6836c8c1824f57373ed72f920b92eb1daa78d1a9cec4c12368230a208e0723de9c2f0
  languageName: node
  linkType: hard

"tsconfig-paths-webpack-plugin@npm:4.0.1":
  version: 4.0.1
  resolution: "tsconfig-paths-webpack-plugin@npm:4.0.1"
  dependencies:
    chalk: "npm:^4.1.0"
    enhanced-resolve: "npm:^5.7.0"
    tsconfig-paths: "npm:^4.1.2"
  checksum: 10c0/c5d09b669b07806b5f4e5aafd893824a9e302ddb8e011ad48c4cf65143414b5b317f09d84e2b8a13ad1e5b52d658e7eb1f71af1dc267533eb766b3f144963b30
  languageName: node
  linkType: hard

"tsconfig-paths@npm:4.2.0, tsconfig-paths@npm:^4.1.2, tsconfig-paths@npm:^4.2.0":
  version: 4.2.0
  resolution: "tsconfig-paths@npm:4.2.0"
  dependencies:
    json5: "npm:^2.2.2"
    minimist: "npm:^1.2.6"
    strip-bom: "npm:^3.0.0"
  checksum: 10c0/09a5877402d082bb1134930c10249edeebc0211f36150c35e1c542e5b91f1047b1ccf7da1e59babca1ef1f014c525510f4f870de7c9bda470c73bb4e2721b3ea
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.15.0":
  version: 3.15.0
  resolution: "tsconfig-paths@npm:3.15.0"
  dependencies:
    "@types/json5": "npm:^0.0.29"
    json5: "npm:^1.0.2"
    minimist: "npm:^1.2.6"
    strip-bom: "npm:^3.0.0"
  checksum: 10c0/5b4f301a2b7a3766a986baf8fc0e177eb80bdba6e396792ff92dc23b5bca8bb279fc96517dcaaef63a3b49bebc6c4c833653ec58155780bc906bdbcf7dda0ef5
  languageName: node
  linkType: hard

"tslib@npm:2.5.3":
  version: 2.5.3
  resolution: "tslib@npm:2.5.3"
  checksum: 10c0/4cb1817d34fae5b27d146e6c4a468d4155097d95c1335d0bc9690f11f33e63844806bf4ed6d97c30c72b8d85261b66cbbe16d871d9c594ac05701ec83e62a607
  languageName: node
  linkType: hard

"tslib@npm:^1.11.1, tslib@npm:^1.8.1":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: 10c0/69ae09c49eea644bc5ebe1bca4fa4cc2c82b7b3e02f43b84bd891504edf66dbc6b2ec0eef31a957042de2269139e4acff911e6d186a258fb14069cd7f6febce2
  languageName: node
  linkType: hard

"tslib@npm:^2.1.0, tslib@npm:^2.2.0, tslib@npm:^2.4.0, tslib@npm:^2.5.0, tslib@npm:^2.6.2":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"tsutils@npm:^3.21.0":
  version: 3.21.0
  resolution: "tsutils@npm:3.21.0"
  dependencies:
    tslib: "npm:^1.8.1"
  peerDependencies:
    typescript: ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"
  checksum: 10c0/02f19e458ec78ead8fffbf711f834ad8ecd2cc6ade4ec0320790713dccc0a412b99e7fd907c4cda2a1dc602c75db6f12e0108e87a5afad4b2f9e90a24cabd5a2
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10c0/7b3fd0ed43891e2080bf0c5c504b418fbb3e5c7b9708d3d015037ba2e6323a28152ec163bcb65212741fa5d2022e3075ac3c76440dbd344c9035f818e8ecee58
  languageName: node
  linkType: hard

"type-detect@npm:4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 10c0/8fb9a51d3f365a7de84ab7f73b653534b61b622aa6800aecdb0f1095a4a646d3f5eb295322127b6573db7982afcd40ab492d038cf825a42093a58b1e1353e0bd
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 10c0/dea9df45ea1f0aaa4e2d3bed3f9a0bfe9e5b2592bddb92eb1bf06e50bcf98dbb78189668cd8bc31a0511d3fc25539b4cd5c704497e53e93e2d40ca764b10bfc3
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10c0/902bd57bfa30d51d4779b641c2bc403cdf1371fb9c91d3c058b0133694fcfdb817aef07a47f40faf79039eecbaa39ee9d3c532deff244f3a19ce68cea71a61e8
  languageName: node
  linkType: hard

"type-is@npm:^1.6.4, type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 10c0/a23daeb538591b7efbd61ecf06b6feb2501b683ffdc9a19c74ef5baba362b4347e42f1b4ed81f5882a8c96a3bfff7f93ce3ffaf0cbbc879b532b04c97a55db9d
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/1105071756eb248774bc71646bfe45b682efcad93b55532c6ffa4518969fb6241354e4aa62af679ae83899ec296d69ef88f1f3763657cdb3a4d29321f7b83079
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/6ae083c6f0354f1fce18b90b243343b9982affd8d839c57bbd2c174a5d5dc71be9eb7019ffd12628a96a4815e7afa85d718d6f1e758615151d5f35df841ffb3e
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 10c0/3d805b050c0c33b51719ee52de17c1cd8e6a571abdf0fffb110e45e8dd87a657e8b56eee94b776b13006d3d347a0c18a730b903cf05293ab6d92e99ff8f77e53
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: 10c0/e38f2ae3779584c138a2d8adfa8ecf749f494af3cd3cdafe4e688ce51418c7d2c5c88df1bd6be2bbea099c3f7cea58c02ca02ed438119e91f162a9de23f61295
  languageName: node
  linkType: hard

"typedarray@npm:^0.0.6":
  version: 0.0.6
  resolution: "typedarray@npm:0.0.6"
  checksum: 10c0/6005cb31df50eef8b1f3c780eb71a17925f3038a100d82f9406ac2ad1de5eb59f8e6decbdc145b3a1f8e5836e17b0c0002fb698b9fe2516b8f9f9ff602d36412
  languageName: node
  linkType: hard

"typeorm@npm:^0.3.12":
  version: 0.3.20
  resolution: "typeorm@npm:0.3.20"
  dependencies:
    "@sqltools/formatter": "npm:^1.2.5"
    app-root-path: "npm:^3.1.0"
    buffer: "npm:^6.0.3"
    chalk: "npm:^4.1.2"
    cli-highlight: "npm:^2.1.11"
    dayjs: "npm:^1.11.9"
    debug: "npm:^4.3.4"
    dotenv: "npm:^16.0.3"
    glob: "npm:^10.3.10"
    mkdirp: "npm:^2.1.3"
    reflect-metadata: "npm:^0.2.1"
    sha.js: "npm:^2.4.11"
    tslib: "npm:^2.5.0"
    uuid: "npm:^9.0.0"
    yargs: "npm:^17.6.2"
  peerDependencies:
    "@google-cloud/spanner": ^5.18.0
    "@sap/hana-client": ^2.12.25
    better-sqlite3: ^7.1.2 || ^8.0.0 || ^9.0.0
    hdb-pool: ^0.1.6
    ioredis: ^5.0.4
    mongodb: ^5.8.0
    mssql: ^9.1.1 || ^10.0.1
    mysql2: ^2.2.5 || ^3.0.1
    oracledb: ^6.3.0
    pg: ^8.5.1
    pg-native: ^3.0.0
    pg-query-stream: ^4.0.0
    redis: ^3.1.1 || ^4.0.0
    sql.js: ^1.4.0
    sqlite3: ^5.0.3
    ts-node: ^10.7.0
    typeorm-aurora-data-api-driver: ^2.0.0
  peerDependenciesMeta:
    "@google-cloud/spanner":
      optional: true
    "@sap/hana-client":
      optional: true
    better-sqlite3:
      optional: true
    hdb-pool:
      optional: true
    ioredis:
      optional: true
    mongodb:
      optional: true
    mssql:
      optional: true
    mysql2:
      optional: true
    oracledb:
      optional: true
    pg:
      optional: true
    pg-native:
      optional: true
    pg-query-stream:
      optional: true
    redis:
      optional: true
    sql.js:
      optional: true
    sqlite3:
      optional: true
    ts-node:
      optional: true
    typeorm-aurora-data-api-driver:
      optional: true
  bin:
    typeorm: cli.js
    typeorm-ts-node-commonjs: cli-ts-node-commonjs.js
    typeorm-ts-node-esm: cli-ts-node-esm.js
  checksum: 10c0/7e4be724641beef86ae36289c87b6e66bfaf19a4313f089926d36d2d6f0d67f9314d942711c9d83ab8a174b8622148c2f7e83e6c1448d638ee3ab24469257814
  languageName: node
  linkType: hard

"typescript@npm:4.9.5, typescript@npm:^4.9.5":
  version: 4.9.5
  resolution: "typescript@npm:4.9.5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/5f6cad2e728a8a063521328e612d7876e12f0d8a8390d3b3aaa452a6a65e24e9ac8ea22beb72a924fd96ea0a49ea63bb4e251fb922b12eedfb7f7a26475e5c56
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A4.9.5#optional!builtin<compat/typescript>, typescript@patch:typescript@npm%3A^4.9.5#optional!builtin<compat/typescript>":
  version: 4.9.5
  resolution: "typescript@patch:typescript@npm%3A4.9.5#optional!builtin<compat/typescript>::version=4.9.5&hash=289587"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/e3333f887c6829dfe0ab6c1dbe0dd1e3e2aeb56c66460cb85c5440c566f900c833d370ca34eb47558c0c69e78ced4bfe09b8f4f98b6de7afed9b84b8d1dd06a1
  languageName: node
  linkType: hard

"uglify-js@npm:^3.1.4":
  version: 3.19.3
  resolution: "uglify-js@npm:3.19.3"
  bin:
    uglifyjs: bin/uglifyjs
  checksum: 10c0/83b0a90eca35f778e07cad9622b80c448b6aad457c9ff8e568afed978212b42930a95f9e1be943a1ffa4258a3340fbb899f41461131c05bb1d0a9c303aed8479
  languageName: node
  linkType: hard

"uid@npm:2.0.2":
  version: 2.0.2
  resolution: "uid@npm:2.0.2"
  dependencies:
    "@lukeed/csprng": "npm:^1.0.0"
  checksum: 10c0/e9d02d0562c74e74b5a2519e586db9d7f8204978e476cddd191ee1a9efb85efafdbab2dbf3fc3dde0f5da01fd9da161f37d604dabf513447fd2c03d008f1324c
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 10c0/7dbd35ab02b0e05fe07136c72cb9355091242455473ec15057c11430129bab38b7b3624019b8778d02a881c13de44d63cd02d122ee782fb519e1de7775b5b982
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 10c0/bb673d7876c2d411b6eb6c560e0c571eef4a01c1c19925175d16e3a30c4c428181fb8d7ae802a261f283e4166a0ac435e2f505743aa9e45d893f9a3df017b501
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: 10c0/68e659a98898d6a836a9a59e6adf14a5d799707f5ea629433e025ac90d239f75e408e2e5ff086afc3cace26f8b26ee52155293564593fbb4a2f666af57fc59bf
  languageName: node
  linkType: hard

"undici@npm:^6.19.5":
  version: 6.21.1
  resolution: "undici@npm:6.21.1"
  checksum: 10c0/d604080e4f8db89b35a63b483b5f96a5f8b19ec9f716e934639345449405809d2997e1dd7212d67048f210e54534143384d712bd9075e4394f0788895ef9ca8e
  languageName: node
  linkType: hard

"unicode-properties@npm:^1.2.2, unicode-properties@npm:^1.3.1":
  version: 1.4.1
  resolution: "unicode-properties@npm:1.4.1"
  dependencies:
    base64-js: "npm:^1.3.0"
    unicode-trie: "npm:^2.0.0"
  checksum: 10c0/1d140b7945664fb0ef53de955170821e077b949eef377c6e4905902f07e339039271bfa2a005e4f4c6074b080d3420b486c52dc905e11f924949a04d1fb47ffd
  languageName: node
  linkType: hard

"unicode-trie@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-trie@npm:2.0.0"
  dependencies:
    pako: "npm:^0.2.5"
    tiny-inflate: "npm:^1.0.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10c0/193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.1":
  version: 1.1.2
  resolution: "update-browserslist-db@npm:1.1.2"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/9cb353998d6d7d6ba1e46b8fa3db888822dd972212da4eda609d185eb5c3557a93fd59780ceb757afd4d84240518df08542736969e6a5d6d6ce2d58e9363aac6
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"url@npm:0.10.3":
  version: 0.10.3
  resolution: "url@npm:0.10.3"
  dependencies:
    punycode: "npm:1.3.2"
    querystring: "npm:0.2.0"
  checksum: 10c0/f0a1c7d99ac35dd68a8962bc7b3dd38f08d457387fc686f0669ff881b00a68eabd9cb3aded09dfbe25401d7b632fc4a9c074cb373f6a4bd1d8b5324d1d442a0d
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"util@npm:^0.12.4":
  version: 0.12.5
  resolution: "util@npm:0.12.5"
  dependencies:
    inherits: "npm:^2.0.3"
    is-arguments: "npm:^1.0.4"
    is-generator-function: "npm:^1.0.7"
    is-typed-array: "npm:^1.1.3"
    which-typed-array: "npm:^1.1.2"
  checksum: 10c0/c27054de2cea2229a66c09522d0fa1415fb12d861d08523a8846bf2e4cbf0079d4c3f725f09dcb87493549bcbf05f5798dce1688b53c6c17201a45759e7253f3
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10c0/02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"uuid@npm:8.0.0":
  version: 8.0.0
  resolution: "uuid@npm:8.0.0"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/e62301a1c6102da5ce9a147b492a4b5cfa14d2e8fdf4a6ebfda7929cb72d186f84173815ec18fa4160a03bf9724b16ece3737b3ac6701815bc965f8fa4279298
  languageName: node
  linkType: hard

"uuid@npm:8.3.2, uuid@npm:^8.3.0":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/bcbb807a917d374a49f475fae2e87fdca7da5e5530820ef53f65ba1d12131bd81a92ecf259cc7ce317cbe0f289e7d79fdfebcef9bfa3087c8c8a2fa304c9be54
  languageName: node
  linkType: hard

"uuid@npm:9.0.0":
  version: 9.0.0
  resolution: "uuid@npm:9.0.0"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/8867e438990d1d33ac61093e2e4e3477a2148b844e4fa9e3c2360fa4399292429c4b6ec64537eb1659c97b2d10db349c673ad58b50e2824a11e0d3630de3c056
  languageName: node
  linkType: hard

"uuid@npm:^9.0.0, uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/1607dd32ac7fc22f2d8f77051e6a64845c9bce5cd3dd8aa0070c074ec73e666a1f63c7b4e0f4bf2bc8b9d59dc85a15e17807446d9d2b17c8485fbc2147b27f9b
  languageName: node
  linkType: hard

"v8-compile-cache-lib@npm:^3.0.1":
  version: 3.0.1
  resolution: "v8-compile-cache-lib@npm:3.0.1"
  checksum: 10c0/bdc36fb8095d3b41df197f5fb6f11e3a26adf4059df3213e3baa93810d8f0cc76f9a74aaefc18b73e91fe7e19154ed6f134eda6fded2e0f1c8d2272ed2d2d391
  languageName: node
  linkType: hard

"v8-to-istanbul@npm:^9.0.1":
  version: 9.3.0
  resolution: "v8-to-istanbul@npm:9.3.0"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.12"
    "@types/istanbul-lib-coverage": "npm:^2.0.1"
    convert-source-map: "npm:^2.0.0"
  checksum: 10c0/968bcf1c7c88c04df1ffb463c179558a2ec17aa49e49376120504958239d9e9dad5281aa05f2a78542b8557f2be0b0b4c325710262f3b838b40d703d5ed30c23
  languageName: node
  linkType: hard

"validator@npm:^13.9.0":
  version: 13.12.0
  resolution: "validator@npm:13.12.0"
  checksum: 10c0/21d48a7947c9e8498790550f56cd7971e0e3d724c73388226b109c1bac2728f4f88caddfc2f7ed4b076f9b0d004316263ac786a17e9c4edf075741200718cd32
  languageName: node
  linkType: hard

"vary@npm:^1, vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10c0/f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"w3c-xmlserializer@npm:^5.0.0":
  version: 5.0.0
  resolution: "w3c-xmlserializer@npm:5.0.0"
  dependencies:
    xml-name-validator: "npm:^5.0.0"
  checksum: 10c0/8712774c1aeb62dec22928bf1cdfd11426c2c9383a1a63f2bcae18db87ca574165a0fbe96b312b73652149167ac6c7f4cf5409f2eb101d9c805efe0e4bae798b
  languageName: node
  linkType: hard

"walker@npm:^1.0.8":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: "npm:1.0.12"
  checksum: 10c0/a17e037bccd3ca8a25a80cb850903facdfed0de4864bd8728f1782370715d679fa72e0a0f5da7c1c1379365159901e5935f35be531229da53bbfc0efdabdb48e
  languageName: node
  linkType: hard

"watchpack@npm:^2.4.0":
  version: 2.4.2
  resolution: "watchpack@npm:2.4.2"
  dependencies:
    glob-to-regexp: "npm:^0.4.1"
    graceful-fs: "npm:^4.1.2"
  checksum: 10c0/ec60a5f0e9efaeca0102fd9126346b3b2d523e01c34030d3fddf5813a7125765121ebdc2552981136dcd2c852deb1af0b39340f2fcc235f292db5399d0283577
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: "npm:^1.0.3"
  checksum: 10c0/5b61ca583a95e2dd85d7078400190efd452e05751a64accb8c06ce4db65d7e0b0cde9917d705e826a2e05cc2548f61efde115ffa374c3e436d04be45c889e5b4
  languageName: node
  linkType: hard

"web-streams-polyfill@npm:4.0.0-beta.3":
  version: 4.0.0-beta.3
  resolution: "web-streams-polyfill@npm:4.0.0-beta.3"
  checksum: 10c0/a9596779db2766990117ed3a158e0b0e9f69b887a6d6ba0779940259e95f99dc3922e534acc3e5a117b5f5905300f527d6fbf8a9f0957faf1d8e585ce3452e8e
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"webidl-conversions@npm:^7.0.0":
  version: 7.0.0
  resolution: "webidl-conversions@npm:7.0.0"
  checksum: 10c0/228d8cb6d270c23b0720cb2d95c579202db3aaf8f633b4e9dd94ec2000a04e7e6e43b76a94509cdb30479bd00ae253ab2371a2da9f81446cc313f89a4213a2c4
  languageName: node
  linkType: hard

"webpack-node-externals@npm:3.0.0":
  version: 3.0.0
  resolution: "webpack-node-externals@npm:3.0.0"
  checksum: 10c0/9f645a4dc8e122dac43cdc8c1367d4b44af20c79632438b633acc1b4fe64ea7ba1ad6ab61bd0fc46e1b873158c48d8c7a25a489cdab1f31299f00eb3b81cfc61
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.3":
  version: 3.2.3
  resolution: "webpack-sources@npm:3.2.3"
  checksum: 10c0/2ef63d77c4fad39de4a6db17323d75eb92897b32674e97d76f0a1e87c003882fc038571266ad0ef581ac734cbe20952912aaa26155f1905e96ce251adbb1eb4e
  languageName: node
  linkType: hard

"webpack@npm:5.82.1":
  version: 5.82.1
  resolution: "webpack@npm:5.82.1"
  dependencies:
    "@types/eslint-scope": "npm:^3.7.3"
    "@types/estree": "npm:^1.0.0"
    "@webassemblyjs/ast": "npm:^1.11.5"
    "@webassemblyjs/wasm-edit": "npm:^1.11.5"
    "@webassemblyjs/wasm-parser": "npm:^1.11.5"
    acorn: "npm:^8.7.1"
    acorn-import-assertions: "npm:^1.7.6"
    browserslist: "npm:^4.14.5"
    chrome-trace-event: "npm:^1.0.2"
    enhanced-resolve: "npm:^5.14.0"
    es-module-lexer: "npm:^1.2.1"
    eslint-scope: "npm:5.1.1"
    events: "npm:^3.2.0"
    glob-to-regexp: "npm:^0.4.1"
    graceful-fs: "npm:^4.2.9"
    json-parse-even-better-errors: "npm:^2.3.1"
    loader-runner: "npm:^4.2.0"
    mime-types: "npm:^2.1.27"
    neo-async: "npm:^2.6.2"
    schema-utils: "npm:^3.1.2"
    tapable: "npm:^2.1.1"
    terser-webpack-plugin: "npm:^5.3.7"
    watchpack: "npm:^2.4.0"
    webpack-sources: "npm:^3.2.3"
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 10c0/8583c71f30af348d16b63b8c2112653445063351b8bebd6033451a3bcf89176c1aec45fcd0f5a048e429e78004b79bb76d1a19f6e166d5bd802306a2df19b725
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^3.1.1":
  version: 3.1.1
  resolution: "whatwg-encoding@npm:3.1.1"
  dependencies:
    iconv-lite: "npm:0.6.3"
  checksum: 10c0/273b5f441c2f7fda3368a496c3009edbaa5e43b71b09728f90425e7f487e5cef9eb2b846a31bd760dd8077739c26faf6b5ca43a5f24033172b003b72cf61a93e
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^4.0.0":
  version: 4.0.0
  resolution: "whatwg-mimetype@npm:4.0.0"
  checksum: 10c0/a773cdc8126b514d790bdae7052e8bf242970cebd84af62fb2f35a33411e78e981f6c0ab9ed1fe6ec5071b09d5340ac9178e05b52d35a9c4bcf558ba1b1551df
  languageName: node
  linkType: hard

"whatwg-url@npm:^14.0.0, whatwg-url@npm:^14.1.1":
  version: 14.2.0
  resolution: "whatwg-url@npm:14.2.0"
  dependencies:
    tr46: "npm:^5.1.0"
    webidl-conversions: "npm:^7.0.0"
  checksum: 10c0/f746fc2f4c906607d09537de1227b13f9494c171141e5427ed7d2c0dd0b6a48b43d8e71abaae57d368d0c06b673fd8ec63550b32ad5ed64990c7b0266c2b4272
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.0.2, which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: 10c0/aceea8ede3b08dede7dce168f3883323f7c62272b49801716e8332ff750e7ae59a511ae088840bc6874f16c1b7fd296c05c949b0e5b357bfe3c431b98c417abe
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/8dcf323c45e5c27887800df42fbe0431d0b66b1163849bb7d46b5a730ad6a96ee8bfe827d078303f825537844ebf20c02459de41239a0a9805e2fcb3cae0d471
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.1, which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 10c0/3345fde20964525a04cdf7c4a96821f85f0cc198f1b2ecb4576e08096746d129eb133571998fe121c77782ac8f21cbd67745a3d35ce100d26d4e684c142ea1f2
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.13":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/702b5dc878addafe6c6300c3d0af5983b175c75fcb4f2a72dfc3dd38d93cf9e89581e4b29c854b16ea37e50a7d7fca5ae42ece5c273d8060dcd603b2404bbb3f
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.18, which-typed-array@npm:^1.1.2":
  version: 1.1.18
  resolution: "which-typed-array@npm:1.1.18"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/0412f4a91880ca1a2a63056187c2e3de6b129b2b5b6c17bc3729f0f7041047ae48fb7424813e51506addb2c97320003ee18b8c57469d2cde37983ef62126143c
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.2":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: "npm:^1.0.2 || 2 || 3 || 4"
  checksum: 10c0/1d9c2a3e36dfb09832f38e2e699c367ef190f96b82c71f809bc0822c306f5379df87bab47bed27ea99106d86447e50eb972d3c516c2f95782807a9d082fbea95
  languageName: node
  linkType: hard

"widest-line@npm:^3.1.0":
  version: 3.1.0
  resolution: "widest-line@npm:3.1.0"
  dependencies:
    string-width: "npm:^4.0.0"
  checksum: 10c0/b1e623adcfb9df35350dd7fc61295d6d4a1eaa65a406ba39c4b8360045b614af95ad10e05abf704936ed022569be438c4bfa02d6d031863c4166a238c301119f
  languageName: node
  linkType: hard

"windows-release@npm:^4.0.0":
  version: 4.0.0
  resolution: "windows-release@npm:4.0.0"
  dependencies:
    execa: "npm:^4.0.2"
  checksum: 10c0/5c0ce2603a85e25e9a5c78eb1ef646aac7036da2fb942643f2120b11fc33ed94fbcdd340b2abbf27daa522efc9e52df36fe95b1c03cd9acd8d6c6c39f88f106b
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10c0/e0e4a1ca27599c92a6ca4c32260e8a92e8a44f4ef6ef93f803f8ed823f486e0889fc0b93be4db59c8d51b3064951d25e43d434e95dc8c960cc3a63d65d00ba20
  languageName: node
  linkType: hard

"wordwrap@npm:^1.0.0":
  version: 1.0.0
  resolution: "wordwrap@npm:1.0.0"
  checksum: 10c0/7ed2e44f3c33c5c3e3771134d2b0aee4314c9e49c749e37f464bf69f2bcdf0cbf9419ca638098e2717cff4875c47f56a007532f6111c3319f557a2ca91278e92
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"write-file-atomic@npm:^4.0.2":
  version: 4.0.2
  resolution: "write-file-atomic@npm:4.0.2"
  dependencies:
    imurmurhash: "npm:^0.1.4"
    signal-exit: "npm:^3.0.7"
  checksum: 10c0/a2c282c95ef5d8e1c27b335ae897b5eca00e85590d92a3fd69a437919b7b93ff36a69ea04145da55829d2164e724bc62202cdb5f4b208b425aba0807889375c7
  languageName: node
  linkType: hard

"ws@npm:^8.18.0":
  version: 8.18.1
  resolution: "ws@npm:8.18.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/e498965d6938c63058c4310ffb6967f07d4fa06789d3364829028af380d299fe05762961742971c764973dce3d1f6a2633fe8b2d9410c9b52e534b4b882a99fa
  languageName: node
  linkType: hard

"xml-name-validator@npm:^5.0.0":
  version: 5.0.0
  resolution: "xml-name-validator@npm:5.0.0"
  checksum: 10c0/3fcf44e7b73fb18be917fdd4ccffff3639373c7cb83f8fc35df6001fecba7942f1dbead29d91ebb8315e2f2ff786b508f0c9dc0215b6353f9983c6b7d62cb1f5
  languageName: node
  linkType: hard

"xml2js@npm:0.6.2":
  version: 0.6.2
  resolution: "xml2js@npm:0.6.2"
  dependencies:
    sax: "npm:>=0.6.0"
    xmlbuilder: "npm:~11.0.0"
  checksum: 10c0/e98a84e9c172c556ee2c5afa0fc7161b46919e8b53ab20de140eedea19903ed82f7cd5b1576fb345c84f0a18da1982ddf65908129b58fc3d7cbc658ae232108f
  languageName: node
  linkType: hard

"xmlbuilder@npm:~11.0.0":
  version: 11.0.1
  resolution: "xmlbuilder@npm:11.0.1"
  checksum: 10c0/74b979f89a0a129926bc786b913459bdbcefa809afaa551c5ab83f89b1915bdaea14c11c759284bb9b931e3b53004dbc2181e21d3ca9553eeb0b2a7b4e40c35b
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: 10c0/b64b535861a6f310c5d9bfa10834cf49127c71922c297da9d4d1b45eeaae40bf9b4363275876088fbe2667e5db028d2cd4f8ee72eed9bede840a67d57dab7593
  languageName: node
  linkType: hard

"xmldoc@npm:^1.3.0":
  version: 1.3.0
  resolution: "xmldoc@npm:1.3.0"
  dependencies:
    sax: "npm:^1.2.4"
  checksum: 10c0/7957c57ff77008ced62063560d3e8f80c7fdf31d3fafa12d16c9f1fe676c8255f50889e8f41b61cca4cd473b841eedf2625089dcaf3f6b8717df521b9d0acfcf
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10c0/366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10c0/4df2842c36e468590c3691c894bc9cdbac41f520566e76e24f59401ba7d8b4811eb1e34524d57e54bc6d864bcb66baab7ffd9ca42bf1eda596618f9162b91249
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yaml@npm:^1.10.0":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: 10c0/5c28b9eb7adc46544f28d9a8d20c5b3cb1215a886609a2fd41f51628d8aaa5878ccd628b755dbcd29f6bb4921bd04ffbc6dcc370689bb96e594e2f9813d2605f
  languageName: node
  linkType: hard

"yargs-parser@npm:21.1.1, yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10c0/f84b5e48169479d2f402239c59f084cfd1c3acc197a05c59b98bab067452e6b3ea46d4dd8ba2985ba7b3d32a343d77df0debd6b343e5dae3da2aab2cdf5886b2
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 10c0/0685a8e58bbfb57fab6aefe03c6da904a59769bd803a722bb098bd5b0f29d274a1357762c7258fb487512811b8063fb5d2824a3415a0a4540598335b3b086c72
  languageName: node
  linkType: hard

"yargs@npm:^16.0.0":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: "npm:^7.0.2"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.0"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^20.2.2"
  checksum: 10c0/b1dbfefa679848442454b60053a6c95d62f2d2e21dd28def92b647587f415969173c6e99a0f3bab4f1b67ee8283bf735ebe3544013f09491186ba9e8a9a2b651
  languageName: node
  linkType: hard

"yargs@npm:^17.3.1, yargs@npm:^17.6.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10c0/ccd7e723e61ad5965fffbb791366db689572b80cca80e0f96aad968dfff4156cd7cd1ad18607afe1046d8241e6fb2d6c08bf7fa7bfb5eaec818735d8feac8f05
  languageName: node
  linkType: hard

"yn@npm:3.1.1":
  version: 3.1.1
  resolution: "yn@npm:3.1.1"
  checksum: 10c0/0732468dd7622ed8a274f640f191f3eaf1f39d5349a1b72836df484998d7d9807fbea094e2f5486d6b0cd2414aad5775972df0e68f8604db89a239f0f4bf7443
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"zip-stream@npm:^5.0.1":
  version: 5.0.2
  resolution: "zip-stream@npm:5.0.2"
  dependencies:
    archiver-utils: "npm:^4.0.1"
    compress-commons: "npm:^5.0.1"
    readable-stream: "npm:^3.6.0"
  checksum: 10c0/cb5c4b57771a03429188ae73f90744f6996aa98c885852970de1c8bed3351c8a931cce0cf74cf37b9fa3727a07119236def871ec6d05c9becbc80746f52dd795
  languageName: node
  linkType: hard
