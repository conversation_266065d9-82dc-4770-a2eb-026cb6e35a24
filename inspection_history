{"list": [{"id": 117, "createdAt": "2025-06-11T07:45:53.909Z", "updatedAt": "2025-06-11T07:45:53.909Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 58023995, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2024-10-23T07:58:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "109801", "inspectionHistoryPlaceName": "PIA Zell am See, address7", "inspectionHistoryContact": "40d110ba-5455-4ab8-a3c9-b9605a25bc62", "inspectionHistoryContactName": "SEECn", "alternativeContactField": "", "salutation": "<PERSON>", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"licenseNumber": "MD222222", "txtLossNumber": "206-1-06225-23", "txtInsuranceNumber": "", "inspectionHistoryButton": ""}}, {"id": 10, "createdAt": "2025-04-15T05:49:19.018Z", "updatedAt": "2025-04-15T05:49:19.018Z", "customerNumber": 3500667, "username": "tuceguen", "contractId": 55489782, "inspectionHistoryName": "<PERSON><PERSON><PERSON>", "inspectionHistoryDate": "2025-04-15T05:49:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "empty", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 17, "createdAt": "2025-04-15T12:11:52.422Z", "updatedAt": "2025-04-15T12:11:52.422Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 39139448, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-15T12:11:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "109825", "inspectionHistoryPlaceName": "Porsche Inter Auto GmbH & Co KG Zweigniederlassung Porsche Wien Liesing", "inspectionHistoryContact": "e2c7d828-1b43-4adf-b8d9-022bcb9322be", "inspectionHistoryContactName": "<PERSON>", "alternativeContactField": "", "salutation": "<PERSON>", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 16, "createdAt": "2025-04-15T12:11:13.420Z", "updatedAt": "2025-04-15T12:11:13.420Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 39139448, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-15T12:11:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "109801", "inspectionHistoryPlaceName": "PIA Zell am See", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 18, "createdAt": "2025-04-15T12:12:18.387Z", "updatedAt": "2025-04-15T12:12:18.387Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 39139448, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-15T12:12:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "empty", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 19, "createdAt": "2025-04-15T12:28:51.517Z", "updatedAt": "2025-04-15T12:28:51.517Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 55843221, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-15T12:28:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 22, "createdAt": "2025-04-16T02:54:41.297Z", "updatedAt": "2025-04-16T02:54:41.297Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 49888025, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-16T02:54:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "9c5653e2-997f-4f7f-aea6-fe872fd3f942", "inspectionHistoryContactName": "Test", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 23, "createdAt": "2025-04-16T03:05:02.794Z", "updatedAt": "2025-04-16T03:05:02.794Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 49888025, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-16T03:04:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "9c5653e2-997f-4f7f-aea6-fe872fd3f942", "inspectionHistoryContactName": "Test", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 25, "createdAt": "2025-04-16T03:15:26.791Z", "updatedAt": "2025-04-16T03:15:26.791Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 55907224, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-16T03:15:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 26, "createdAt": "2025-04-16T03:19:39.719Z", "updatedAt": "2025-04-16T03:19:39.719Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 55907224, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-16T03:19:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 29, "createdAt": "2025-04-16T15:54:12.926Z", "updatedAt": "2025-04-16T15:54:12.926Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 37993522, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-16T15:54:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "110470", "inspectionHistoryPlaceName": "Figl GmbH Markus VW Audi", "inspectionHistoryContact": "c5126529-bf87-459a-b765-f8d24713b457", "inspectionHistoryContactName": "Dummy", "alternativeContactField": "", "salutation": "<PERSON>", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 31, "createdAt": "2025-04-16T15:54:46.082Z", "updatedAt": "2025-04-16T15:54:46.082Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 37993522, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-16T15:54:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "empty", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 30, "createdAt": "2025-04-16T15:54:32.713Z", "updatedAt": "2025-04-16T15:54:32.713Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 37993522, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-16T15:54:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "104986", "inspectionHistoryPlaceName": "<PERSON><PERSON>t Ges.m.b.H. BMW-Vertragshändler", "inspectionHistoryContact": "empty", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 32, "createdAt": "2025-04-16T15:56:12.133Z", "updatedAt": "2025-04-16T15:56:12.133Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 55769797, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-16T15:55:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "409407", "inspectionHistoryPlaceName": "Franz Gady GmbH", "inspectionHistoryContact": "71b969c3-8f7d-4ac3-89b6-a58c169a3264", "inspectionHistoryContactName": "Test", "alternativeContactField": "", "salutation": "<PERSON>", "inspectionHistoryStatus": "open", "inspectionHistoryInspection": "driveIn", "inspectionHistoryCondition": "repaired", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 34, "createdAt": "2025-04-16T15:56:53.695Z", "updatedAt": "2025-04-16T15:56:53.695Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 55769797, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-16T15:56:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "210072", "inspectionHistoryPlaceName": "Zöhrer KFZ-Meisterbetrieb", "inspectionHistoryContact": "empty", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 33, "createdAt": "2025-04-16T15:56:30.574Z", "updatedAt": "2025-04-16T15:56:30.574Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 55769797, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-16T15:56:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "409407", "inspectionHistoryPlaceName": "Franz Gady GmbH", "inspectionHistoryContact": "71b969c3-8f7d-4ac3-89b6-a58c169a3264", "inspectionHistoryContactName": "Test", "alternativeContactField": "", "salutation": "<PERSON>", "inspectionHistoryStatus": "unsuccessful", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "not_defined", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 40, "createdAt": "2025-04-17T08:21:08.301Z", "updatedAt": "2025-04-17T08:21:08.301Z", "customerNumber": 3500667, "username": "kollthom", "contractId": 55991985, "inspectionHistoryName": "<PERSON>", "inspectionHistoryDate": "2025-04-17T08:21:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "110470", "inspectionHistoryPlaceName": "", "inspectionHistoryContact": "c5126529-bf87-459a-b765-f8d24713b457", "inspectionHistoryContactName": "Dummy", "alternativeContactField": "", "salutation": "<PERSON>", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 41, "createdAt": "2025-04-17T09:53:03.358Z", "updatedAt": "2025-04-17T09:53:03.358Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 55912262, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-17T09:52:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "109801", "inspectionHistoryPlaceName": "", "inspectionHistoryContact": "8c8a7f12-eae0-4a98-9f84-0d97135ca2a4", "inspectionHistoryContactName": "<PERSON>", "alternativeContactField": "", "salutation": "<PERSON>", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 42, "createdAt": "2025-04-17T09:55:05.170Z", "updatedAt": "2025-04-17T09:55:05.170Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 55912262, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-17T09:55:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "109801", "inspectionHistoryPlaceName": "", "inspectionHistoryContact": "8c8a7f12-eae0-4a98-9f84-0d97135ca2a4", "inspectionHistoryContactName": "<PERSON>", "alternativeContactField": "", "salutation": "<PERSON>", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 43, "createdAt": "2025-04-17T10:46:33.100Z", "updatedAt": "2025-04-17T10:46:33.100Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56003622, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-17T10:46:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 44, "createdAt": "2025-04-17T10:59:50.404Z", "updatedAt": "2025-04-17T10:59:50.404Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56004487, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-17T10:59:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 45, "createdAt": "2025-04-17T12:57:18.280Z", "updatedAt": "2025-04-17T12:57:18.280Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56012401, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-17T12:57:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "", "inspectionHistoryPlaceName": "", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 46, "createdAt": "2025-04-17T12:59:24.069Z", "updatedAt": "2025-04-17T12:59:24.069Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56012553, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-17T12:59:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "faber test", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 47, "createdAt": "2025-04-17T13:05:03.604Z", "updatedAt": "2025-04-17T13:05:03.604Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56013065, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-17T13:05:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "", "inspectionHistoryPlaceName": "myClaim Client, A-1230, Wien, Forchheimergasse", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 48, "createdAt": "2025-04-17T13:07:11.977Z", "updatedAt": "2025-04-17T13:07:11.977Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56013267, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-17T13:07:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "", "inspectionHistoryPlaceName": "myClaim <PERSON>, A-1230, Wien", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 49, "createdAt": "2025-04-17T23:59:20.407Z", "updatedAt": "2025-04-17T23:59:20.407Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56025654, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-17T23:58:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "myClaim Client, A-1230, Wien, Forchheimergasse", "inspectionHistoryContact": "2411bcbb-707e-4286-b2be-0dd19fa8a425", "inspectionHistoryContactName": "<PERSON>", "alternativeContactField": "", "salutation": "<PERSON><PERSON>", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 50, "createdAt": "2025-04-18T00:09:38.444Z", "updatedAt": "2025-04-18T00:09:38.444Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56025654, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T00:09:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "myClaim Client, A-1230, Wien, Forchheimergasse", "inspectionHistoryContact": "2411bcbb-707e-4286-b2be-0dd19fa8a425", "inspectionHistoryContactName": "<PERSON>", "alternativeContactField": "", "salutation": "<PERSON><PERSON>", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 51, "createdAt": "2025-04-18T00:42:51.718Z", "updatedAt": "2025-04-18T00:42:51.718Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56025761, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T00:42:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "myClaim Client, A-1230, Wien, Forchheimergasse", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 53, "createdAt": "2025-04-18T00:46:19.328Z", "updatedAt": "2025-04-18T00:46:19.328Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56025761, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T00:44:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 52, "createdAt": "2025-04-18T00:44:39.354Z", "updatedAt": "2025-04-18T00:44:39.354Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56025761, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T00:44:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "myClaim Client, A-1230, Wien, Forchheimergasse", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 54, "createdAt": "2025-04-18T00:51:25.329Z", "updatedAt": "2025-04-18T00:51:25.329Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56025796, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T00:51:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "myClaim Client, A-1230, Wien, Forchheimergasse", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 56, "createdAt": "2025-04-18T00:56:04.579Z", "updatedAt": "2025-04-18T00:56:04.579Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56025796, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T00:55:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 55, "createdAt": "2025-04-18T00:55:19.246Z", "updatedAt": "2025-04-18T00:55:19.246Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56025796, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T00:55:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "myClaim <PERSON>, Forchheimergasse, A-1230, Wien", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 57, "createdAt": "2025-04-18T01:49:21.959Z", "updatedAt": "2025-04-18T01:49:21.959Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56025885, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T01:49:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "empty", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 58, "createdAt": "2025-04-18T01:57:07.083Z", "updatedAt": "2025-04-18T01:57:07.083Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56025885, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T01:56:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "unsuccessful", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 59, "createdAt": "2025-04-18T04:59:52.765Z", "updatedAt": "2025-04-18T04:59:52.765Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 53594486, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T04:59:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "100591", "inspectionHistoryPlaceName": "Eder GmbH Hans Vertragshändler und Werkstätte, An der Trauner Kreuzung 6, 4061, <PERSON><PERSON><PERSON>", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 60, "createdAt": "2025-04-18T05:00:24.156Z", "updatedAt": "2025-04-18T05:00:24.156Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 53594486, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T05:00:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "109825", "inspectionHistoryPlaceName": "Porsche Inter Auto GmbH & Co KG Zweigniederlassung Porsche Wien Liesing, Ketzergasse 120, 1230, Wien", "inspectionHistoryContact": "e2c7d828-1b43-4adf-b8d9-022bcb9322be", "inspectionHistoryContactName": "<PERSON>", "alternativeContactField": "", "salutation": "<PERSON>", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 61, "createdAt": "2025-04-18T05:05:54.985Z", "updatedAt": "2025-04-18T05:05:54.985Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56027213, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T05:05:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "105050", "inspectionHistoryPlaceName": "Zitta Ges.m.b.H. & CO KG, Mühlgasse 82, 2380, <PERSON><PERSON><PERSON>sdorf", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "open", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 62, "createdAt": "2025-04-18T05:07:13.430Z", "updatedAt": "2025-04-18T05:07:13.430Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 46199493, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T05:06:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "110470", "inspectionHistoryPlaceName": "Figl GmbH Markus VW Audi, Hauptstr. 121, 3021, <PERSON><PERSON><PERSON>", "inspectionHistoryContact": "empty", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 63, "createdAt": "2025-04-18T05:10:18.396Z", "updatedAt": "2025-04-18T05:10:18.396Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56027227, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T05:10:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "102474", "inspectionHistoryPlaceName": "Liewers Kraftfahrzeughandel- und Liegenschaftsverwaltung AG, Triester Str. 87, 1102, Wien", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 64, "createdAt": "2025-04-18T09:48:40.255Z", "updatedAt": "2025-04-18T09:48:40.255Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56027213, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-18T09:48:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "999999", "inspectionHistoryPlaceName": "Fotoexpertise", "inspectionHistoryContact": "empty", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "Fotoexpertise", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 85, "createdAt": "2025-05-19T13:37:42.710Z", "updatedAt": "2025-05-19T13:39:22.287Z", "customerNumber": 3500667, "username": "tuceguen", "contractId": 56042840, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-19T08:59:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "110845", "inspectionHistoryPlaceName": "Fior GmbH Autohaus, Kärntner Str. 67-73, 8020, Graz", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "F<PERSON>rzeugh<PERSON>ter", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "video", "inspectionHistoryCondition": "assembled", "additionalValues": {"licenseNumber": "G 407XP", "txtLossNumber": "25-000858545-G-530", "txtInsuranceNumber": "", "inspectionHistoryButton": ""}}, {"id": 65, "createdAt": "2025-04-21T12:59:41.121Z", "updatedAt": "2025-04-21T13:00:39.611Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56027227, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-21T12:59:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "102474", "inspectionHistoryPlaceName": "Liewers Kraftfahrzeughandel- und Liegenschaftsverwaltung AG, Triester Str. 87, 1102, Wien", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 66, "createdAt": "2025-04-21T13:00:01.276Z", "updatedAt": "2025-04-21T13:00:39.611Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56027227, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-21T12:59:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "102474", "inspectionHistoryPlaceName": "Liewers Kraftfahrzeughandel- und Liegenschaftsverwaltung AG, Triester Str. 87, 1102, Wien", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 67, "createdAt": "2025-04-25T07:22:24.185Z", "updatedAt": "2025-04-25T07:22:24.185Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56216922, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-25T07:22:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "102474", "inspectionHistoryPlaceName": "Liewers Kraftfahrzeughandel- und Liegenschaftsverwaltung AG, Triester Str. 87, 1102, Wien", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 68, "createdAt": "2025-04-28T14:24:59.989Z", "updatedAt": "2025-04-28T14:24:59.989Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56251102, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-28T14:24:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "myClaim <PERSON>, Forchheimergasse, A-1230, Wien", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 69, "createdAt": "2025-04-30T13:09:38.075Z", "updatedAt": "2025-04-30T13:09:38.075Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56251102, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-30T13:09:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "3500667", "inspectionHistoryPlaceName": "myClaim <PERSON>, Forchheimergasse, A-1230, Wien", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 70, "createdAt": "2025-04-30T13:23:18.029Z", "updatedAt": "2025-04-30T13:23:18.029Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 41831975, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-30T13:23:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "104986", "inspectionHistoryPlaceName": "<PERSON><PERSON><PERSON> Ges.m.b.H. <PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Grazer Vorstadt 120, 8570, <PERSON><PERSON><PERSON>", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 71, "createdAt": "2025-04-30T16:22:11.626Z", "updatedAt": "2025-04-30T16:22:11.626Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56294791, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-04-30T16:22:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "102474", "inspectionHistoryPlaceName": "Liewers Kraftfahrzeughandel- und Liegenschaftsverwaltung AG, Triester Str. 87, 1102, Wien", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}, {"id": 72, "createdAt": "2025-05-06T01:52:53.537Z", "updatedAt": "2025-05-06T01:54:46.729Z", "customerNumber": 3500667, "username": "wedatdemo", "contractId": 56294791, "inspectionHistoryName": "we DAT", "inspectionHistoryDate": "2025-05-06T01:52:00.000Z", "repairer_id": "", "inspectionHistoryPlace": "102902", "inspectionHistoryPlaceName": "Narowetz GmbH VW-Audi-Kundendienst und Verkauf, Pechhüttenbrunnengasse 4 - 10, 2345, Brunn am Gebirge", "inspectionHistoryContact": "", "inspectionHistoryContactName": "", "alternativeContactField": "", "salutation": "", "inspectionHistoryStatus": "done", "inspectionHistoryInspection": "onSite", "inspectionHistoryCondition": "assembled", "additionalValues": {"inspectionHistoryButton": ""}}], "total": 104}