{"name": "dat-backend", "version": "0.0.2", "private": true, "license": "UNLICENSED", "scripts": {"restore-db-damp": "node scripts/restore_db_damp.js", "create-db-dump": "node scripts/create_db_dump.js", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "run-my-script": "ts-node ./src/tasks/downloadTranslations.ts", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "node --max-old-space-size=8192 ./node_modules/jest/bin/jest.js --maxWorkers=50%", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/.bin/typeorm", "typeorm:win": "ts-node --esm ./node_modules/typeorm/cli.js", "typeorm:migrations:sync": "npm run typeorm -- schema:sync -d src/config/migration.config.ts", "typeorm:migrations:show": "npm run typeorm -- migration:show -d src/config/migration.config.ts", "typeorm:migrations:run": "npm run typeorm migration:run -- -d src/config/migration.config.ts", "typeorm:migrations:generate": "npm run typeorm -- migration:generate -d src/config/migration.config.ts", "typeorm:migrations:revert": "npm run typeorm -- -d src/config/migration.config.ts migration:revert"}, "watch": {"ignore": "files"}, "dependencies": {"@aws-sdk/client-s3": "^3.454.0", "@azure/identity": "^4.10.0", "@googlemaps/google-maps-services-js": "^3.4.0", "@nestjs/axios": "^2.0.0", "@nestjs/cache-manager": "^2.2.2", "@nestjs/cli": "^9.2.0", "@nestjs/common": "^9.3.9", "@nestjs/core": "^9.3.9", "@nestjs/jwt": "^10.1.0", "@nestjs/platform-express": "^9.3.9", "@nestjs/schedule": "^2.1.0", "@nestjs/swagger": "^6.2.1", "@nestjs/terminus": "10", "@nestjs/typeorm": "^9.0.1", "adm-zip": "^0.5.16", "api2dat5": "^1.2.2", "archiver": "^6.0.1", "aws-sdk": "^2.1333.0", "axios": "^1.3.4", "bcrypt": "^5.1.1", "cache-manager": "^5.1.7", "cheerio": "^1.0.0-rc.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.8.0", "csv-parser": "^3.0.0", "date-fns": "^2.29.3", "date-holidays": "^3.24.3", "deep-object-diff": "^1.1.9", "dotenv": "^16.0.0", "fast-xml-parser": "^3.17.6", "handlebars": "^4.7.8", "hjson": "^3.2.2", "html-to-pdfmake": "^2.5.23", "html-to-text": "^9.0.5", "image-size": "^1.1.1", "jest": "^29.5.0", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.0", "jszip": "^3.10.1", "lodash.merge": "^4.6.2", "luxon": "^3.5.0", "nestjs-spelunker": "^1.1.5", "nodemailer": "^6.9.14", "openai": "^4.67.1", "papaparse": "^5.4.0", "pdf-lib": "^1.17.1", "pdfkit": "^0.14.0", "pdfmake": "^0.2.18", "pg": "^8.10.0", "prom-client": "^15.1.0", "qs": "^6.11.1", "queue": "^6.0.2", "ramda": "^0.28.0", "raw-body": "^3.0.0", "rxjs": "^7.8.0", "sharp": "^0.33.5", "stream-array": "^1.1.2", "stream-chain": "^2.2.5", "stream-json": "^1.8.0", "swagger-ui-express": "^4.6.2", "typeorm": "^0.3.12", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/schematics": "^9.0.4", "@nestjs/testing": "^9.3.9", "@types/adm-zip": "^0.5.0", "@types/archiver": "^6.0.2", "@types/cache-manager": "^4.0.2", "@types/cheerio": "^0.22.35", "@types/compression": "^1", "@types/express": "^4.17.21", "@types/handlebars": "^4.1.0", "@types/hjson": "^2.4.3", "@types/html-to-pdfmake": "^2.4.4", "@types/html-to-text": "^9", "@types/jest": "^29.5.12", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.1", "@types/lodash": "^4.14.167", "@types/luxon": "^3", "@types/multer": "^1.4.11", "@types/node": "^18.15.0", "@types/papaparse": "^5.3.2", "@types/pdfkit": "^0.13.4", "@types/pdfmake": "^0", "@types/ramda": "^0.28.23", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.54.1", "eslint": "^8.35.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.4", "supertest": "^6.3.3", "ts-jest": "^29.0.5", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^4.9.5"}, "packageManager": "yarn@4.6.0"}